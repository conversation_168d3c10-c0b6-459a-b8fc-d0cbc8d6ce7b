
import React from 'react';
import { motion } from 'framer-motion';
import { Leaf, ShieldAlert, BarChart3, Building, Globe, MapPin, CloudRain, Droplet, AlertTriangle, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const servicesList = [
  { id: "climate-smart-agriculture", name: "Climate-Smart Agriculture", icon: <Leaf className="h-10 w-10" />, description: "Enhancing agricultural resilience and productivity through advanced geospatial data analysis, precision farming techniques, and climate modeling. We help optimize resource use, improve crop yields, and promote sustainable farming practices in the face of climate change.", image: "climate_smart_agriculture_detailed.jpg", alt: "Drone flying over a lush farm field for data collection" },
  { id: "natural-risk-assessments", name: "Natural Risk Assessments", icon: <ShieldAlert className="h-10 w-10" />, description: "Identifying, analyzing, and mitigating natural hazard risks such as floods, landslides, and droughts. Our assessments provide crucial information for disaster preparedness, land-use planning, and infrastructure development to build safer, more resilient communities.", image: "natural_risk_assessment_map.jpg", alt: "Geospatial map highlighting areas of natural risk" },
  { id: "spatial-data-analytics", name: "Spatial Data Analytics", icon: <BarChart3 className="h-10 w-10" />, description: "Transforming complex geographic data into actionable intelligence. We employ sophisticated analytical methods, machine learning, and AI to uncover patterns, trends, and insights that support strategic decision-making across various sectors.", image: "spatial_data_analytics_dashboard.jpg", alt: "Computer screen showing complex spatial data visualizations" },
  { id: "urban-planning", name: "Urban Planning", icon: <Building className="h-10 w-10" />, description: "Supporting the development of sustainable, efficient, and livable urban environments. Our services include land suitability analysis, infrastructure planning, population growth modeling, and smart city solutions to guide urban development.", image: "urban_planning_blueprint.jpg", alt: "Digital blueprint of a modern city plan" },
  { id: "environmental-monitoring", name: "Environmental Monitoring", icon: <Globe className="h-10 w-10" />, description: "Utilizing satellite imagery, remote sensing, and GIS to track environmental changes, monitor deforestation, assess biodiversity, and manage protected areas. We provide data-driven insights for effective environmental conservation and policy-making.", image: "satellite_image_environmental_change.jpg", alt: "Satellite image showing changes in land cover over time" },
  { id: "natural-resources-management", name: "Natural Resources Management", icon: <MapPin className="h-10 w-10" />, description: "Optimizing the sustainable use and conservation of natural resources like forests, minerals, and fisheries. Our solutions help balance economic development with environmental protection through detailed resource mapping and monitoring.", image: "natural_resources_map_layers.jpg", alt: "Layered map showing different natural resources in a region" },
  { id: "climate-change-adaptation", name: "Climate Change Adaptation", icon: <CloudRain className="h-10 w-10" />, description: "Developing and implementing strategies to help communities and ecosystems adapt to the impacts of climate change. This includes vulnerability assessments, adaptation planning, and monitoring the effectiveness of adaptation measures.", image: "climate_adaptation_strategy_infographic.jpg", alt: "Infographic illustrating climate change adaptation strategies" },
  { id: "water-resources-management", name: "Water Resources Management", icon: <Droplet className="h-10 w-10" />, description: "Ensuring the sustainable management and equitable distribution of water resources. We offer services in watershed analysis, groundwater mapping, irrigation optimization, and flood management to address water scarcity and quality issues.", image: "water_resources_system_diagram.jpg", alt: "Diagram showing a comprehensive water resource management system" },
  { id: "disaster-risk-management", name: "Disaster Risk Management", icon: <AlertTriangle className="h-10 w-10" />, description: "Strengthening preparedness, response, and recovery capabilities for disaster events. Our services include early warning systems, damage assessment, evacuation planning, and post-disaster needs analysis using geospatial technologies.", image: "disaster_response_command_center.jpg", alt: "Emergency response team using maps in a command center" },
];


const ServicesPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Our Geospatial Services
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Delivering comprehensive solutions to address complex environmental and developmental challenges.
          </motion.p>
        </div>
      </section>

      {/* Services List Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="space-y-16">
            {servicesList.map((service, index) => (
              <motion.div
                key={service.id}
                id={service.id}
                className="grid md:grid-cols-2 gap-8 md:gap-12 items-center"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className={`md:order-${index % 2 === 0 ? 1 : 2}`}>
                  <img  
                    src={service.image} 
                    alt={service.alt} 
                    className="rounded-lg shadow-xl w-full h-auto object-cover max-h-[400px]"
                   src="https://images.unsplash.com/photo-1599472696777-95cab5e0f891" />
                </div>
                <div className={`md:order-${index % 2 === 0 ? 2 : 1}`}>
                  <div className="flex items-center mb-4">
                    <span className="p-3 rounded-full bg-primary/10 text-primary mr-4">
                      {React.cloneElement(service.icon, { className: "h-8 w-8" })}
                    </span>
                    <h2 className="text-2xl lg:text-3xl font-jost font-semibold text-primary">{service.name}</h2>
                  </div>
                  <p className="text-foreground/80 mb-6 leading-relaxed">{service.description}</p>
                  <Button asChild variant="default" className="font-jost">
                    <Link to={`/quote?service=${encodeURIComponent(service.name)}`}>
                      Request Quote for {service.name} <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Have a Specific Challenge?
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            Our team is ready to discuss your unique requirements and develop a tailored geospatial solution.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button size="lg" asChild className="font-jost">
              <Link to="/contact">Contact Us</Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="font-jost border-primary text-primary hover:bg-primary/10">
              <Link to="/projects">See Our Work</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
