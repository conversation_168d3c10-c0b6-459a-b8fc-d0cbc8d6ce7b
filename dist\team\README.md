# Team Profile Pictures

Add team member profile pictures to this folder with the following naming convention:

## Required Images:

1. `pad<PERSON>-<PERSON><PERSON><PERSON>-jas<PERSON>.jpg` - PADONA MATABA<PERSON> Jason (CEO)
2. `amani-in<PERSON>-maguru.jpg` - <PERSON><PERSON> (Research Assistant)
3. `ushindi-gab<PERSON>.jpg` - <PERSON><PERSON><PERSON> (CTO)
4. `<PERSON>uza-mpuranyi-judith.jpg` - <PERSON><PERSON> (GIS Specialist)
5. `jordan-bi<PERSON><PERSON>.jpg` - <PERSON> (Architect)
6. `mush<PERSON><PERSON>a-namegabe-alain.jpg` - Mushagalusa Namega<PERSON> (Environmental Data Scientist)
7. `cubaka-bahozi-rigobert.jpg` - <PERSON><PERSON> (Remote Sensing Analyst)
8. `gaelle-baraka.jpg` - <PERSON><PERSON><PERSON> (Human Resource Manager)
9. `moise-rap<PERSON><PERSON>.jpg` - <PERSON><PERSON> (Senior Risk Assessment Expert)

## Image Specifications:
- **Format:** JPG, PNG, or WebP
- **Size:** 400x400 pixels (square) recommended
- **File size:** Under 500KB each for optimal loading
- **Quality:** High resolution for crisp display

## Alternative Naming:
You can also use:
- `.png` extension instead of `.jpg`
- <PERSON> naming (just update the code accordingly)
