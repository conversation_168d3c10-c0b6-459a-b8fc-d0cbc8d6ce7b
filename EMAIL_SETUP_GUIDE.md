# Email Integration Setup Guide for GEOSTRATDRC Website

## 📧 How to Receive Emails from Your Website

Your website is now configured to send emails to **<EMAIL>** when visitors:
- Submit contact forms
- Request quotes
- Subscribe to newsletter

## 🔧 EmailJS Setup (Required)

To activate email functionality, you need to set up EmailJS:

### Step 1: Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Create Email Service
1. In EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail recommended)
4. Connect your Gmail account (<EMAIL>)
5. Note the **Service ID** (e.g., "service_geostrat")

### Step 3: Create Email Templates

#### Template 1: Contact Form
1. Go to "Email Templates" → "Create New Template"
2. Template ID: `template_contact`
3. Template content:
```
Subject: New Contact Form Submission - {{subject}}

From: {{from_name}} ({{from_email}})
Phone: {{phone}}
Company: {{company}}

Message:
{{message}}

Submitted: {{submission_date}}
Website: {{website}}
```

#### Template 2: Quote Request
1. Create another template
2. Template ID: `template_quote`
3. Template content:
```
Subject: Quote Request - {{project_title}}

CONTACT INFORMATION:
Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Company: {{company}}

PROJECT DETAILS:
Title: {{project_title}}
Description: {{project_description}}
Services: {{services}}
Budget: {{budget}}
Timeline: {{timeline}}

Message:
{{message}}

Submitted: {{submission_date}}
Website: {{website}}
```

#### Template 3: Newsletter Subscription
1. Create third template
2. Template ID: `template_newsletter`
3. Template content:
```
Subject: New Newsletter Subscription

New newsletter subscription from: {{from_email}}

Submitted: {{submission_date}}
Website: {{website}}
```

### Step 4: Get Public Key
1. Go to "Account" → "General"
2. Copy your **Public Key**

### Step 5: Update Configuration
1. Open `src/lib/emailService.js`
2. Replace the placeholder values:
```javascript
const EMAILJS_CONFIG = {
  serviceId: 'your_service_id_here', // From Step 2
  templateId: 'template_contact',    // From Step 3
  publicKey: 'your_public_key_here', // From Step 4
  recipientEmail: '<EMAIL>'
};
```

## 🧪 Testing Email Functionality

### Test Contact Form:
1. Go to `/contact` page
2. Fill out and submit the form
3. Check <EMAIL> for email

### Test Quote Request:
1. Go to `/quote` page
2. Fill out and submit the form
3. Check <EMAIL> for email

### Test Newsletter:
1. Scroll to footer on any page
2. Enter email and subscribe
3. Check <EMAIL> for email

## 📱 Alternative Options

### Option 1: Formspree (Simpler)
1. Go to [https://formspree.io/](https://formspree.io/)
2. Create account and get form endpoint
3. Update forms to POST to Formspree endpoint

### Option 2: Netlify Forms (If hosting on Netlify)
1. Add `netlify` attribute to forms
2. Forms automatically work on Netlify

### Option 3: Backend Service
1. Set up Node.js/Express backend
2. Use Nodemailer for email sending
3. Deploy to Heroku/Vercel

## 🔒 Security Notes

- EmailJS public key is safe to expose in frontend
- Never expose private keys or passwords
- Consider rate limiting for production
- Monitor EmailJS usage limits

## 📊 Email Limits

**EmailJS Free Plan:**
- 200 emails/month
- 2 email services
- Basic templates

**Upgrade if needed:**
- More emails per month
- Advanced features
- Better support

## 🆘 Troubleshooting

### Emails not sending:
1. Check browser console for errors
2. Verify EmailJS configuration
3. Check EmailJS dashboard for failed sends
4. Ensure templates exist and match IDs

### Emails going to spam:
1. Add your domain to EmailJS
2. Set up proper email authentication
3. Use professional email content

## 📞 Support

If you need help setting this up:
1. Check EmailJS documentation
2. Test with simple examples first
3. Verify all configuration values match

Your website will automatically send emails to **<EMAIL>** once EmailJS is properly configured!
