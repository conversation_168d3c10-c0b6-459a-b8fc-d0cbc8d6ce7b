import{c as N,r as m,b as W,g as K,f as O,j as e,P as R,h as P,i as G,l as Y,R as V,p as T,u as Z,z as ee,t as te,m as F,A as se,I as j,y as ae,a as re,B as oe,C as ne}from"./index-2bfcaeaf.js";import{L as y,T as M}from"./label-362760fe.js";import{C as ie,a as ce,b as le,d as de,c as me}from"./card-3197beef.js";import{U as ue}from"./user-d70188e6.js";const pe=N("CalendarDays",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]]),he=N("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),xe=N("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),fe=N("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function ye(t){const o=m.useRef({value:t,previous:t});return m.useMemo(()=>(o.current.value!==t&&(o.current.previous=o.current.value,o.current.value=t),o.current.previous),[t])}function be(t){const[o,n]=m.useState(void 0);return W(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});const d=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const a=s[0];let p,i;if("borderBoxSize"in a){const u=a.borderBoxSize,r=Array.isArray(u)?u[0]:u;p=r.inlineSize,i=r.blockSize}else p=t.offsetWidth,i=t.offsetHeight;n({width:p,height:i})});return d.observe(t,{box:"border-box"}),()=>d.unobserve(t)}else n(void 0)},[t]),o}var S="Checkbox",[ge,we]=K(S),[je,I]=ge(S);function ve(t){const{__scopeCheckbox:o,checked:n,children:d,defaultChecked:s,disabled:a,form:p,name:i,onCheckedChange:u,required:r,value:c="on",internal_do_not_use_render:l}=t,[h,k]=Y({prop:n,defaultProp:s??!1,onChange:u,caller:S}),[g,v]=m.useState(null),[C,x]=m.useState(null),f=m.useRef(!1),q=g?!!p||!!g.closest("form"):!0,w={checked:h,disabled:a,setChecked:k,control:g,setControl:v,name:i,form:p,value:c,hasConsumerStoppedPropagationRef:f,required:r,defaultChecked:b(s)?!1:s,isFormControl:q,bubbleInput:C,setBubbleInput:x};return e.jsx(je,{scope:o,...w,children:ke(l)?l(w):d})}var _="CheckboxTrigger",A=m.forwardRef(({__scopeCheckbox:t,onKeyDown:o,onClick:n,...d},s)=>{const{control:a,value:p,disabled:i,checked:u,required:r,setControl:c,setChecked:l,hasConsumerStoppedPropagationRef:h,isFormControl:k,bubbleInput:g}=I(_,t),v=O(s,c),C=m.useRef(u);return m.useEffect(()=>{const x=a==null?void 0:a.form;if(x){const f=()=>l(C.current);return x.addEventListener("reset",f),()=>x.removeEventListener("reset",f)}},[a,l]),e.jsx(R.button,{type:"button",role:"checkbox","aria-checked":b(u)?"mixed":u,"aria-required":r,"data-state":H(u),"data-disabled":i?"":void 0,disabled:i,value:p,...d,ref:v,onKeyDown:P(o,x=>{x.key==="Enter"&&x.preventDefault()}),onClick:P(n,x=>{l(f=>b(f)?!0:!f),g&&k&&(h.current=x.isPropagationStopped(),h.current||x.stopPropagation())})})});A.displayName=_;var D=m.forwardRef((t,o)=>{const{__scopeCheckbox:n,name:d,checked:s,defaultChecked:a,required:p,disabled:i,value:u,onCheckedChange:r,form:c,...l}=t;return e.jsx(ve,{__scopeCheckbox:n,checked:s,defaultChecked:a,disabled:i,required:p,onCheckedChange:r,name:d,form:c,value:u,internal_do_not_use_render:({isFormControl:h})=>e.jsxs(e.Fragment,{children:[e.jsx(A,{...l,ref:o,__scopeCheckbox:n}),h&&e.jsx(X,{__scopeCheckbox:n})]})})});D.displayName=S;var z="CheckboxIndicator",L=m.forwardRef((t,o)=>{const{__scopeCheckbox:n,forceMount:d,...s}=t,a=I(z,n);return e.jsx(G,{present:d||b(a.checked)||a.checked===!0,children:e.jsx(R.span,{"data-state":H(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:o,style:{pointerEvents:"none",...t.style}})})});L.displayName=z;var Q="CheckboxBubbleInput",X=m.forwardRef(({__scopeCheckbox:t,...o},n)=>{const{control:d,hasConsumerStoppedPropagationRef:s,checked:a,defaultChecked:p,required:i,disabled:u,name:r,value:c,form:l,bubbleInput:h,setBubbleInput:k}=I(Q,t),g=O(n,k),v=ye(a),C=be(d);m.useEffect(()=>{const f=h;if(!f)return;const q=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor(q,"checked").set,$=!s.current;if(v!==a&&E){const J=new Event("click",{bubbles:$});f.indeterminate=b(a),E.call(f,b(a)?!1:a),f.dispatchEvent(J)}},[h,v,a,s]);const x=m.useRef(b(a)?!1:a);return e.jsx(R.input,{type:"checkbox","aria-hidden":!0,defaultChecked:p??x.current,required:i,disabled:u,name:r,value:c,form:l,...o,tabIndex:-1,ref:g,style:{...o.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});X.displayName=Q;function ke(t){return typeof t=="function"}function b(t){return t==="indeterminate"}function H(t){return b(t)?"indeterminate":t?"checked":"unchecked"}const U=V.forwardRef(({className:t,...o},n)=>e.jsx(D,{ref:n,className:T("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...o,children:e.jsx(L,{className:T("flex items-center justify-center text-current"),children:e.jsx(he,{className:"h-4 w-4"})})}));U.displayName=D.displayName;const B=["Climate-Smart Agriculture","Natural Risk Assessments","Spatial Data Analytics","Urban Planning","Environmental Monitoring","Natural Resources Management","Climate Change Adaptation","Water Resources Management","Disaster Risk Management","Other"],Re=()=>{const{toast:t}=Z(),o=ee(),[n,d]=m.useState([]),[s,a]=m.useState({name:"",email:"",phone:"",company:"",projectTitle:"",projectDescription:"",estimatedBudget:"",desiredTimeline:"",additionalInfo:""});m.useEffect(()=>{te();const r=new URLSearchParams(o.search),c=r.get("service");c&&B.includes(c)&&d([c]);const l=r.get("project");l&&a(h=>({...h,projectTitle:`Inquiry about: ${l}`}))},[o.search]);const p=r=>{d(c=>c.includes(r)?c.filter(l=>l!==r):[...c,r])},i=r=>{const{name:c,value:l}=r.target;a(h=>({...h,[c]:l}))},u=async r=>{r.preventDefault();const c={...s,services:n,submissionDate:new Date().toISOString()};console.log("Detailed Quote Request Data:",c);try{const l=JSON.parse(localStorage.getItem("detailedQuoteRequests"))||[];if(localStorage.setItem("detailedQuoteRequests",JSON.stringify([...l,c])),(await ne(c)).success)t({title:"Quote Request Submitted Successfully!",description:"Thank you! Our team will review your request and contact you <NAME_EMAIL>.",variant:"default"}),a({name:"",email:"",phone:"",company:"",projectTitle:"",projectDescription:"",estimatedBudget:"",desiredTimeline:"",additionalInfo:""}),d([]);else throw new Error("Email sending failed")}catch(l){console.error("Failed to send quote request:",l),t({title:"Quote Saved Locally",description:"Your quote request was saved but email notification failed. We'll still review your request.",variant:"default"}),a({name:"",email:"",phone:"",company:"",projectTitle:"",projectDescription:"",estimatedBudget:"",desiredTimeline:"",additionalInfo:""}),d([])}};return e.jsxs("div",{className:"animate-fade-in font-sans",children:[e.jsxs("section",{className:"relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground",children:[e.jsx("div",{className:"absolute inset-0 bg-black/30"}),e.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[e.jsx(F.h1,{className:"text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:"Request a Detailed Quote"}),e.jsx(F.p,{className:"text-lg md:text-xl max-w-3xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"Provide us with details about your project, and we'll tailor a comprehensive proposal to meet your specific needs."})]})]}),e.jsx("section",{className:"py-16 lg:py-24",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs(ie,{className:"max-w-4xl mx-auto p-6 sm:p-8 md:p-10 shadow-2xl bg-background",children:[e.jsxs(ce,{className:"p-0 mb-8 text-center",children:[e.jsx(se,{className:"h-12 w-12 text-primary mx-auto mb-3"}),e.jsx(le,{className:"font-jost text-3xl text-primary",children:"Project Details Form"}),e.jsx(de,{className:"text-foreground/70 mt-1",children:"The more information you provide, the more accurate our quote will be."})]}),e.jsx(me,{className:"p-0",children:e.jsxs("form",{onSubmit:u,className:"space-y-8",children:[e.jsxs("fieldset",{className:"space-y-6 border p-4 rounded-md border-border",children:[e.jsxs("legend",{className:"text-xl font-jost font-semibold text-primary px-2 flex items-center",children:[e.jsx(ue,{className:"h-5 w-5 mr-2"}),"Contact Information"]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-name",children:["Full Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(j,{id:"quote-name",name:"name",value:s.name,onChange:i,type:"text",placeholder:"e.g., John Doe",required:!0,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-email",children:["Email Address ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(j,{id:"quote-email",name:"email",value:s.email,onChange:i,type:"email",placeholder:"e.g., <EMAIL>",required:!0,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"quote-phone",children:"Phone Number"}),e.jsx(j,{id:"quote-phone",name:"phone",value:s.phone,onChange:i,type:"tel",placeholder:"e.g., +243 XXX XXX XXX",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"quote-company",children:"Company/Organization (Optional)"}),e.jsx(j,{id:"quote-company",name:"company",value:s.company,onChange:i,type:"text",placeholder:"e.g., Example Corp",className:"mt-1"})]})]})]}),e.jsxs("fieldset",{className:"space-y-4 border p-4 rounded-md border-border",children:[e.jsxs("legend",{className:"text-xl font-jost font-semibold text-primary px-2 flex items-center",children:[e.jsx(ae,{className:"h-5 w-5 mr-2"}),"Services of Interest ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-4",children:B.map(r=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(U,{id:`service-${r.toLowerCase().replace(/\s+/g,"-")}`,checked:n.includes(r),onCheckedChange:()=>p(r)}),e.jsx(y,{htmlFor:`service-${r.toLowerCase().replace(/\s+/g,"-")}`,className:"font-normal text-sm cursor-pointer",children:r})]},r))}),n.length===0&&e.jsx("p",{className:"text-sm text-destructive",children:"Please select at least one service."})]}),e.jsxs("fieldset",{className:"space-y-6 border p-4 rounded-md border-border",children:[e.jsxs("legend",{className:"text-xl font-jost font-semibold text-primary px-2 flex items-center",children:[e.jsx(re,{className:"h-5 w-5 mr-2"}),"Project Details"]}),e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-project-title",children:["Project Title/Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(j,{id:"quote-project-title",name:"projectTitle",value:s.projectTitle,onChange:i,type:"text",placeholder:"e.g., Kinshasa Urban Resilience Plan",required:!0,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-project-description",children:["Project Description & Objectives ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(M,{id:"quote-project-description",name:"projectDescription",value:s.projectDescription,onChange:i,placeholder:"Describe your project, its goals, scope, and any specific challenges...",required:!0,rows:5,className:"mt-1"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-budget",className:"flex items-center",children:[e.jsx(xe,{className:"h-4 w-4 mr-1 text-muted-foreground"}),"Estimated Budget (Optional)"]}),e.jsx(j,{id:"quote-budget",name:"estimatedBudget",value:s.estimatedBudget,onChange:i,type:"text",placeholder:"e.g., $5,000 - $10,000 USD or N/A",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsxs(y,{htmlFor:"quote-timeline",className:"flex items-center",children:[e.jsx(pe,{className:"h-4 w-4 mr-1 text-muted-foreground"}),"Desired Timeline (Optional)"]}),e.jsx(j,{id:"quote-timeline",name:"desiredTimeline",value:s.desiredTimeline,onChange:i,type:"text",placeholder:"e.g., 3-6 months, or specific deadline",className:"mt-1"})]})]})]}),e.jsxs("fieldset",{className:"space-y-6 border p-4 rounded-md border-border",children:[e.jsxs("legend",{className:"text-xl font-jost font-semibold text-primary px-2 flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 mr-2"}),"Additional Information"]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"quote-additional-info",children:"Any Other Relevant Information (Optional)"}),e.jsx(M,{id:"quote-additional-info",name:"additionalInfo",value:s.additionalInfo,onChange:i,placeholder:"Include any existing data, specific requirements, or questions you have...",rows:3,className:"mt-1"})]})]}),e.jsx(oe,{type:"submit",size:"lg",className:"w-full font-jost text-lg py-3",disabled:n.length===0||!s.name||!s.email||!s.projectTitle||!s.projectDescription,children:"Submit Detailed Quote Request"})]})})]})})})]})};export{Re as default};
