function Ov(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function Wf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Hf={exports:{}},ls={},Gf={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lo=Symbol.for("react.element"),Iv=Symbol.for("react.portal"),Fv=Symbol.for("react.fragment"),zv=Symbol.for("react.strict_mode"),Bv=Symbol.for("react.profiler"),Uv=Symbol.for("react.provider"),$v=Symbol.for("react.context"),Wv=Symbol.for("react.forward_ref"),Hv=Symbol.for("react.suspense"),Gv=Symbol.for("react.memo"),Kv=Symbol.for("react.lazy"),cc=Symbol.iterator;function Qv(e){return e===null||typeof e!="object"?null:(e=cc&&e[cc]||e["@@iterator"],typeof e=="function"?e:null)}var Kf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Qf=Object.assign,Yf={};function Pr(e,t,n){this.props=e,this.context=t,this.refs=Yf,this.updater=n||Kf}Pr.prototype.isReactComponent={};Pr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Pr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Xf(){}Xf.prototype=Pr.prototype;function Ul(e,t,n){this.props=e,this.context=t,this.refs=Yf,this.updater=n||Kf}var $l=Ul.prototype=new Xf;$l.constructor=Ul;Qf($l,Pr.prototype);$l.isPureReactComponent=!0;var dc=Array.isArray,Zf=Object.prototype.hasOwnProperty,Wl={current:null},Jf={key:!0,ref:!0,__self:!0,__source:!0};function qf(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Zf.call(t,r)&&!Jf.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Lo,type:e,key:i,ref:s,props:o,_owner:Wl.current}}function Yv(e,t){return{$$typeof:Lo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Hl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Lo}function Xv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var fc=/\/+/g;function Fs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xv(""+e.key):t.toString(36)}function di(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Lo:case Iv:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Fs(s,0):r,dc(o)?(n="",e!=null&&(n=e.replace(fc,"$&/")+"/"),di(o,t,n,"",function(u){return u})):o!=null&&(Hl(o)&&(o=Yv(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(fc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",dc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Fs(i,a);s+=di(i,t,n,l,o)}else if(l=Qv(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Fs(i,a++),s+=di(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Bo(e,t,n){if(e==null)return e;var r=[],o=0;return di(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Zv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ae={current:null},fi={transition:null},Jv={ReactCurrentDispatcher:Ae,ReactCurrentBatchConfig:fi,ReactCurrentOwner:Wl};function ep(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:Bo,forEach:function(e,t,n){Bo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Bo(e,function(){t++}),t},toArray:function(e){return Bo(e,function(t){return t})||[]},only:function(e){if(!Hl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=Pr;I.Fragment=Fv;I.Profiler=Bv;I.PureComponent=Ul;I.StrictMode=zv;I.Suspense=Hv;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Jv;I.act=ep;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Qf({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Wl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Zf.call(t,l)&&!Jf.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Lo,type:e.type,key:o,ref:i,props:r,_owner:s}};I.createContext=function(e){return e={$$typeof:$v,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Uv,_context:e},e.Consumer=e};I.createElement=qf;I.createFactory=function(e){var t=qf.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:Wv,render:e}};I.isValidElement=Hl;I.lazy=function(e){return{$$typeof:Kv,_payload:{_status:-1,_result:e},_init:Zv}};I.memo=function(e,t){return{$$typeof:Gv,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=fi.transition;fi.transition={};try{e()}finally{fi.transition=t}};I.unstable_act=ep;I.useCallback=function(e,t){return Ae.current.useCallback(e,t)};I.useContext=function(e){return Ae.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return Ae.current.useDeferredValue(e)};I.useEffect=function(e,t){return Ae.current.useEffect(e,t)};I.useId=function(){return Ae.current.useId()};I.useImperativeHandle=function(e,t,n){return Ae.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return Ae.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return Ae.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return Ae.current.useMemo(e,t)};I.useReducer=function(e,t,n){return Ae.current.useReducer(e,t,n)};I.useRef=function(e){return Ae.current.useRef(e)};I.useState=function(e){return Ae.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return Ae.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return Ae.current.useTransition()};I.version="18.3.1";Gf.exports=I;var v=Gf.exports;const fe=Wf(v),tp=Ov({__proto__:null,default:fe},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qv=v,ey=Symbol.for("react.element"),ty=Symbol.for("react.fragment"),ny=Object.prototype.hasOwnProperty,ry=qv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,oy={key:!0,ref:!0,__self:!0,__source:!0};function np(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)ny.call(t,r)&&!oy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ey,type:e,key:i,ref:s,props:o,_owner:ry.current}}ls.Fragment=ty;ls.jsx=np;ls.jsxs=np;Hf.exports=ls;var P=Hf.exports,Ra={},rp={exports:{}},Ge={},op={exports:{}},ip={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,_){var O=L.length;L.push(_);e:for(;0<O;){var b=O-1>>>1,B=L[b];if(0<o(B,_))L[b]=_,L[O]=B,O=b;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var _=L[0],O=L.pop();if(O!==_){L[0]=O;e:for(var b=0,B=L.length,H=B>>>1;b<H;){var Ie=2*(b+1)-1,Un=L[Ie],Fe=Ie+1,mn=L[Fe];if(0>o(Un,O))Fe<B&&0>o(mn,Un)?(L[b]=mn,L[Fe]=O,b=Fe):(L[b]=Un,L[Ie]=O,b=Ie);else if(Fe<B&&0>o(mn,O))L[b]=mn,L[Fe]=O,b=Fe;else break e}}return _}function o(L,_){var O=L.sortIndex-_.sortIndex;return O!==0?O:L.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,d=null,f=3,m=!1,x=!1,y=!1,S=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(L){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=L)r(u),_.sortIndex=_.expirationTime,t(l,_);else break;_=n(u)}}function w(L){if(y=!1,h(L),!x)if(n(l)!==null)x=!0,z(C);else{var _=n(u);_!==null&&ae(w,_.startTime-L)}}function C(L,_){x=!1,y&&(y=!1,g(k),k=-1),m=!0;var O=f;try{for(h(_),d=n(l);d!==null&&(!(d.expirationTime>_)||L&&!$());){var b=d.callback;if(typeof b=="function"){d.callback=null,f=d.priorityLevel;var B=b(d.expirationTime<=_);_=e.unstable_now(),typeof B=="function"?d.callback=B:d===n(l)&&r(l),h(_)}else r(l);d=n(l)}if(d!==null)var H=!0;else{var Ie=n(u);Ie!==null&&ae(w,Ie.startTime-_),H=!1}return H}finally{d=null,f=O,m=!1}}var T=!1,E=null,k=-1,N=5,M=-1;function $(){return!(e.unstable_now()-M<N)}function V(){if(E!==null){var L=e.unstable_now();M=L;var _=!0;try{_=E(!0,L)}finally{_?q():(T=!1,E=null)}}else T=!1}var q;if(typeof p=="function")q=function(){p(V)};else if(typeof MessageChannel<"u"){var j=new MessageChannel,Z=j.port2;j.port1.onmessage=V,q=function(){Z.postMessage(null)}}else q=function(){S(V,0)};function z(L){E=L,T||(T=!0,q())}function ae(L,_){k=S(function(){L(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){x||m||(x=!0,z(C))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(L){switch(f){case 1:case 2:case 3:var _=3;break;default:_=f}var O=f;f=_;try{return L()}finally{f=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,_){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var O=f;f=L;try{return _()}finally{f=O}},e.unstable_scheduleCallback=function(L,_,O){var b=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?b+O:b):O=b,L){case 1:var B=-1;break;case 2:B=250;break;case 5:B=**********;break;case 4:B=1e4;break;default:B=5e3}return B=O+B,L={id:c++,callback:_,priorityLevel:L,startTime:O,expirationTime:B,sortIndex:-1},O>b?(L.sortIndex=O,t(u,L),n(l)===null&&L===n(u)&&(y?(g(k),k=-1):y=!0,ae(w,O-b))):(L.sortIndex=B,t(l,L),x||m||(x=!0,z(C))),L},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(L){var _=f;return function(){var O=f;f=_;try{return L.apply(this,arguments)}finally{f=O}}}})(ip);op.exports=ip;var iy=op.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sy=v,We=iy;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var sp=new Set,oo={};function Dn(e,t){fr(e,t),fr(e+"Capture",t)}function fr(e,t){for(oo[e]=t,e=0;e<t.length;e++)sp.add(t[e])}var Lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),La=Object.prototype.hasOwnProperty,ay=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,pc={},hc={};function ly(e){return La.call(hc,e)?!0:La.call(pc,e)?!1:ay.test(e)?hc[e]=!0:(pc[e]=!0,!1)}function uy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function cy(e,t,n,r){if(t===null||typeof t>"u"||uy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Me(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var xe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){xe[e]=new Me(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];xe[t]=new Me(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){xe[e]=new Me(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){xe[e]=new Me(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){xe[e]=new Me(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){xe[e]=new Me(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){xe[e]=new Me(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){xe[e]=new Me(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){xe[e]=new Me(e,5,!1,e.toLowerCase(),null,!1,!1)});var Gl=/[\-:]([a-z])/g;function Kl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Gl,Kl);xe[t]=new Me(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Gl,Kl);xe[t]=new Me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Gl,Kl);xe[t]=new Me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){xe[e]=new Me(e,1,!1,e.toLowerCase(),null,!1,!1)});xe.xlinkHref=new Me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){xe[e]=new Me(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ql(e,t,n,r){var o=xe.hasOwnProperty(t)?xe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(cy(t,n,o,r)&&(n=null),r||o===null?ly(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var _t=sy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Uo=Symbol.for("react.element"),Wn=Symbol.for("react.portal"),Hn=Symbol.for("react.fragment"),Yl=Symbol.for("react.strict_mode"),Na=Symbol.for("react.profiler"),ap=Symbol.for("react.provider"),lp=Symbol.for("react.context"),Xl=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ma=Symbol.for("react.suspense_list"),Zl=Symbol.for("react.memo"),Ot=Symbol.for("react.lazy"),up=Symbol.for("react.offscreen"),mc=Symbol.iterator;function Rr(e){return e===null||typeof e!="object"?null:(e=mc&&e[mc]||e["@@iterator"],typeof e=="function"?e:null)}var re=Object.assign,zs;function Fr(e){if(zs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);zs=t&&t[1]||""}return`
`+zs+e}var Bs=!1;function Us(e,t){if(!e||Bs)return"";Bs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Bs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Fr(e):""}function dy(e){switch(e.tag){case 5:return Fr(e.type);case 16:return Fr("Lazy");case 13:return Fr("Suspense");case 19:return Fr("SuspenseList");case 0:case 2:case 15:return e=Us(e.type,!1),e;case 11:return e=Us(e.type.render,!1),e;case 1:return e=Us(e.type,!0),e;default:return""}}function ja(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Hn:return"Fragment";case Wn:return"Portal";case Na:return"Profiler";case Yl:return"StrictMode";case Aa:return"Suspense";case Ma:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case lp:return(e.displayName||"Context")+".Consumer";case ap:return(e._context.displayName||"Context")+".Provider";case Xl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Zl:return t=e.displayName||null,t!==null?t:ja(e.type)||"Memo";case Ot:t=e._payload,e=e._init;try{return ja(e(t))}catch{}}return null}function fy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ja(t);case 8:return t===Yl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function rn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function py(e){var t=cp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function $o(e){e._valueTracker||(e._valueTracker=py(e))}function dp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=cp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ti(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _a(e,t){var n=t.checked;return re({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function gc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=rn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function fp(e,t){t=t.checked,t!=null&&Ql(e,"checked",t,!1)}function ba(e,t){fp(e,t);var n=rn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Va(e,t.type,n):t.hasOwnProperty("defaultValue")&&Va(e,t.type,rn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function vc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Va(e,t,n){(t!=="number"||Ti(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var zr=Array.isArray;function sr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+rn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Da(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return re({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function yc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(zr(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:rn(n)}}function pp(e,t){var n=rn(t.value),r=rn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function xc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function hp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Oa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?hp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Wo,mp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Wo=Wo||document.createElement("div"),Wo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Wo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function io(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Wr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hy=["Webkit","ms","Moz","O"];Object.keys(Wr).forEach(function(e){hy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Wr[t]=Wr[e]})});function gp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Wr.hasOwnProperty(e)&&Wr[e]?(""+t).trim():t+"px"}function vp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=gp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var my=re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ia(e,t){if(t){if(my[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Fa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var za=null;function Jl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ba=null,ar=null,lr=null;function wc(e){if(e=Mo(e)){if(typeof Ba!="function")throw Error(R(280));var t=e.stateNode;t&&(t=ps(t),Ba(e.stateNode,e.type,t))}}function yp(e){ar?lr?lr.push(e):lr=[e]:ar=e}function xp(){if(ar){var e=ar,t=lr;if(lr=ar=null,wc(e),t)for(e=0;e<t.length;e++)wc(t[e])}}function wp(e,t){return e(t)}function Sp(){}var $s=!1;function Pp(e,t,n){if($s)return e(t,n);$s=!0;try{return wp(e,t,n)}finally{$s=!1,(ar!==null||lr!==null)&&(Sp(),xp())}}function so(e,t){var n=e.stateNode;if(n===null)return null;var r=ps(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Ua=!1;if(Lt)try{var Lr={};Object.defineProperty(Lr,"passive",{get:function(){Ua=!0}}),window.addEventListener("test",Lr,Lr),window.removeEventListener("test",Lr,Lr)}catch{Ua=!1}function gy(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Hr=!1,ki=null,Ri=!1,$a=null,vy={onError:function(e){Hr=!0,ki=e}};function yy(e,t,n,r,o,i,s,a,l){Hr=!1,ki=null,gy.apply(vy,arguments)}function xy(e,t,n,r,o,i,s,a,l){if(yy.apply(this,arguments),Hr){if(Hr){var u=ki;Hr=!1,ki=null}else throw Error(R(198));Ri||(Ri=!0,$a=u)}}function On(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Cp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Sc(e){if(On(e)!==e)throw Error(R(188))}function wy(e){var t=e.alternate;if(!t){if(t=On(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Sc(o),e;if(i===r)return Sc(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Ep(e){return e=wy(e),e!==null?Tp(e):null}function Tp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Tp(e);if(t!==null)return t;e=e.sibling}return null}var kp=We.unstable_scheduleCallback,Pc=We.unstable_cancelCallback,Sy=We.unstable_shouldYield,Py=We.unstable_requestPaint,le=We.unstable_now,Cy=We.unstable_getCurrentPriorityLevel,ql=We.unstable_ImmediatePriority,Rp=We.unstable_UserBlockingPriority,Li=We.unstable_NormalPriority,Ey=We.unstable_LowPriority,Lp=We.unstable_IdlePriority,us=null,gt=null;function Ty(e){if(gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(us,e,void 0,(e.current.flags&128)===128)}catch{}}var lt=Math.clz32?Math.clz32:Ly,ky=Math.log,Ry=Math.LN2;function Ly(e){return e>>>=0,e===0?32:31-(ky(e)/Ry|0)|0}var Ho=64,Go=4194304;function Br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ni(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Br(a):(i&=s,i!==0&&(r=Br(i)))}else s=n&~o,s!==0?r=Br(s):i!==0&&(r=Br(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-lt(t),o=1<<n,r|=e[n],t&=~o;return r}function Ny(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ay(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-lt(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=Ny(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Wa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Np(){var e=Ho;return Ho<<=1,!(Ho&4194240)&&(Ho=64),e}function Ws(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function No(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-lt(t),e[t]=n}function My(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-lt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function eu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var W=0;function Ap(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Mp,tu,jp,_p,bp,Ha=!1,Ko=[],Ht=null,Gt=null,Kt=null,ao=new Map,lo=new Map,zt=[],jy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Cc(e,t){switch(e){case"focusin":case"focusout":Ht=null;break;case"dragenter":case"dragleave":Gt=null;break;case"mouseover":case"mouseout":Kt=null;break;case"pointerover":case"pointerout":ao.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":lo.delete(t.pointerId)}}function Nr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Mo(t),t!==null&&tu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function _y(e,t,n,r,o){switch(t){case"focusin":return Ht=Nr(Ht,e,t,n,r,o),!0;case"dragenter":return Gt=Nr(Gt,e,t,n,r,o),!0;case"mouseover":return Kt=Nr(Kt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return ao.set(i,Nr(ao.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,lo.set(i,Nr(lo.get(i)||null,e,t,n,r,o)),!0}return!1}function Vp(e){var t=Cn(e.target);if(t!==null){var n=On(t);if(n!==null){if(t=n.tag,t===13){if(t=Cp(n),t!==null){e.blockedOn=t,bp(e.priority,function(){jp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function pi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ga(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);za=r,n.target.dispatchEvent(r),za=null}else return t=Mo(n),t!==null&&tu(t),e.blockedOn=n,!1;t.shift()}return!0}function Ec(e,t,n){pi(e)&&n.delete(t)}function by(){Ha=!1,Ht!==null&&pi(Ht)&&(Ht=null),Gt!==null&&pi(Gt)&&(Gt=null),Kt!==null&&pi(Kt)&&(Kt=null),ao.forEach(Ec),lo.forEach(Ec)}function Ar(e,t){e.blockedOn===t&&(e.blockedOn=null,Ha||(Ha=!0,We.unstable_scheduleCallback(We.unstable_NormalPriority,by)))}function uo(e){function t(o){return Ar(o,e)}if(0<Ko.length){Ar(Ko[0],e);for(var n=1;n<Ko.length;n++){var r=Ko[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ht!==null&&Ar(Ht,e),Gt!==null&&Ar(Gt,e),Kt!==null&&Ar(Kt,e),ao.forEach(t),lo.forEach(t),n=0;n<zt.length;n++)r=zt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<zt.length&&(n=zt[0],n.blockedOn===null);)Vp(n),n.blockedOn===null&&zt.shift()}var ur=_t.ReactCurrentBatchConfig,Ai=!0;function Vy(e,t,n,r){var o=W,i=ur.transition;ur.transition=null;try{W=1,nu(e,t,n,r)}finally{W=o,ur.transition=i}}function Dy(e,t,n,r){var o=W,i=ur.transition;ur.transition=null;try{W=4,nu(e,t,n,r)}finally{W=o,ur.transition=i}}function nu(e,t,n,r){if(Ai){var o=Ga(e,t,n,r);if(o===null)ea(e,t,r,Mi,n),Cc(e,r);else if(_y(o,e,t,n,r))r.stopPropagation();else if(Cc(e,r),t&4&&-1<jy.indexOf(e)){for(;o!==null;){var i=Mo(o);if(i!==null&&Mp(i),i=Ga(e,t,n,r),i===null&&ea(e,t,r,Mi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else ea(e,t,r,null,n)}}var Mi=null;function Ga(e,t,n,r){if(Mi=null,e=Jl(r),e=Cn(e),e!==null)if(t=On(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Cp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Mi=e,null}function Dp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Cy()){case ql:return 1;case Rp:return 4;case Li:case Ey:return 16;case Lp:return 536870912;default:return 16}default:return 16}}var Ut=null,ru=null,hi=null;function Op(){if(hi)return hi;var e,t=ru,n=t.length,r,o="value"in Ut?Ut.value:Ut.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return hi=o.slice(e,1<r?1-r:void 0)}function mi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Qo(){return!0}function Tc(){return!1}function Ke(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Qo:Tc,this.isPropagationStopped=Tc,this}return re(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Qo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Qo)},persist:function(){},isPersistent:Qo}),t}var Cr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ou=Ke(Cr),Ao=re({},Cr,{view:0,detail:0}),Oy=Ke(Ao),Hs,Gs,Mr,cs=re({},Ao,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:iu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Mr&&(Mr&&e.type==="mousemove"?(Hs=e.screenX-Mr.screenX,Gs=e.screenY-Mr.screenY):Gs=Hs=0,Mr=e),Hs)},movementY:function(e){return"movementY"in e?e.movementY:Gs}}),kc=Ke(cs),Iy=re({},cs,{dataTransfer:0}),Fy=Ke(Iy),zy=re({},Ao,{relatedTarget:0}),Ks=Ke(zy),By=re({},Cr,{animationName:0,elapsedTime:0,pseudoElement:0}),Uy=Ke(By),$y=re({},Cr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wy=Ke($y),Hy=re({},Cr,{data:0}),Rc=Ke(Hy),Gy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ky={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Yy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qy[e])?!!t[e]:!1}function iu(){return Yy}var Xy=re({},Ao,{key:function(e){if(e.key){var t=Gy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=mi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ky[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:iu,charCode:function(e){return e.type==="keypress"?mi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?mi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Zy=Ke(Xy),Jy=re({},cs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Lc=Ke(Jy),qy=re({},Ao,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:iu}),e0=Ke(qy),t0=re({},Cr,{propertyName:0,elapsedTime:0,pseudoElement:0}),n0=Ke(t0),r0=re({},cs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),o0=Ke(r0),i0=[9,13,27,32],su=Lt&&"CompositionEvent"in window,Gr=null;Lt&&"documentMode"in document&&(Gr=document.documentMode);var s0=Lt&&"TextEvent"in window&&!Gr,Ip=Lt&&(!su||Gr&&8<Gr&&11>=Gr),Nc=String.fromCharCode(32),Ac=!1;function Fp(e,t){switch(e){case"keyup":return i0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gn=!1;function a0(e,t){switch(e){case"compositionend":return zp(t);case"keypress":return t.which!==32?null:(Ac=!0,Nc);case"textInput":return e=t.data,e===Nc&&Ac?null:e;default:return null}}function l0(e,t){if(Gn)return e==="compositionend"||!su&&Fp(e,t)?(e=Op(),hi=ru=Ut=null,Gn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ip&&t.locale!=="ko"?null:t.data;default:return null}}var u0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Mc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!u0[e.type]:t==="textarea"}function Bp(e,t,n,r){yp(r),t=ji(t,"onChange"),0<t.length&&(n=new ou("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kr=null,co=null;function c0(e){Jp(e,0)}function ds(e){var t=Yn(e);if(dp(t))return e}function d0(e,t){if(e==="change")return t}var Up=!1;if(Lt){var Qs;if(Lt){var Ys="oninput"in document;if(!Ys){var jc=document.createElement("div");jc.setAttribute("oninput","return;"),Ys=typeof jc.oninput=="function"}Qs=Ys}else Qs=!1;Up=Qs&&(!document.documentMode||9<document.documentMode)}function _c(){Kr&&(Kr.detachEvent("onpropertychange",$p),co=Kr=null)}function $p(e){if(e.propertyName==="value"&&ds(co)){var t=[];Bp(t,co,e,Jl(e)),Pp(c0,t)}}function f0(e,t,n){e==="focusin"?(_c(),Kr=t,co=n,Kr.attachEvent("onpropertychange",$p)):e==="focusout"&&_c()}function p0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ds(co)}function h0(e,t){if(e==="click")return ds(t)}function m0(e,t){if(e==="input"||e==="change")return ds(t)}function g0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ct=typeof Object.is=="function"?Object.is:g0;function fo(e,t){if(ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!La.call(t,o)||!ct(e[o],t[o]))return!1}return!0}function bc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vc(e,t){var n=bc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=bc(n)}}function Wp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Wp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Hp(){for(var e=window,t=Ti();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ti(e.document)}return t}function au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function v0(e){var t=Hp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Wp(n.ownerDocument.documentElement,n)){if(r!==null&&au(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Vc(n,i);var s=Vc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var y0=Lt&&"documentMode"in document&&11>=document.documentMode,Kn=null,Ka=null,Qr=null,Qa=!1;function Dc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Qa||Kn==null||Kn!==Ti(r)||(r=Kn,"selectionStart"in r&&au(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Qr&&fo(Qr,r)||(Qr=r,r=ji(Ka,"onSelect"),0<r.length&&(t=new ou("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Kn)))}function Yo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Qn={animationend:Yo("Animation","AnimationEnd"),animationiteration:Yo("Animation","AnimationIteration"),animationstart:Yo("Animation","AnimationStart"),transitionend:Yo("Transition","TransitionEnd")},Xs={},Gp={};Lt&&(Gp=document.createElement("div").style,"AnimationEvent"in window||(delete Qn.animationend.animation,delete Qn.animationiteration.animation,delete Qn.animationstart.animation),"TransitionEvent"in window||delete Qn.transitionend.transition);function fs(e){if(Xs[e])return Xs[e];if(!Qn[e])return e;var t=Qn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gp)return Xs[e]=t[n];return e}var Kp=fs("animationend"),Qp=fs("animationiteration"),Yp=fs("animationstart"),Xp=fs("transitionend"),Zp=new Map,Oc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function un(e,t){Zp.set(e,t),Dn(t,[e])}for(var Zs=0;Zs<Oc.length;Zs++){var Js=Oc[Zs],x0=Js.toLowerCase(),w0=Js[0].toUpperCase()+Js.slice(1);un(x0,"on"+w0)}un(Kp,"onAnimationEnd");un(Qp,"onAnimationIteration");un(Yp,"onAnimationStart");un("dblclick","onDoubleClick");un("focusin","onFocus");un("focusout","onBlur");un(Xp,"onTransitionEnd");fr("onMouseEnter",["mouseout","mouseover"]);fr("onMouseLeave",["mouseout","mouseover"]);fr("onPointerEnter",["pointerout","pointerover"]);fr("onPointerLeave",["pointerout","pointerover"]);Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ur="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),S0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ur));function Ic(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,xy(r,t,void 0,e),e.currentTarget=null}function Jp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;Ic(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;Ic(o,a,u),i=l}}}if(Ri)throw e=$a,Ri=!1,$a=null,e}function Y(e,t){var n=t[qa];n===void 0&&(n=t[qa]=new Set);var r=e+"__bubble";n.has(r)||(qp(t,e,2,!1),n.add(r))}function qs(e,t,n){var r=0;t&&(r|=4),qp(n,e,r,t)}var Xo="_reactListening"+Math.random().toString(36).slice(2);function po(e){if(!e[Xo]){e[Xo]=!0,sp.forEach(function(n){n!=="selectionchange"&&(S0.has(n)||qs(n,!1,e),qs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xo]||(t[Xo]=!0,qs("selectionchange",!1,t))}}function qp(e,t,n,r){switch(Dp(t)){case 1:var o=Vy;break;case 4:o=Dy;break;default:o=nu}n=o.bind(null,t,n,e),o=void 0,!Ua||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function ea(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Cn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Pp(function(){var u=i,c=Jl(n),d=[];e:{var f=Zp.get(e);if(f!==void 0){var m=ou,x=e;switch(e){case"keypress":if(mi(n)===0)break e;case"keydown":case"keyup":m=Zy;break;case"focusin":x="focus",m=Ks;break;case"focusout":x="blur",m=Ks;break;case"beforeblur":case"afterblur":m=Ks;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=kc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Fy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=e0;break;case Kp:case Qp:case Yp:m=Uy;break;case Xp:m=n0;break;case"scroll":m=Oy;break;case"wheel":m=o0;break;case"copy":case"cut":case"paste":m=Wy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Lc}var y=(t&4)!==0,S=!y&&e==="scroll",g=y?f!==null?f+"Capture":null:f;y=[];for(var p=u,h;p!==null;){h=p;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,g!==null&&(w=so(p,g),w!=null&&y.push(ho(p,w,h)))),S)break;p=p.return}0<y.length&&(f=new m(f,x,null,n,c),d.push({event:f,listeners:y}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==za&&(x=n.relatedTarget||n.fromElement)&&(Cn(x)||x[Nt]))break e;if((m||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=u,x=x?Cn(x):null,x!==null&&(S=On(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=u),m!==x)){if(y=kc,w="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Lc,w="onPointerLeave",g="onPointerEnter",p="pointer"),S=m==null?f:Yn(m),h=x==null?f:Yn(x),f=new y(w,p+"leave",m,n,c),f.target=S,f.relatedTarget=h,w=null,Cn(c)===u&&(y=new y(g,p+"enter",x,n,c),y.target=h,y.relatedTarget=S,w=y),S=w,m&&x)t:{for(y=m,g=x,p=0,h=y;h;h=$n(h))p++;for(h=0,w=g;w;w=$n(w))h++;for(;0<p-h;)y=$n(y),p--;for(;0<h-p;)g=$n(g),h--;for(;p--;){if(y===g||g!==null&&y===g.alternate)break t;y=$n(y),g=$n(g)}y=null}else y=null;m!==null&&Fc(d,f,m,y,!1),x!==null&&S!==null&&Fc(d,S,x,y,!0)}}e:{if(f=u?Yn(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var C=d0;else if(Mc(f))if(Up)C=m0;else{C=p0;var T=f0}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=h0);if(C&&(C=C(e,u))){Bp(d,C,n,c);break e}T&&T(e,f,u),e==="focusout"&&(T=f._wrapperState)&&T.controlled&&f.type==="number"&&Va(f,"number",f.value)}switch(T=u?Yn(u):window,e){case"focusin":(Mc(T)||T.contentEditable==="true")&&(Kn=T,Ka=u,Qr=null);break;case"focusout":Qr=Ka=Kn=null;break;case"mousedown":Qa=!0;break;case"contextmenu":case"mouseup":case"dragend":Qa=!1,Dc(d,n,c);break;case"selectionchange":if(y0)break;case"keydown":case"keyup":Dc(d,n,c)}var E;if(su)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Gn?Fp(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(Ip&&n.locale!=="ko"&&(Gn||k!=="onCompositionStart"?k==="onCompositionEnd"&&Gn&&(E=Op()):(Ut=c,ru="value"in Ut?Ut.value:Ut.textContent,Gn=!0)),T=ji(u,k),0<T.length&&(k=new Rc(k,e,null,n,c),d.push({event:k,listeners:T}),E?k.data=E:(E=zp(n),E!==null&&(k.data=E)))),(E=s0?a0(e,n):l0(e,n))&&(u=ji(u,"onBeforeInput"),0<u.length&&(c=new Rc("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=E))}Jp(d,t)})}function ho(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ji(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=so(e,n),i!=null&&r.unshift(ho(e,i,o)),i=so(e,t),i!=null&&r.push(ho(e,i,o))),e=e.return}return r}function $n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Fc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=so(n,i),l!=null&&s.unshift(ho(n,l,a))):o||(l=so(n,i),l!=null&&s.push(ho(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var P0=/\r\n?/g,C0=/\u0000|\uFFFD/g;function zc(e){return(typeof e=="string"?e:""+e).replace(P0,`
`).replace(C0,"")}function Zo(e,t,n){if(t=zc(t),zc(e)!==t&&n)throw Error(R(425))}function _i(){}var Ya=null,Xa=null;function Za(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ja=typeof setTimeout=="function"?setTimeout:void 0,E0=typeof clearTimeout=="function"?clearTimeout:void 0,Bc=typeof Promise=="function"?Promise:void 0,T0=typeof queueMicrotask=="function"?queueMicrotask:typeof Bc<"u"?function(e){return Bc.resolve(null).then(e).catch(k0)}:Ja;function k0(e){setTimeout(function(){throw e})}function ta(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),uo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);uo(t)}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Er=Math.random().toString(36).slice(2),mt="__reactFiber$"+Er,mo="__reactProps$"+Er,Nt="__reactContainer$"+Er,qa="__reactEvents$"+Er,R0="__reactListeners$"+Er,L0="__reactHandles$"+Er;function Cn(e){var t=e[mt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nt]||n[mt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uc(e);e!==null;){if(n=e[mt])return n;e=Uc(e)}return t}e=n,n=e.parentNode}return null}function Mo(e){return e=e[mt]||e[Nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Yn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function ps(e){return e[mo]||null}var el=[],Xn=-1;function cn(e){return{current:e}}function X(e){0>Xn||(e.current=el[Xn],el[Xn]=null,Xn--)}function G(e,t){Xn++,el[Xn]=e.current,e.current=t}var on={},Ee=cn(on),be=cn(!1),An=on;function pr(e,t){var n=e.type.contextTypes;if(!n)return on;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ve(e){return e=e.childContextTypes,e!=null}function bi(){X(be),X(Ee)}function $c(e,t,n){if(Ee.current!==on)throw Error(R(168));G(Ee,t),G(be,n)}function eh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,fy(e)||"Unknown",o));return re({},n,r)}function Vi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||on,An=Ee.current,G(Ee,e),G(be,be.current),!0}function Wc(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=eh(e,t,An),r.__reactInternalMemoizedMergedChildContext=e,X(be),X(Ee),G(Ee,e)):X(be),G(be,n)}var St=null,hs=!1,na=!1;function th(e){St===null?St=[e]:St.push(e)}function N0(e){hs=!0,th(e)}function dn(){if(!na&&St!==null){na=!0;var e=0,t=W;try{var n=St;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,hs=!1}catch(o){throw St!==null&&(St=St.slice(e+1)),kp(ql,dn),o}finally{W=t,na=!1}}return null}var Zn=[],Jn=0,Di=null,Oi=0,Ze=[],Je=0,Mn=null,Pt=1,Ct="";function yn(e,t){Zn[Jn++]=Oi,Zn[Jn++]=Di,Di=e,Oi=t}function nh(e,t,n){Ze[Je++]=Pt,Ze[Je++]=Ct,Ze[Je++]=Mn,Mn=e;var r=Pt;e=Ct;var o=32-lt(r)-1;r&=~(1<<o),n+=1;var i=32-lt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Pt=1<<32-lt(t)+o|n<<o|r,Ct=i+e}else Pt=1<<i|n<<o|r,Ct=e}function lu(e){e.return!==null&&(yn(e,1),nh(e,1,0))}function uu(e){for(;e===Di;)Di=Zn[--Jn],Zn[Jn]=null,Oi=Zn[--Jn],Zn[Jn]=null;for(;e===Mn;)Mn=Ze[--Je],Ze[Je]=null,Ct=Ze[--Je],Ze[Je]=null,Pt=Ze[--Je],Ze[Je]=null}var $e=null,Ue=null,J=!1,at=null;function rh(e,t){var n=qe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,$e=e,Ue=Qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,$e=e,Ue=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Mn!==null?{id:Pt,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=qe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,$e=e,Ue=null,!0):!1;default:return!1}}function tl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nl(e){if(J){var t=Ue;if(t){var n=t;if(!Hc(e,t)){if(tl(e))throw Error(R(418));t=Qt(n.nextSibling);var r=$e;t&&Hc(e,t)?rh(r,n):(e.flags=e.flags&-4097|2,J=!1,$e=e)}}else{if(tl(e))throw Error(R(418));e.flags=e.flags&-4097|2,J=!1,$e=e}}}function Gc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$e=e}function Jo(e){if(e!==$e)return!1;if(!J)return Gc(e),J=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Za(e.type,e.memoizedProps)),t&&(t=Ue)){if(tl(e))throw oh(),Error(R(418));for(;t;)rh(e,t),t=Qt(t.nextSibling)}if(Gc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ue=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ue=null}}else Ue=$e?Qt(e.stateNode.nextSibling):null;return!0}function oh(){for(var e=Ue;e;)e=Qt(e.nextSibling)}function hr(){Ue=$e=null,J=!1}function cu(e){at===null?at=[e]:at.push(e)}var A0=_t.ReactCurrentBatchConfig;function jr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function qo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Kc(e){var t=e._init;return t(e._payload)}function ih(e){function t(g,p){if(e){var h=g.deletions;h===null?(g.deletions=[p],g.flags|=16):h.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function o(g,p){return g=Jt(g,p),g.index=0,g.sibling=null,g}function i(g,p,h){return g.index=h,e?(h=g.alternate,h!==null?(h=h.index,h<p?(g.flags|=2,p):h):(g.flags|=2,p)):(g.flags|=1048576,p)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,p,h,w){return p===null||p.tag!==6?(p=ua(h,g.mode,w),p.return=g,p):(p=o(p,h),p.return=g,p)}function l(g,p,h,w){var C=h.type;return C===Hn?c(g,p,h.props.children,w,h.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ot&&Kc(C)===p.type)?(w=o(p,h.props),w.ref=jr(g,p,h),w.return=g,w):(w=Pi(h.type,h.key,h.props,null,g.mode,w),w.ref=jr(g,p,h),w.return=g,w)}function u(g,p,h,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=ca(h,g.mode,w),p.return=g,p):(p=o(p,h.children||[]),p.return=g,p)}function c(g,p,h,w,C){return p===null||p.tag!==7?(p=Nn(h,g.mode,w,C),p.return=g,p):(p=o(p,h),p.return=g,p)}function d(g,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ua(""+p,g.mode,h),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Uo:return h=Pi(p.type,p.key,p.props,null,g.mode,h),h.ref=jr(g,null,p),h.return=g,h;case Wn:return p=ca(p,g.mode,h),p.return=g,p;case Ot:var w=p._init;return d(g,w(p._payload),h)}if(zr(p)||Rr(p))return p=Nn(p,g.mode,h,null),p.return=g,p;qo(g,p)}return null}function f(g,p,h,w){var C=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return C!==null?null:a(g,p,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Uo:return h.key===C?l(g,p,h,w):null;case Wn:return h.key===C?u(g,p,h,w):null;case Ot:return C=h._init,f(g,p,C(h._payload),w)}if(zr(h)||Rr(h))return C!==null?null:c(g,p,h,w,null);qo(g,h)}return null}function m(g,p,h,w,C){if(typeof w=="string"&&w!==""||typeof w=="number")return g=g.get(h)||null,a(p,g,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Uo:return g=g.get(w.key===null?h:w.key)||null,l(p,g,w,C);case Wn:return g=g.get(w.key===null?h:w.key)||null,u(p,g,w,C);case Ot:var T=w._init;return m(g,p,h,T(w._payload),C)}if(zr(w)||Rr(w))return g=g.get(h)||null,c(p,g,w,C,null);qo(p,w)}return null}function x(g,p,h,w){for(var C=null,T=null,E=p,k=p=0,N=null;E!==null&&k<h.length;k++){E.index>k?(N=E,E=null):N=E.sibling;var M=f(g,E,h[k],w);if(M===null){E===null&&(E=N);break}e&&E&&M.alternate===null&&t(g,E),p=i(M,p,k),T===null?C=M:T.sibling=M,T=M,E=N}if(k===h.length)return n(g,E),J&&yn(g,k),C;if(E===null){for(;k<h.length;k++)E=d(g,h[k],w),E!==null&&(p=i(E,p,k),T===null?C=E:T.sibling=E,T=E);return J&&yn(g,k),C}for(E=r(g,E);k<h.length;k++)N=m(E,g,k,h[k],w),N!==null&&(e&&N.alternate!==null&&E.delete(N.key===null?k:N.key),p=i(N,p,k),T===null?C=N:T.sibling=N,T=N);return e&&E.forEach(function($){return t(g,$)}),J&&yn(g,k),C}function y(g,p,h,w){var C=Rr(h);if(typeof C!="function")throw Error(R(150));if(h=C.call(h),h==null)throw Error(R(151));for(var T=C=null,E=p,k=p=0,N=null,M=h.next();E!==null&&!M.done;k++,M=h.next()){E.index>k?(N=E,E=null):N=E.sibling;var $=f(g,E,M.value,w);if($===null){E===null&&(E=N);break}e&&E&&$.alternate===null&&t(g,E),p=i($,p,k),T===null?C=$:T.sibling=$,T=$,E=N}if(M.done)return n(g,E),J&&yn(g,k),C;if(E===null){for(;!M.done;k++,M=h.next())M=d(g,M.value,w),M!==null&&(p=i(M,p,k),T===null?C=M:T.sibling=M,T=M);return J&&yn(g,k),C}for(E=r(g,E);!M.done;k++,M=h.next())M=m(E,g,k,M.value,w),M!==null&&(e&&M.alternate!==null&&E.delete(M.key===null?k:M.key),p=i(M,p,k),T===null?C=M:T.sibling=M,T=M);return e&&E.forEach(function(V){return t(g,V)}),J&&yn(g,k),C}function S(g,p,h,w){if(typeof h=="object"&&h!==null&&h.type===Hn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Uo:e:{for(var C=h.key,T=p;T!==null;){if(T.key===C){if(C=h.type,C===Hn){if(T.tag===7){n(g,T.sibling),p=o(T,h.props.children),p.return=g,g=p;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ot&&Kc(C)===T.type){n(g,T.sibling),p=o(T,h.props),p.ref=jr(g,T,h),p.return=g,g=p;break e}n(g,T);break}else t(g,T);T=T.sibling}h.type===Hn?(p=Nn(h.props.children,g.mode,w,h.key),p.return=g,g=p):(w=Pi(h.type,h.key,h.props,null,g.mode,w),w.ref=jr(g,p,h),w.return=g,g=w)}return s(g);case Wn:e:{for(T=h.key;p!==null;){if(p.key===T)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(g,p.sibling),p=o(p,h.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=ca(h,g.mode,w),p.return=g,g=p}return s(g);case Ot:return T=h._init,S(g,p,T(h._payload),w)}if(zr(h))return x(g,p,h,w);if(Rr(h))return y(g,p,h,w);qo(g,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(g,p.sibling),p=o(p,h),p.return=g,g=p):(n(g,p),p=ua(h,g.mode,w),p.return=g,g=p),s(g)):n(g,p)}return S}var mr=ih(!0),sh=ih(!1),Ii=cn(null),Fi=null,qn=null,du=null;function fu(){du=qn=Fi=null}function pu(e){var t=Ii.current;X(Ii),e._currentValue=t}function rl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function cr(e,t){Fi=e,du=qn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(_e=!0),e.firstContext=null)}function tt(e){var t=e._currentValue;if(du!==e)if(e={context:e,memoizedValue:t,next:null},qn===null){if(Fi===null)throw Error(R(308));qn=e,Fi.dependencies={lanes:0,firstContext:e}}else qn=qn.next=e;return t}var En=null;function hu(e){En===null?En=[e]:En.push(e)}function ah(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,hu(t)):(n.next=o.next,o.next=n),t.interleaved=n,At(e,r)}function At(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var It=!1;function mu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function lh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Yt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,At(e,n)}return o=r.interleaved,o===null?(t.next=t,hu(r)):(t.next=o.next,o.next=t),r.interleaved=t,At(e,n)}function gi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,eu(e,n)}}function Qc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function zi(e,t,n,r){var o=e.updateQueue;It=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var d=o.baseState;s=0,c=u=l=null,a=i;do{var f=a.lane,m=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,y=a;switch(f=t,m=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){d=x.call(m,d,f);break e}d=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,f=typeof x=="function"?x.call(m,d,f):x,f==null)break e;d=re({},d,f);break e;case 2:It=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=m,l=d):c=c.next=m,s|=f;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;f=a,a=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(1);if(c===null&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);_n|=s,e.lanes=s,e.memoizedState=d}}function Yc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var jo={},vt=cn(jo),go=cn(jo),vo=cn(jo);function Tn(e){if(e===jo)throw Error(R(174));return e}function gu(e,t){switch(G(vo,t),G(go,e),G(vt,jo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Oa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Oa(t,e)}X(vt),G(vt,t)}function gr(){X(vt),X(go),X(vo)}function uh(e){Tn(vo.current);var t=Tn(vt.current),n=Oa(t,e.type);t!==n&&(G(go,e),G(vt,n))}function vu(e){go.current===e&&(X(vt),X(go))}var ee=cn(0);function Bi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function yu(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var vi=_t.ReactCurrentDispatcher,oa=_t.ReactCurrentBatchConfig,jn=0,ne=null,de=null,he=null,Ui=!1,Yr=!1,yo=0,M0=0;function we(){throw Error(R(321))}function xu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ct(e[n],t[n]))return!1;return!0}function wu(e,t,n,r,o,i){if(jn=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,vi.current=e===null||e.memoizedState===null?V0:D0,e=n(r,o),Yr){i=0;do{if(Yr=!1,yo=0,25<=i)throw Error(R(301));i+=1,he=de=null,t.updateQueue=null,vi.current=O0,e=n(r,o)}while(Yr)}if(vi.current=$i,t=de!==null&&de.next!==null,jn=0,he=de=ne=null,Ui=!1,t)throw Error(R(300));return e}function Su(){var e=yo!==0;return yo=0,e}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return he===null?ne.memoizedState=he=e:he=he.next=e,he}function nt(){if(de===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=de.next;var t=he===null?ne.memoizedState:he.next;if(t!==null)he=t,de=e;else{if(e===null)throw Error(R(310));de=e,e={memoizedState:de.memoizedState,baseState:de.baseState,baseQueue:de.baseQueue,queue:de.queue,next:null},he===null?ne.memoizedState=he=e:he=he.next=e}return he}function xo(e,t){return typeof t=="function"?t(e):t}function ia(e){var t=nt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=de,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var c=u.lane;if((jn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,ne.lanes|=c,_n|=c}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,ct(r,t.memoizedState)||(_e=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ne.lanes|=i,_n|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function sa(e){var t=nt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);ct(i,t.memoizedState)||(_e=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ch(){}function dh(e,t){var n=ne,r=nt(),o=t(),i=!ct(r.memoizedState,o);if(i&&(r.memoizedState=o,_e=!0),r=r.queue,Pu(hh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||he!==null&&he.memoizedState.tag&1){if(n.flags|=2048,wo(9,ph.bind(null,n,r,o,t),void 0,null),me===null)throw Error(R(349));jn&30||fh(n,t,o)}return o}function fh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ph(e,t,n,r){t.value=n,t.getSnapshot=r,mh(t)&&gh(e)}function hh(e,t,n){return n(function(){mh(t)&&gh(e)})}function mh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ct(e,n)}catch{return!0}}function gh(e){var t=At(e,1);t!==null&&ut(t,e,1,-1)}function Xc(e){var t=pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=b0.bind(null,ne,e),[t.memoizedState,e]}function wo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function vh(){return nt().memoizedState}function yi(e,t,n,r){var o=pt();ne.flags|=e,o.memoizedState=wo(1|t,n,void 0,r===void 0?null:r)}function ms(e,t,n,r){var o=nt();r=r===void 0?null:r;var i=void 0;if(de!==null){var s=de.memoizedState;if(i=s.destroy,r!==null&&xu(r,s.deps)){o.memoizedState=wo(t,n,i,r);return}}ne.flags|=e,o.memoizedState=wo(1|t,n,i,r)}function Zc(e,t){return yi(8390656,8,e,t)}function Pu(e,t){return ms(2048,8,e,t)}function yh(e,t){return ms(4,2,e,t)}function xh(e,t){return ms(4,4,e,t)}function wh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sh(e,t,n){return n=n!=null?n.concat([e]):null,ms(4,4,wh.bind(null,t,e),n)}function Cu(){}function Ph(e,t){var n=nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&xu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ch(e,t){var n=nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&xu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Eh(e,t,n){return jn&21?(ct(n,t)||(n=Np(),ne.lanes|=n,_n|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,_e=!0),e.memoizedState=n)}function j0(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=oa.transition;oa.transition={};try{e(!1),t()}finally{W=n,oa.transition=r}}function Th(){return nt().memoizedState}function _0(e,t,n){var r=Zt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},kh(e))Rh(t,n);else if(n=ah(e,t,n,r),n!==null){var o=Ne();ut(n,e,r,o),Lh(n,t,r)}}function b0(e,t,n){var r=Zt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(kh(e))Rh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,ct(a,s)){var l=t.interleaved;l===null?(o.next=o,hu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=ah(e,t,o,r),n!==null&&(o=Ne(),ut(n,e,r,o),Lh(n,t,r))}}function kh(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function Rh(e,t){Yr=Ui=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Lh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,eu(e,n)}}var $i={readContext:tt,useCallback:we,useContext:we,useEffect:we,useImperativeHandle:we,useInsertionEffect:we,useLayoutEffect:we,useMemo:we,useReducer:we,useRef:we,useState:we,useDebugValue:we,useDeferredValue:we,useTransition:we,useMutableSource:we,useSyncExternalStore:we,useId:we,unstable_isNewReconciler:!1},V0={readContext:tt,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:tt,useEffect:Zc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,yi(4194308,4,wh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return yi(4194308,4,e,t)},useInsertionEffect:function(e,t){return yi(4,2,e,t)},useMemo:function(e,t){var n=pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=_0.bind(null,ne,e),[r.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:Xc,useDebugValue:Cu,useDeferredValue:function(e){return pt().memoizedState=e},useTransition:function(){var e=Xc(!1),t=e[0];return e=j0.bind(null,e[1]),pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ne,o=pt();if(J){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),me===null)throw Error(R(349));jn&30||fh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Zc(hh.bind(null,r,i,e),[e]),r.flags|=2048,wo(9,ph.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=pt(),t=me.identifierPrefix;if(J){var n=Ct,r=Pt;n=(r&~(1<<32-lt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=yo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=M0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},D0={readContext:tt,useCallback:Ph,useContext:tt,useEffect:Pu,useImperativeHandle:Sh,useInsertionEffect:yh,useLayoutEffect:xh,useMemo:Ch,useReducer:ia,useRef:vh,useState:function(){return ia(xo)},useDebugValue:Cu,useDeferredValue:function(e){var t=nt();return Eh(t,de.memoizedState,e)},useTransition:function(){var e=ia(xo)[0],t=nt().memoizedState;return[e,t]},useMutableSource:ch,useSyncExternalStore:dh,useId:Th,unstable_isNewReconciler:!1},O0={readContext:tt,useCallback:Ph,useContext:tt,useEffect:Pu,useImperativeHandle:Sh,useInsertionEffect:yh,useLayoutEffect:xh,useMemo:Ch,useReducer:sa,useRef:vh,useState:function(){return sa(xo)},useDebugValue:Cu,useDeferredValue:function(e){var t=nt();return de===null?t.memoizedState=e:Eh(t,de.memoizedState,e)},useTransition:function(){var e=sa(xo)[0],t=nt().memoizedState;return[e,t]},useMutableSource:ch,useSyncExternalStore:dh,useId:Th,unstable_isNewReconciler:!1};function it(e,t){if(e&&e.defaultProps){t=re({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ol(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:re({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var gs={isMounted:function(e){return(e=e._reactInternals)?On(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Zt(e),i=Tt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Yt(e,i,o),t!==null&&(ut(t,e,o,r),gi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Zt(e),i=Tt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Yt(e,i,o),t!==null&&(ut(t,e,o,r),gi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ne(),r=Zt(e),o=Tt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Yt(e,o,r),t!==null&&(ut(t,e,r,n),gi(t,e,r))}};function Jc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!fo(n,r)||!fo(o,i):!0}function Nh(e,t,n){var r=!1,o=on,i=t.contextType;return typeof i=="object"&&i!==null?i=tt(i):(o=Ve(t)?An:Ee.current,r=t.contextTypes,i=(r=r!=null)?pr(e,o):on),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=gs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function qc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&gs.enqueueReplaceState(t,t.state,null)}function il(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},mu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=tt(i):(i=Ve(t)?An:Ee.current,o.context=pr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ol(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&gs.enqueueReplaceState(o,o.state,null),zi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function vr(e,t){try{var n="",r=t;do n+=dy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function aa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function sl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var I0=typeof WeakMap=="function"?WeakMap:Map;function Ah(e,t,n){n=Tt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hi||(Hi=!0,gl=r),sl(e,t)},n}function Mh(e,t,n){n=Tt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){sl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){sl(e,t),typeof r!="function"&&(Xt===null?Xt=new Set([this]):Xt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function ed(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new I0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=J0.bind(null,e,t,n),t.then(e,e))}function td(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function nd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Tt(-1,1),t.tag=2,Yt(n,t,1))),n.lanes|=1),e)}var F0=_t.ReactCurrentOwner,_e=!1;function Le(e,t,n,r){t.child=e===null?sh(t,null,n,r):mr(t,e.child,n,r)}function rd(e,t,n,r,o){n=n.render;var i=t.ref;return cr(t,o),r=wu(e,t,n,r,i,o),n=Su(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(J&&n&&lu(t),t.flags|=1,Le(e,t,r,o),t.child)}function od(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Mu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,jh(e,t,i,r,o)):(e=Pi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:fo,n(s,r)&&e.ref===t.ref)return Mt(e,t,o)}return t.flags|=1,e=Jt(i,r),e.ref=t.ref,e.return=t,t.child=e}function jh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(fo(i,r)&&e.ref===t.ref)if(_e=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(_e=!0);else return t.lanes=e.lanes,Mt(e,t,o)}return al(e,t,n,r,o)}function _h(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(tr,ze),ze|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,G(tr,ze),ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,G(tr,ze),ze|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,G(tr,ze),ze|=r;return Le(e,t,o,n),t.child}function bh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function al(e,t,n,r,o){var i=Ve(n)?An:Ee.current;return i=pr(t,i),cr(t,o),n=wu(e,t,n,r,i,o),r=Su(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(J&&r&&lu(t),t.flags|=1,Le(e,t,n,o),t.child)}function id(e,t,n,r,o){if(Ve(n)){var i=!0;Vi(t)}else i=!1;if(cr(t,o),t.stateNode===null)xi(e,t),Nh(t,n,r),il(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=tt(u):(u=Ve(n)?An:Ee.current,u=pr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&qc(t,s,r,u),It=!1;var f=t.memoizedState;s.state=f,zi(t,r,s,o),l=t.memoizedState,a!==r||f!==l||be.current||It?(typeof c=="function"&&(ol(t,n,c,r),l=t.memoizedState),(a=It||Jc(t,n,a,r,f,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,lh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:it(t.type,a),s.props=u,d=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=tt(l):(l=Ve(n)?An:Ee.current,l=pr(t,l));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||f!==l)&&qc(t,s,r,l),It=!1,f=t.memoizedState,s.state=f,zi(t,r,s,o);var x=t.memoizedState;a!==d||f!==x||be.current||It?(typeof m=="function"&&(ol(t,n,m,r),x=t.memoizedState),(u=It||Jc(t,n,u,r,f,x,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return ll(e,t,n,r,i,o)}function ll(e,t,n,r,o,i){bh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Wc(t,n,!1),Mt(e,t,i);r=t.stateNode,F0.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=mr(t,e.child,null,i),t.child=mr(t,null,a,i)):Le(e,t,a,i),t.memoizedState=r.state,o&&Wc(t,n,!0),t.child}function Vh(e){var t=e.stateNode;t.pendingContext?$c(e,t.pendingContext,t.pendingContext!==t.context):t.context&&$c(e,t.context,!1),gu(e,t.containerInfo)}function sd(e,t,n,r,o){return hr(),cu(o),t.flags|=256,Le(e,t,n,r),t.child}var ul={dehydrated:null,treeContext:null,retryLane:0};function cl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Dh(e,t,n){var r=t.pendingProps,o=ee.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),G(ee,o&1),e===null)return nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=xs(s,r,0,null),e=Nn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=cl(n),t.memoizedState=ul,e):Eu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return z0(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Jt(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Jt(a,i):(i=Nn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?cl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=ul,r}return i=e.child,e=i.sibling,r=Jt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Eu(e,t){return t=xs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ei(e,t,n,r){return r!==null&&cu(r),mr(t,e.child,null,n),e=Eu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function z0(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=aa(Error(R(422))),ei(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=xs({mode:"visible",children:r.children},o,0,null),i=Nn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&mr(t,e.child,null,s),t.child.memoizedState=cl(s),t.memoizedState=ul,i);if(!(t.mode&1))return ei(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=aa(i,r,void 0),ei(e,t,s,r)}if(a=(s&e.childLanes)!==0,_e||a){if(r=me,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,At(e,o),ut(r,e,o,-1))}return Au(),r=aa(Error(R(421))),ei(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=q0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ue=Qt(o.nextSibling),$e=t,J=!0,at=null,e!==null&&(Ze[Je++]=Pt,Ze[Je++]=Ct,Ze[Je++]=Mn,Pt=e.id,Ct=e.overflow,Mn=t),t=Eu(t,r.children),t.flags|=4096,t)}function ad(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rl(e.return,t,n)}function la(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Oh(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Le(e,t,r.children,n),r=ee.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ad(e,n,t);else if(e.tag===19)ad(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(G(ee,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Bi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),la(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Bi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}la(t,!0,n,null,i);break;case"together":la(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function xi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_n|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function B0(e,t,n){switch(t.tag){case 3:Vh(t),hr();break;case 5:uh(t);break;case 1:Ve(t.type)&&Vi(t);break;case 4:gu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;G(Ii,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(G(ee,ee.current&1),t.flags|=128,null):n&t.child.childLanes?Dh(e,t,n):(G(ee,ee.current&1),e=Mt(e,t,n),e!==null?e.sibling:null);G(ee,ee.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Oh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),G(ee,ee.current),r)break;return null;case 22:case 23:return t.lanes=0,_h(e,t,n)}return Mt(e,t,n)}var Ih,dl,Fh,zh;Ih=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};dl=function(){};Fh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Tn(vt.current);var i=null;switch(n){case"input":o=_a(e,o),r=_a(e,r),i=[];break;case"select":o=re({},o,{value:void 0}),r=re({},r,{value:void 0}),i=[];break;case"textarea":o=Da(e,o),r=Da(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=_i)}Ia(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(oo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(oo.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Y("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};zh=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!J)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Se(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function U0(e,t,n){var r=t.pendingProps;switch(uu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Se(t),null;case 1:return Ve(t.type)&&bi(),Se(t),null;case 3:return r=t.stateNode,gr(),X(be),X(Ee),yu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Jo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,at!==null&&(xl(at),at=null))),dl(e,t),Se(t),null;case 5:vu(t);var o=Tn(vo.current);if(n=t.type,e!==null&&t.stateNode!=null)Fh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Se(t),null}if(e=Tn(vt.current),Jo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[mt]=t,r[mo]=i,e=(t.mode&1)!==0,n){case"dialog":Y("cancel",r),Y("close",r);break;case"iframe":case"object":case"embed":Y("load",r);break;case"video":case"audio":for(o=0;o<Ur.length;o++)Y(Ur[o],r);break;case"source":Y("error",r);break;case"img":case"image":case"link":Y("error",r),Y("load",r);break;case"details":Y("toggle",r);break;case"input":gc(r,i),Y("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Y("invalid",r);break;case"textarea":yc(r,i),Y("invalid",r)}Ia(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Zo(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Zo(r.textContent,a,e),o=["children",""+a]):oo.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Y("scroll",r)}switch(n){case"input":$o(r),vc(r,i,!0);break;case"textarea":$o(r),xc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=_i)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=hp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[mt]=t,e[mo]=r,Ih(e,t,!1,!1),t.stateNode=e;e:{switch(s=Fa(n,r),n){case"dialog":Y("cancel",e),Y("close",e),o=r;break;case"iframe":case"object":case"embed":Y("load",e),o=r;break;case"video":case"audio":for(o=0;o<Ur.length;o++)Y(Ur[o],e);o=r;break;case"source":Y("error",e),o=r;break;case"img":case"image":case"link":Y("error",e),Y("load",e),o=r;break;case"details":Y("toggle",e),o=r;break;case"input":gc(e,r),o=_a(e,r),Y("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=re({},r,{value:void 0}),Y("invalid",e);break;case"textarea":yc(e,r),o=Da(e,r),Y("invalid",e);break;default:o=r}Ia(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?vp(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&mp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&io(e,l):typeof l=="number"&&io(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(oo.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Y("scroll",e):l!=null&&Ql(e,i,l,s))}switch(n){case"input":$o(e),vc(e,r,!1);break;case"textarea":$o(e),xc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+rn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?sr(e,!!r.multiple,i,!1):r.defaultValue!=null&&sr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=_i)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Se(t),null;case 6:if(e&&t.stateNode!=null)zh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Tn(vo.current),Tn(vt.current),Jo(t)){if(r=t.stateNode,n=t.memoizedProps,r[mt]=t,(i=r.nodeValue!==n)&&(e=$e,e!==null))switch(e.tag){case 3:Zo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Zo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[mt]=t,t.stateNode=r}return Se(t),null;case 13:if(X(ee),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(J&&Ue!==null&&t.mode&1&&!(t.flags&128))oh(),hr(),t.flags|=98560,i=!1;else if(i=Jo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[mt]=t}else hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Se(t),i=!1}else at!==null&&(xl(at),at=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ee.current&1?pe===0&&(pe=3):Au())),t.updateQueue!==null&&(t.flags|=4),Se(t),null);case 4:return gr(),dl(e,t),e===null&&po(t.stateNode.containerInfo),Se(t),null;case 10:return pu(t.type._context),Se(t),null;case 17:return Ve(t.type)&&bi(),Se(t),null;case 19:if(X(ee),i=t.memoizedState,i===null)return Se(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)_r(i,!1);else{if(pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Bi(e),s!==null){for(t.flags|=128,_r(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return G(ee,ee.current&1|2),t.child}e=e.sibling}i.tail!==null&&le()>yr&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304)}else{if(!r)if(e=Bi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!J)return Se(t),null}else 2*le()-i.renderingStartTime>yr&&n!==1073741824&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=le(),t.sibling=null,n=ee.current,G(ee,r?n&1|2:n&1),t):(Se(t),null);case 22:case 23:return Nu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ze&1073741824&&(Se(t),t.subtreeFlags&6&&(t.flags|=8192)):Se(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function $0(e,t){switch(uu(t),t.tag){case 1:return Ve(t.type)&&bi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return gr(),X(be),X(Ee),yu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return vu(t),null;case 13:if(X(ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(ee),null;case 4:return gr(),null;case 10:return pu(t.type._context),null;case 22:case 23:return Nu(),null;case 24:return null;default:return null}}var ti=!1,Ce=!1,W0=typeof WeakSet=="function"?WeakSet:Set,A=null;function er(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(e,t,r)}else n.current=null}function fl(e,t,n){try{n()}catch(r){oe(e,t,r)}}var ld=!1;function H0(e,t){if(Ya=Ai,e=Hp(),au(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var m;d!==n||o!==0&&d.nodeType!==3||(a=s+o),d!==i||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)f=d,d=m;for(;;){if(d===e)break t;if(f===n&&++u===o&&(a=s),f===i&&++c===r&&(l=s),(m=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xa={focusedElem:e,selectionRange:n},Ai=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,S=x.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?y:it(t.type,y),S);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(w){oe(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return x=ld,ld=!1,x}function Xr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&fl(t,n,i)}o=o.next}while(o!==r)}}function vs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Bh(e){var t=e.alternate;t!==null&&(e.alternate=null,Bh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[mt],delete t[mo],delete t[qa],delete t[R0],delete t[L0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Uh(e){return e.tag===5||e.tag===3||e.tag===4}function ud(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Uh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=_i));else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ml(e,t,n),e=e.sibling;e!==null;)ml(e,t,n),e=e.sibling}var ge=null,st=!1;function bt(e,t,n){for(n=n.child;n!==null;)$h(e,t,n),n=n.sibling}function $h(e,t,n){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(us,n)}catch{}switch(n.tag){case 5:Ce||er(n,t);case 6:var r=ge,o=st;ge=null,bt(e,t,n),ge=r,st=o,ge!==null&&(st?(e=ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ge.removeChild(n.stateNode));break;case 18:ge!==null&&(st?(e=ge,n=n.stateNode,e.nodeType===8?ta(e.parentNode,n):e.nodeType===1&&ta(e,n),uo(e)):ta(ge,n.stateNode));break;case 4:r=ge,o=st,ge=n.stateNode.containerInfo,st=!0,bt(e,t,n),ge=r,st=o;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&fl(n,t,s),o=o.next}while(o!==r)}bt(e,t,n);break;case 1:if(!Ce&&(er(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){oe(n,t,a)}bt(e,t,n);break;case 21:bt(e,t,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,bt(e,t,n),Ce=r):bt(e,t,n);break;default:bt(e,t,n)}}function cd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new W0),t.forEach(function(r){var o=e1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function rt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ge=a.stateNode,st=!1;break e;case 3:ge=a.stateNode.containerInfo,st=!0;break e;case 4:ge=a.stateNode.containerInfo,st=!0;break e}a=a.return}if(ge===null)throw Error(R(160));$h(i,s,o),ge=null,st=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){oe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wh(t,e),t=t.sibling}function Wh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(rt(t,e),ft(e),r&4){try{Xr(3,e,e.return),vs(3,e)}catch(y){oe(e,e.return,y)}try{Xr(5,e,e.return)}catch(y){oe(e,e.return,y)}}break;case 1:rt(t,e),ft(e),r&512&&n!==null&&er(n,n.return);break;case 5:if(rt(t,e),ft(e),r&512&&n!==null&&er(n,n.return),e.flags&32){var o=e.stateNode;try{io(o,"")}catch(y){oe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&fp(o,i),Fa(a,s);var u=Fa(a,i);for(s=0;s<l.length;s+=2){var c=l[s],d=l[s+1];c==="style"?vp(o,d):c==="dangerouslySetInnerHTML"?mp(o,d):c==="children"?io(o,d):Ql(o,c,d,u)}switch(a){case"input":ba(o,i);break;case"textarea":pp(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?sr(o,!!i.multiple,m,!1):f!==!!i.multiple&&(i.defaultValue!=null?sr(o,!!i.multiple,i.defaultValue,!0):sr(o,!!i.multiple,i.multiple?[]:"",!1))}o[mo]=i}catch(y){oe(e,e.return,y)}}break;case 6:if(rt(t,e),ft(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){oe(e,e.return,y)}}break;case 3:if(rt(t,e),ft(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{uo(t.containerInfo)}catch(y){oe(e,e.return,y)}break;case 4:rt(t,e),ft(e);break;case 13:rt(t,e),ft(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Ru=le())),r&4&&cd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ce=(u=Ce)||c,rt(t,e),Ce=u):rt(t,e),ft(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(A=e,c=e.child;c!==null;){for(d=A=c;A!==null;){switch(f=A,m=f.child,f.tag){case 0:case 11:case 14:case 15:Xr(4,f,f.return);break;case 1:er(f,f.return);var x=f.stateNode;if(typeof x.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){oe(r,n,y)}}break;case 5:er(f,f.return);break;case 22:if(f.memoizedState!==null){fd(d);continue}}m!==null?(m.return=f,A=m):fd(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=gp("display",s))}catch(y){oe(e,e.return,y)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(y){oe(e,e.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:rt(t,e),ft(e),r&4&&cd(e);break;case 21:break;default:rt(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Uh(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(io(o,""),r.flags&=-33);var i=ud(e);ml(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=ud(e);hl(e,a,s);break;default:throw Error(R(161))}}catch(l){oe(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function G0(e,t,n){A=e,Hh(e)}function Hh(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var o=A,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||ti;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||Ce;a=ti;var u=Ce;if(ti=s,(Ce=l)&&!u)for(A=o;A!==null;)s=A,l=s.child,s.tag===22&&s.memoizedState!==null?pd(o):l!==null?(l.return=s,A=l):pd(o);for(;i!==null;)A=i,Hh(i),i=i.sibling;A=o,ti=a,Ce=u}dd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,A=i):dd(e)}}function dd(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ce||vs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:it(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Yc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Yc(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&uo(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ce||t.flags&512&&pl(t)}catch(f){oe(t,t.return,f)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function fd(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function pd(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{vs(4,t)}catch(l){oe(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){oe(t,o,l)}}var i=t.return;try{pl(t)}catch(l){oe(t,i,l)}break;case 5:var s=t.return;try{pl(t)}catch(l){oe(t,s,l)}}}catch(l){oe(t,t.return,l)}if(t===e){A=null;break}var a=t.sibling;if(a!==null){a.return=t.return,A=a;break}A=t.return}}var K0=Math.ceil,Wi=_t.ReactCurrentDispatcher,Tu=_t.ReactCurrentOwner,et=_t.ReactCurrentBatchConfig,U=0,me=null,ce=null,ye=0,ze=0,tr=cn(0),pe=0,So=null,_n=0,ys=0,ku=0,Zr=null,je=null,Ru=0,yr=1/0,wt=null,Hi=!1,gl=null,Xt=null,ni=!1,$t=null,Gi=0,Jr=0,vl=null,wi=-1,Si=0;function Ne(){return U&6?le():wi!==-1?wi:wi=le()}function Zt(e){return e.mode&1?U&2&&ye!==0?ye&-ye:A0.transition!==null?(Si===0&&(Si=Np()),Si):(e=W,e!==0||(e=window.event,e=e===void 0?16:Dp(e.type)),e):1}function ut(e,t,n,r){if(50<Jr)throw Jr=0,vl=null,Error(R(185));No(e,n,r),(!(U&2)||e!==me)&&(e===me&&(!(U&2)&&(ys|=n),pe===4&&Bt(e,ye)),De(e,r),n===1&&U===0&&!(t.mode&1)&&(yr=le()+500,hs&&dn()))}function De(e,t){var n=e.callbackNode;Ay(e,t);var r=Ni(e,e===me?ye:0);if(r===0)n!==null&&Pc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Pc(n),t===1)e.tag===0?N0(hd.bind(null,e)):th(hd.bind(null,e)),T0(function(){!(U&6)&&dn()}),n=null;else{switch(Ap(r)){case 1:n=ql;break;case 4:n=Rp;break;case 16:n=Li;break;case 536870912:n=Lp;break;default:n=Li}n=qh(n,Gh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Gh(e,t){if(wi=-1,Si=0,U&6)throw Error(R(327));var n=e.callbackNode;if(dr()&&e.callbackNode!==n)return null;var r=Ni(e,e===me?ye:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ki(e,r);else{t=r;var o=U;U|=2;var i=Qh();(me!==e||ye!==t)&&(wt=null,yr=le()+500,Ln(e,t));do try{X0();break}catch(a){Kh(e,a)}while(1);fu(),Wi.current=i,U=o,ce!==null?t=0:(me=null,ye=0,t=pe)}if(t!==0){if(t===2&&(o=Wa(e),o!==0&&(r=o,t=yl(e,o))),t===1)throw n=So,Ln(e,0),Bt(e,r),De(e,le()),n;if(t===6)Bt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Q0(o)&&(t=Ki(e,r),t===2&&(i=Wa(e),i!==0&&(r=i,t=yl(e,i))),t===1))throw n=So,Ln(e,0),Bt(e,r),De(e,le()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:xn(e,je,wt);break;case 3:if(Bt(e,r),(r&130023424)===r&&(t=Ru+500-le(),10<t)){if(Ni(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ne(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ja(xn.bind(null,e,je,wt),t);break}xn(e,je,wt);break;case 4:if(Bt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-lt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*K0(r/1960))-r,10<r){e.timeoutHandle=Ja(xn.bind(null,e,je,wt),r);break}xn(e,je,wt);break;case 5:xn(e,je,wt);break;default:throw Error(R(329))}}}return De(e,le()),e.callbackNode===n?Gh.bind(null,e):null}function yl(e,t){var n=Zr;return e.current.memoizedState.isDehydrated&&(Ln(e,t).flags|=256),e=Ki(e,t),e!==2&&(t=je,je=n,t!==null&&xl(t)),e}function xl(e){je===null?je=e:je.push.apply(je,e)}function Q0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ct(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bt(e,t){for(t&=~ku,t&=~ys,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function hd(e){if(U&6)throw Error(R(327));dr();var t=Ni(e,0);if(!(t&1))return De(e,le()),null;var n=Ki(e,t);if(e.tag!==0&&n===2){var r=Wa(e);r!==0&&(t=r,n=yl(e,r))}if(n===1)throw n=So,Ln(e,0),Bt(e,t),De(e,le()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xn(e,je,wt),De(e,le()),null}function Lu(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(yr=le()+500,hs&&dn())}}function bn(e){$t!==null&&$t.tag===0&&!(U&6)&&dr();var t=U;U|=1;var n=et.transition,r=W;try{if(et.transition=null,W=1,e)return e()}finally{W=r,et.transition=n,U=t,!(U&6)&&dn()}}function Nu(){ze=tr.current,X(tr)}function Ln(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,E0(n)),ce!==null)for(n=ce.return;n!==null;){var r=n;switch(uu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&bi();break;case 3:gr(),X(be),X(Ee),yu();break;case 5:vu(r);break;case 4:gr();break;case 13:X(ee);break;case 19:X(ee);break;case 10:pu(r.type._context);break;case 22:case 23:Nu()}n=n.return}if(me=e,ce=e=Jt(e.current,null),ye=ze=t,pe=0,So=null,ku=ys=_n=0,je=Zr=null,En!==null){for(t=0;t<En.length;t++)if(n=En[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}En=null}return e}function Kh(e,t){do{var n=ce;try{if(fu(),vi.current=$i,Ui){for(var r=ne.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ui=!1}if(jn=0,he=de=ne=null,Yr=!1,yo=0,Tu.current=null,n===null||n.return===null){pe=1,So=t,ce=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=ye,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=td(s);if(m!==null){m.flags&=-257,nd(m,s,a,i,t),m.mode&1&&ed(i,u,t),t=m,l=u;var x=t.updateQueue;if(x===null){var y=new Set;y.add(l),t.updateQueue=y}else x.add(l);break e}else{if(!(t&1)){ed(i,u,t),Au();break e}l=Error(R(426))}}else if(J&&a.mode&1){var S=td(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),nd(S,s,a,i,t),cu(vr(l,a));break e}}i=l=vr(l,a),pe!==4&&(pe=2),Zr===null?Zr=[i]:Zr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=Ah(i,l,t);Qc(i,g);break e;case 1:a=l;var p=i.type,h=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Xt===null||!Xt.has(h)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=Mh(i,a,t);Qc(i,w);break e}}i=i.return}while(i!==null)}Xh(n)}catch(C){t=C,ce===n&&n!==null&&(ce=n=n.return);continue}break}while(1)}function Qh(){var e=Wi.current;return Wi.current=$i,e===null?$i:e}function Au(){(pe===0||pe===3||pe===2)&&(pe=4),me===null||!(_n&268435455)&&!(ys&268435455)||Bt(me,ye)}function Ki(e,t){var n=U;U|=2;var r=Qh();(me!==e||ye!==t)&&(wt=null,Ln(e,t));do try{Y0();break}catch(o){Kh(e,o)}while(1);if(fu(),U=n,Wi.current=r,ce!==null)throw Error(R(261));return me=null,ye=0,pe}function Y0(){for(;ce!==null;)Yh(ce)}function X0(){for(;ce!==null&&!Sy();)Yh(ce)}function Yh(e){var t=Jh(e.alternate,e,ze);e.memoizedProps=e.pendingProps,t===null?Xh(e):ce=t,Tu.current=null}function Xh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=$0(n,t),n!==null){n.flags&=32767,ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{pe=6,ce=null;return}}else if(n=U0(n,t,ze),n!==null){ce=n;return}if(t=t.sibling,t!==null){ce=t;return}ce=t=e}while(t!==null);pe===0&&(pe=5)}function xn(e,t,n){var r=W,o=et.transition;try{et.transition=null,W=1,Z0(e,t,n,r)}finally{et.transition=o,W=r}return null}function Z0(e,t,n,r){do dr();while($t!==null);if(U&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(My(e,i),e===me&&(ce=me=null,ye=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ni||(ni=!0,qh(Li,function(){return dr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=et.transition,et.transition=null;var s=W;W=1;var a=U;U|=4,Tu.current=null,H0(e,n),Wh(n,e),v0(Xa),Ai=!!Ya,Xa=Ya=null,e.current=n,G0(n),Py(),U=a,W=s,et.transition=i}else e.current=n;if(ni&&(ni=!1,$t=e,Gi=o),i=e.pendingLanes,i===0&&(Xt=null),Ty(n.stateNode),De(e,le()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hi)throw Hi=!1,e=gl,gl=null,e;return Gi&1&&e.tag!==0&&dr(),i=e.pendingLanes,i&1?e===vl?Jr++:(Jr=0,vl=e):Jr=0,dn(),null}function dr(){if($t!==null){var e=Ap(Gi),t=et.transition,n=W;try{if(et.transition=null,W=16>e?16:e,$t===null)var r=!1;else{if(e=$t,$t=null,Gi=0,U&6)throw Error(R(331));var o=U;for(U|=4,A=e.current;A!==null;){var i=A,s=i.child;if(A.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(A=u;A!==null;){var c=A;switch(c.tag){case 0:case 11:case 15:Xr(8,c,i)}var d=c.child;if(d!==null)d.return=c,A=d;else for(;A!==null;){c=A;var f=c.sibling,m=c.return;if(Bh(c),c===u){A=null;break}if(f!==null){f.return=m,A=f;break}A=m}}}var x=i.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var S=y.sibling;y.sibling=null,y=S}while(y!==null)}}A=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,A=s;else e:for(;A!==null;){if(i=A,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Xr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,A=g;break e}A=i.return}}var p=e.current;for(A=p;A!==null;){s=A;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,A=h;else e:for(s=p;A!==null;){if(a=A,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:vs(9,a)}}catch(C){oe(a,a.return,C)}if(a===s){A=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,A=w;break e}A=a.return}}if(U=o,dn(),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(us,e)}catch{}r=!0}return r}finally{W=n,et.transition=t}}return!1}function md(e,t,n){t=vr(n,t),t=Ah(e,t,1),e=Yt(e,t,1),t=Ne(),e!==null&&(No(e,1,t),De(e,t))}function oe(e,t,n){if(e.tag===3)md(e,e,n);else for(;t!==null;){if(t.tag===3){md(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Xt===null||!Xt.has(r))){e=vr(n,e),e=Mh(t,e,1),t=Yt(t,e,1),e=Ne(),t!==null&&(No(t,1,e),De(t,e));break}}t=t.return}}function J0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ne(),e.pingedLanes|=e.suspendedLanes&n,me===e&&(ye&n)===n&&(pe===4||pe===3&&(ye&130023424)===ye&&500>le()-Ru?Ln(e,0):ku|=n),De(e,t)}function Zh(e,t){t===0&&(e.mode&1?(t=Go,Go<<=1,!(Go&130023424)&&(Go=4194304)):t=1);var n=Ne();e=At(e,t),e!==null&&(No(e,t,n),De(e,n))}function q0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Zh(e,n)}function e1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),Zh(e,n)}var Jh;Jh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||be.current)_e=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return _e=!1,B0(e,t,n);_e=!!(e.flags&131072)}else _e=!1,J&&t.flags&1048576&&nh(t,Oi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;xi(e,t),e=t.pendingProps;var o=pr(t,Ee.current);cr(t,n),o=wu(null,t,r,e,o,n);var i=Su();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ve(r)?(i=!0,Vi(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,mu(t),o.updater=gs,t.stateNode=o,o._reactInternals=t,il(t,r,e,n),t=ll(null,t,r,!0,i,n)):(t.tag=0,J&&i&&lu(t),Le(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(xi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=n1(r),e=it(r,e),o){case 0:t=al(null,t,r,e,n);break e;case 1:t=id(null,t,r,e,n);break e;case 11:t=rd(null,t,r,e,n);break e;case 14:t=od(null,t,r,it(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:it(r,o),al(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:it(r,o),id(e,t,r,o,n);case 3:e:{if(Vh(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,lh(e,t),zi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=vr(Error(R(423)),t),t=sd(e,t,r,n,o);break e}else if(r!==o){o=vr(Error(R(424)),t),t=sd(e,t,r,n,o);break e}else for(Ue=Qt(t.stateNode.containerInfo.firstChild),$e=t,J=!0,at=null,n=sh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(hr(),r===o){t=Mt(e,t,n);break e}Le(e,t,r,n)}t=t.child}return t;case 5:return uh(t),e===null&&nl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Za(r,o)?s=null:i!==null&&Za(r,i)&&(t.flags|=32),bh(e,t),Le(e,t,s,n),t.child;case 6:return e===null&&nl(t),null;case 13:return Dh(e,t,n);case 4:return gu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=mr(t,null,r,n):Le(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:it(r,o),rd(e,t,r,o,n);case 7:return Le(e,t,t.pendingProps,n),t.child;case 8:return Le(e,t,t.pendingProps.children,n),t.child;case 12:return Le(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,G(Ii,r._currentValue),r._currentValue=s,i!==null)if(ct(i.value,s)){if(i.children===o.children&&!be.current){t=Mt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Tt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),rl(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),rl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Le(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,cr(t,n),o=tt(o),r=r(o),t.flags|=1,Le(e,t,r,n),t.child;case 14:return r=t.type,o=it(r,t.pendingProps),o=it(r.type,o),od(e,t,r,o,n);case 15:return jh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:it(r,o),xi(e,t),t.tag=1,Ve(r)?(e=!0,Vi(t)):e=!1,cr(t,n),Nh(t,r,o),il(t,r,o,n),ll(null,t,r,!0,e,n);case 19:return Oh(e,t,n);case 22:return _h(e,t,n)}throw Error(R(156,t.tag))};function qh(e,t){return kp(e,t)}function t1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function qe(e,t,n,r){return new t1(e,t,n,r)}function Mu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function n1(e){if(typeof e=="function")return Mu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Xl)return 11;if(e===Zl)return 14}return 2}function Jt(e,t){var n=e.alternate;return n===null?(n=qe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Pi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Mu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Hn:return Nn(n.children,o,i,t);case Yl:s=8,o|=8;break;case Na:return e=qe(12,n,t,o|2),e.elementType=Na,e.lanes=i,e;case Aa:return e=qe(13,n,t,o),e.elementType=Aa,e.lanes=i,e;case Ma:return e=qe(19,n,t,o),e.elementType=Ma,e.lanes=i,e;case up:return xs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ap:s=10;break e;case lp:s=9;break e;case Xl:s=11;break e;case Zl:s=14;break e;case Ot:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=qe(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Nn(e,t,n,r){return e=qe(7,e,r,t),e.lanes=n,e}function xs(e,t,n,r){return e=qe(22,e,r,t),e.elementType=up,e.lanes=n,e.stateNode={isHidden:!1},e}function ua(e,t,n){return e=qe(6,e,null,t),e.lanes=n,e}function ca(e,t,n){return t=qe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function r1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ws(0),this.expirationTimes=Ws(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ws(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,o,i,s,a,l){return e=new r1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=qe(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},mu(i),e}function o1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function em(e){if(!e)return on;e=e._reactInternals;e:{if(On(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Ve(n))return eh(e,n,t)}return t}function tm(e,t,n,r,o,i,s,a,l){return e=ju(n,r,!0,e,o,i,s,a,l),e.context=em(null),n=e.current,r=Ne(),o=Zt(n),i=Tt(r,o),i.callback=t??null,Yt(n,i,o),e.current.lanes=o,No(e,o,r),De(e,r),e}function ws(e,t,n,r){var o=t.current,i=Ne(),s=Zt(o);return n=em(n),t.context===null?t.context=n:t.pendingContext=n,t=Tt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Yt(o,t,s),e!==null&&(ut(e,o,s,i),gi(e,o,s)),s}function Qi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function gd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function _u(e,t){gd(e,t),(e=e.alternate)&&gd(e,t)}function i1(){return null}var nm=typeof reportError=="function"?reportError:function(e){console.error(e)};function bu(e){this._internalRoot=e}Ss.prototype.render=bu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));ws(e,t,null,null)};Ss.prototype.unmount=bu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;bn(function(){ws(null,e,null,null)}),t[Nt]=null}};function Ss(e){this._internalRoot=e}Ss.prototype.unstable_scheduleHydration=function(e){if(e){var t=_p();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zt.length&&t!==0&&t<zt[n].priority;n++);zt.splice(n,0,e),n===0&&Vp(e)}};function Vu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ps(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function vd(){}function s1(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Qi(s);i.call(u)}}var s=tm(t,r,e,0,null,!1,!1,"",vd);return e._reactRootContainer=s,e[Nt]=s.current,po(e.nodeType===8?e.parentNode:e),bn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Qi(l);a.call(u)}}var l=ju(e,0,!1,null,null,!1,!1,"",vd);return e._reactRootContainer=l,e[Nt]=l.current,po(e.nodeType===8?e.parentNode:e),bn(function(){ws(t,l,n,r)}),l}function Cs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Qi(s);a.call(l)}}ws(t,s,e,o)}else s=s1(n,t,e,o,r);return Qi(s)}Mp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Br(t.pendingLanes);n!==0&&(eu(t,n|1),De(t,le()),!(U&6)&&(yr=le()+500,dn()))}break;case 13:bn(function(){var r=At(e,1);if(r!==null){var o=Ne();ut(r,e,1,o)}}),_u(e,1)}};tu=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var n=Ne();ut(t,e,134217728,n)}_u(e,134217728)}};jp=function(e){if(e.tag===13){var t=Zt(e),n=At(e,t);if(n!==null){var r=Ne();ut(n,e,t,r)}_u(e,t)}};_p=function(){return W};bp=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};Ba=function(e,t,n){switch(t){case"input":if(ba(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ps(r);if(!o)throw Error(R(90));dp(r),ba(r,o)}}}break;case"textarea":pp(e,n);break;case"select":t=n.value,t!=null&&sr(e,!!n.multiple,t,!1)}};wp=Lu;Sp=bn;var a1={usingClientEntryPoint:!1,Events:[Mo,Yn,ps,yp,xp,Lu]},br={findFiberByHostInstance:Cn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},l1={bundleType:br.bundleType,version:br.version,rendererPackageName:br.rendererPackageName,rendererConfig:br.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ep(e),e===null?null:e.stateNode},findFiberByHostInstance:br.findFiberByHostInstance||i1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ri.isDisabled&&ri.supportsFiber)try{us=ri.inject(l1),gt=ri}catch{}}Ge.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=a1;Ge.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Vu(t))throw Error(R(200));return o1(e,t,null,n)};Ge.createRoot=function(e,t){if(!Vu(e))throw Error(R(299));var n=!1,r="",o=nm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ju(e,1,!1,null,null,n,!1,r,o),e[Nt]=t.current,po(e.nodeType===8?e.parentNode:e),new bu(t)};Ge.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Ep(t),e=e===null?null:e.stateNode,e};Ge.flushSync=function(e){return bn(e)};Ge.hydrate=function(e,t,n){if(!Ps(t))throw Error(R(200));return Cs(null,e,t,!0,n)};Ge.hydrateRoot=function(e,t,n){if(!Vu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=nm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=tm(t,null,e,1,n??null,o,!1,i,s),e[Nt]=t.current,po(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ss(t)};Ge.render=function(e,t,n){if(!Ps(t))throw Error(R(200));return Cs(null,e,t,!1,n)};Ge.unmountComponentAtNode=function(e){if(!Ps(e))throw Error(R(40));return e._reactRootContainer?(bn(function(){Cs(null,null,e,!1,function(){e._reactRootContainer=null,e[Nt]=null})}),!0):!1};Ge.unstable_batchedUpdates=Lu;Ge.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ps(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Cs(e,t,n,!1,r)};Ge.version="18.3.1-next-f1338f8080-20240426";function rm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(rm)}catch(e){console.error(e)}}rm(),rp.exports=Ge;var Es=rp.exports;const u1=Wf(Es);var yd=Es;Ra.createRoot=yd.createRoot,Ra.hydrateRoot=yd.hydrateRoot;const c1="modulepreload",d1=function(e){return"/"+e},xd={},dt=function(t,n,r){if(!n||n.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=d1(i),i in xd)return;xd[i]=!0;const s=i.endsWith(".css"),a=s?'[rel="stylesheet"]':"";if(!!r)for(let c=o.length-1;c>=0;c--){const d=o[c];if(d.href===i&&(!s||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${a}`))return;const u=document.createElement("link");if(u.rel=s?"stylesheet":c1,s||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),s)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Po(){return Po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Po.apply(this,arguments)}var Wt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Wt||(Wt={}));const wd="popstate";function f1(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return wl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Yi(o)}return h1(t,n,null,e)}function se(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function om(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function p1(){return Math.random().toString(36).substr(2,8)}function Sd(e,t){return{usr:e.state,key:e.key,idx:t}}function wl(e,t,n,r){return n===void 0&&(n=null),Po({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Tr(t):t,{state:n,key:t&&t.key||r||p1()})}function Yi(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Tr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function h1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=Wt.Pop,l=null,u=c();u==null&&(u=0,s.replaceState(Po({},s.state,{idx:u}),""));function c(){return(s.state||{idx:null}).idx}function d(){a=Wt.Pop;let S=c(),g=S==null?null:S-u;u=S,l&&l({action:a,location:y.location,delta:g})}function f(S,g){a=Wt.Push;let p=wl(y.location,S,g);n&&n(p,S),u=c()+1;let h=Sd(p,u),w=y.createHref(p);try{s.pushState(h,"",w)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(w)}i&&l&&l({action:a,location:y.location,delta:1})}function m(S,g){a=Wt.Replace;let p=wl(y.location,S,g);n&&n(p,S),u=c();let h=Sd(p,u),w=y.createHref(p);s.replaceState(h,"",w),i&&l&&l({action:a,location:y.location,delta:0})}function x(S){let g=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof S=="string"?S:Yi(S);return p=p.replace(/ $/,"%20"),se(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let y={get action(){return a},get location(){return e(o,s)},listen(S){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(wd,d),l=S,()=>{o.removeEventListener(wd,d),l=null}},createHref(S){return t(o,S)},createURL:x,encodeLocation(S){let g=x(S);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(S){return s.go(S)}};return y}var Pd;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Pd||(Pd={}));function m1(e,t,n){return n===void 0&&(n="/"),g1(e,t,n,!1)}function g1(e,t,n,r){let o=typeof t=="string"?Tr(t):t,i=xr(o.pathname||"/",n);if(i==null)return null;let s=im(e);v1(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let u=L1(i);a=k1(s[l],u,r)}return a}function im(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(se(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=qt([r,l.relativePath]),c=n.concat(l);i.children&&i.children.length>0&&(se(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),im(i.children,t,c,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:E1(u,i.index),routesMeta:c})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of sm(i.path))o(i,s,l)}),t}function sm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=sm(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function v1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:T1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const y1=/^:[\w-]+$/,x1=3,w1=2,S1=1,P1=10,C1=-2,Cd=e=>e==="*";function E1(e,t){let n=e.split("/"),r=n.length;return n.some(Cd)&&(r+=C1),t&&(r+=w1),n.filter(o=>!Cd(o)).reduce((o,i)=>o+(y1.test(i)?x1:i===""?S1:P1),r)}function T1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function k1(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,o={},i="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=i==="/"?t:t.slice(i.length)||"/",d=Xi({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),f=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Xi({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(o,d.params),s.push({params:o,pathname:qt([i,d.pathname]),pathnameBase:j1(qt([i,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(i=qt([i,d.pathnameBase]))}return s}function Xi(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=R1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:m}=c;if(f==="*"){let y=a[d]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const x=a[d];return m&&!x?u[f]=void 0:u[f]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function R1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),om(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function L1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return om(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function xr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function N1(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Tr(e):e;return{pathname:n?n.startsWith("/")?n:A1(n,t):t,search:_1(r),hash:b1(o)}}function A1(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function da(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function M1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function am(e,t){let n=M1(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function lm(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Tr(e):(o=Po({},e),se(!o.pathname||!o.pathname.includes("?"),da("?","pathname","search",o)),se(!o.pathname||!o.pathname.includes("#"),da("#","pathname","hash",o)),se(!o.search||!o.search.includes("#"),da("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,a;if(s==null)a=n;else{let d=t.length-1;if(!r&&s.startsWith("..")){let f=s.split("/");for(;f[0]==="..";)f.shift(),d-=1;o.pathname=f.join("/")}a=d>=0?t[d]:"/"}let l=N1(o,a),u=s&&s!=="/"&&s.endsWith("/"),c=(i||s===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const qt=e=>e.join("/").replace(/\/\/+/g,"/"),j1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),_1=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,b1=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function V1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const um=["post","put","patch","delete"];new Set(um);const D1=["get",...um];new Set(D1);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Co(){return Co=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Co.apply(this,arguments)}const Ts=v.createContext(null),cm=v.createContext(null),fn=v.createContext(null),ks=v.createContext(null),In=v.createContext({outlet:null,matches:[],isDataRoute:!1}),dm=v.createContext(null);function O1(e,t){let{relative:n}=t===void 0?{}:t;_o()||se(!1);let{basename:r,navigator:o}=v.useContext(fn),{hash:i,pathname:s,search:a}=Rs(e,{relative:n}),l=s;return r!=="/"&&(l=s==="/"?r:qt([r,s])),o.createHref({pathname:l,search:a,hash:i})}function _o(){return v.useContext(ks)!=null}function kr(){return _o()||se(!1),v.useContext(ks).location}function fm(e){v.useContext(fn).static||v.useLayoutEffect(e)}function I1(){let{isDataRoute:e}=v.useContext(In);return e?Z1():F1()}function F1(){_o()||se(!1);let e=v.useContext(Ts),{basename:t,future:n,navigator:r}=v.useContext(fn),{matches:o}=v.useContext(In),{pathname:i}=kr(),s=JSON.stringify(am(o,n.v7_relativeSplatPath)),a=v.useRef(!1);return fm(()=>{a.current=!0}),v.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let d=lm(u,JSON.parse(s),i,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:qt([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,s,i,e])}function Rs(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=v.useContext(fn),{matches:o}=v.useContext(In),{pathname:i}=kr(),s=JSON.stringify(am(o,r.v7_relativeSplatPath));return v.useMemo(()=>lm(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function z1(e,t){return B1(e,t)}function B1(e,t,n,r){_o()||se(!1);let{navigator:o}=v.useContext(fn),{matches:i}=v.useContext(In),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=kr(),c;if(t){var d;let S=typeof t=="string"?Tr(t):t;l==="/"||(d=S.pathname)!=null&&d.startsWith(l)||se(!1),c=S}else c=u;let f=c.pathname||"/",m=f;if(l!=="/"){let S=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(S.length).join("/")}let x=m1(e,{pathname:m}),y=G1(x&&x.map(S=>Object.assign({},S,{params:Object.assign({},a,S.params),pathname:qt([l,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?l:qt([l,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),i,n,r);return t&&y?v.createElement(ks.Provider,{value:{location:Co({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Wt.Pop}},y):y}function U1(){let e=X1(),t=V1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},t),n?v.createElement("pre",{style:o},n):null,i)}const $1=v.createElement(U1,null);class W1 extends v.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?v.createElement(In.Provider,{value:this.props.routeContext},v.createElement(dm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function H1(e){let{routeContext:t,match:n,children:r}=e,o=v.useContext(Ts);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),v.createElement(In.Provider,{value:t},r)}function G1(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let c=s.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||se(!1),s=s.slice(0,Math.min(s.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<s.length;c++){let d=s[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:m}=n,x=d.route.loader&&f[d.route.id]===void 0&&(!m||m[d.route.id]===void 0);if(d.route.lazy||x){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((c,d,f)=>{let m,x=!1,y=null,S=null;n&&(m=a&&d.route.id?a[d.route.id]:void 0,y=d.route.errorElement||$1,l&&(u<0&&f===0?(J1("route-fallback",!1),x=!0,S=null):u===f&&(x=!0,S=d.route.hydrateFallbackElement||null)));let g=t.concat(s.slice(0,f+1)),p=()=>{let h;return m?h=y:x?h=S:d.route.Component?h=v.createElement(d.route.Component,null):d.route.element?h=d.route.element:h=c,v.createElement(H1,{match:d,routeContext:{outlet:c,matches:g,isDataRoute:n!=null},children:h})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?v.createElement(W1,{location:n.location,revalidation:n.revalidation,component:y,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var pm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(pm||{}),Zi=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Zi||{});function K1(e){let t=v.useContext(Ts);return t||se(!1),t}function Q1(e){let t=v.useContext(cm);return t||se(!1),t}function Y1(e){let t=v.useContext(In);return t||se(!1),t}function hm(e){let t=Y1(),n=t.matches[t.matches.length-1];return n.route.id||se(!1),n.route.id}function X1(){var e;let t=v.useContext(dm),n=Q1(Zi.UseRouteError),r=hm(Zi.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Z1(){let{router:e}=K1(pm.UseNavigateStable),t=hm(Zi.UseNavigateStable),n=v.useRef(!1);return fm(()=>{n.current=!0}),v.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,Co({fromRouteId:t},i)))},[e,t])}const Ed={};function J1(e,t,n){!t&&!Ed[e]&&(Ed[e]=!0)}function q1(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Ye(e){se(!1)}function ex(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Wt.Pop,navigator:i,static:s=!1,future:a}=e;_o()&&se(!1);let l=t.replace(/^\/*/,"/"),u=v.useMemo(()=>({basename:l,navigator:i,static:s,future:Co({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=Tr(r));let{pathname:c="/",search:d="",hash:f="",state:m=null,key:x="default"}=r,y=v.useMemo(()=>{let S=xr(c,l);return S==null?null:{location:{pathname:S,search:d,hash:f,state:m,key:x},navigationType:o}},[l,c,d,f,m,x,o]);return y==null?null:v.createElement(fn.Provider,{value:u},v.createElement(ks.Provider,{children:n,value:y}))}function tx(e){let{children:t,location:n}=e;return z1(Sl(t),n)}new Promise(()=>{});function Sl(e,t){t===void 0&&(t=[]);let n=[];return v.Children.forEach(e,(r,o)=>{if(!v.isValidElement(r))return;let i=[...t,o];if(r.type===v.Fragment){n.push.apply(n,Sl(r.props.children,i));return}r.type!==Ye&&se(!1),!r.props.index||!r.props.children||se(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Sl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ji.apply(this,arguments)}function mm(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function nx(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function rx(e,t){return e.button===0&&(!t||t==="_self")&&!nx(e)}const ox=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ix=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],sx="6";try{window.__reactRouterVersion=sx}catch{}const ax=v.createContext({isTransitioning:!1}),lx="startTransition",Td=tp[lx];function ux(e){let{basename:t,children:n,future:r,window:o}=e,i=v.useRef();i.current==null&&(i.current=f1({window:o,v5Compat:!0}));let s=i.current,[a,l]=v.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},c=v.useCallback(d=>{u&&Td?Td(()=>l(d)):l(d)},[l,u]);return v.useLayoutEffect(()=>s.listen(c),[s,c]),v.useEffect(()=>q1(r),[r]),v.createElement(ex,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}const cx=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",dx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ht=v.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:a,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,f=mm(t,ox),{basename:m}=v.useContext(fn),x,y=!1;if(typeof u=="string"&&dx.test(u)&&(x=u,cx))try{let h=new URL(window.location.href),w=u.startsWith("//")?new URL(h.protocol+u):new URL(u),C=xr(w.pathname,m);w.origin===h.origin&&C!=null?u=C+w.search+w.hash:y=!0}catch{}let S=O1(u,{relative:o}),g=px(u,{replace:s,state:a,target:l,preventScrollReset:c,relative:o,viewTransition:d});function p(h){r&&r(h),h.defaultPrevented||g(h)}return v.createElement("a",Ji({},f,{href:x||S,onClick:y||i?r:p,ref:n,target:l}))}),kd=v.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:o=!1,className:i="",end:s=!1,style:a,to:l,viewTransition:u,children:c}=t,d=mm(t,ix),f=Rs(l,{relative:d.relative}),m=kr(),x=v.useContext(cm),{navigator:y,basename:S}=v.useContext(fn),g=x!=null&&hx(f)&&u===!0,p=y.encodeLocation?y.encodeLocation(f).pathname:f.pathname,h=m.pathname,w=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;o||(h=h.toLowerCase(),w=w?w.toLowerCase():null,p=p.toLowerCase()),w&&S&&(w=xr(w,S)||w);const C=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let T=h===p||!s&&h.startsWith(p)&&h.charAt(C)==="/",E=w!=null&&(w===p||!s&&w.startsWith(p)&&w.charAt(p.length)==="/"),k={isActive:T,isPending:E,isTransitioning:g},N=T?r:void 0,M;typeof i=="function"?M=i(k):M=[i,T?"active":null,E?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let $=typeof a=="function"?a(k):a;return v.createElement(ht,Ji({},d,{"aria-current":N,className:M,ref:n,style:$,to:l,viewTransition:u}),typeof c=="function"?c(k):c)});var Pl;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Pl||(Pl={}));var Rd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Rd||(Rd={}));function fx(e){let t=v.useContext(Ts);return t||se(!1),t}function px(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:a}=t===void 0?{}:t,l=I1(),u=kr(),c=Rs(e,{relative:s});return v.useCallback(d=>{if(rx(d,n)){d.preventDefault();let f=r!==void 0?r:Yi(u)===Yi(c);l(e,{replace:f,state:o,preventScrollReset:i,relative:s,viewTransition:a})}},[u,l,c,r,o,n,e,i,s,a])}function hx(e,t){t===void 0&&(t={});let n=v.useContext(ax);n==null&&se(!1);let{basename:r}=fx(Pl.useViewTransitionState),o=Rs(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=xr(n.currentLocation.pathname,r)||n.currentLocation.pathname,s=xr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Xi(o.pathname,s)!=null||Xi(o.pathname,i)!=null}function gm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=gm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=gm(e))&&(r&&(r+=" "),r+=t);return r}function mx(){for(var e=0,t,n,r="";e<arguments.length;)(t=arguments[e++])&&(n=ym(t))&&(r&&(r+=" "),r+=n);return r}function ym(e){if(typeof e=="string")return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=ym(e[r]))&&(n&&(n+=" "),n+=t);return n}var Du="-";function gx(e){var t=yx(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,o=r===void 0?{}:r;function i(a){var l=a.split(Du);return l[0]===""&&l.length!==1&&l.shift(),xm(l,t)||vx(a)}function s(a,l){var u=n[a]||[];return l&&o[a]?[].concat(u,o[a]):u}return{getClassGroupId:i,getConflictingClassGroupIds:s}}function xm(e,t){var s;if(e.length===0)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),o=r?xm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length!==0){var i=e.join(Du);return(s=t.validators.find(function(a){var l=a.validator;return l(i)}))==null?void 0:s.classGroupId}}var Ld=/^\[(.+)\]$/;function vx(e){if(Ld.test(e)){var t=Ld.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function yx(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},o=wx(Object.entries(e.classGroups),n);return o.forEach(function(i){var s=i[0],a=i[1];Cl(a,r,s,t)}),r}function Cl(e,t,n,r){e.forEach(function(o){if(typeof o=="string"){var i=o===""?t:Nd(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(xx(o)){Cl(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(function(s){var a=s[0],l=s[1];Cl(l,Nd(t,a),n,r)})})}function Nd(e,t){var n=e;return t.split(Du).forEach(function(r){n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function xx(e){return e.isThemeGetter}function wx(e,t){return t?e.map(function(n){var r=n[0],o=n[1],i=o.map(function(s){return typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(function(a){var l=a[0],u=a[1];return[t+l,u]})):s});return[r,i]}):e}function Sx(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get:function(s){var a=n.get(s);if(a!==void 0)return a;if((a=r.get(s))!==void 0)return o(s,a),a},set:function(s,a){n.has(s)?n.set(s,a):o(s,a)}}}var wm="!";function Px(e){var t=e.separator||":",n=t.length===1,r=t[0],o=t.length;return function(s){for(var a=[],l=0,u=0,c,d=0;d<s.length;d++){var f=s[d];if(l===0){if(f===r&&(n||s.slice(d,d+o)===t)){a.push(s.slice(u,d)),u=d+o;continue}if(f==="/"){c=d;continue}}f==="["?l++:f==="]"&&l--}var m=a.length===0?s:s.substring(u),x=m.startsWith(wm),y=x?m.substring(1):m,S=c&&c>u?c-u:void 0;return{modifiers:a,hasImportantModifier:x,baseClassName:y,maybePostfixModifierPosition:S}}}function Cx(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(r){var o=r[0]==="[";o?(t.push.apply(t,n.sort().concat([r])),n=[]):n.push(r)}),t.push.apply(t,n.sort()),t}function Ex(e){return{cache:Sx(e.cacheSize),splitModifiers:Px(e),...gx(e)}}var Tx=/\s+/;function kx(e,t){var n=t.splitModifiers,r=t.getClassGroupId,o=t.getConflictingClassGroupIds,i=new Set;return e.trim().split(Tx).map(function(s){var a=n(s),l=a.modifiers,u=a.hasImportantModifier,c=a.baseClassName,d=a.maybePostfixModifierPosition,f=r(d?c.substring(0,d):c),m=!!d;if(!f){if(!d)return{isTailwindClass:!1,originalClassName:s};if(f=r(c),!f)return{isTailwindClass:!1,originalClassName:s};m=!1}var x=Cx(l).join(":"),y=u?x+wm:x;return{isTailwindClass:!0,modifierId:y,classGroupId:f,originalClassName:s,hasPostfixModifier:m}}).reverse().filter(function(s){if(!s.isTailwindClass)return!0;var a=s.modifierId,l=s.classGroupId,u=s.hasPostfixModifier,c=a+l;return i.has(c)?!1:(i.add(c),o(l,u).forEach(function(d){return i.add(a+d)}),!0)}).reverse().map(function(s){return s.originalClassName}).join(" ")}function Rx(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,o,i,s=a;function a(u){var c=t[0],d=t.slice(1),f=d.reduce(function(m,x){return x(m)},c());return r=Ex(f),o=r.cache.get,i=r.cache.set,s=l,l(u)}function l(u){var c=o(u);if(c)return c;var d=kx(u,r);return i(u,d),d}return function(){return s(mx.apply(null,arguments))}}function Q(e){var t=function(r){return r[e]||[]};return t.isThemeGetter=!0,t}var Sm=/^\[(?:([a-z-]+):)?(.+)\]$/i,Lx=/^\d+\/\d+$/,Nx=new Set(["px","full","screen"]),Ax=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Mx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,jx=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function ot(e){return kn(e)||Nx.has(e)||Lx.test(e)||El(e)}function El(e){return Fn(e,"length",Ix)}function _x(e){return Fn(e,"size",Pm)}function bx(e){return Fn(e,"position",Pm)}function Vx(e){return Fn(e,"url",Fx)}function oi(e){return Fn(e,"number",kn)}function kn(e){return!Number.isNaN(Number(e))}function Dx(e){return e.endsWith("%")&&kn(e.slice(0,-1))}function Vr(e){return Ad(e)||Fn(e,"number",Ad)}function F(e){return Sm.test(e)}function Dr(){return!0}function Vt(e){return Ax.test(e)}function Ox(e){return Fn(e,"",zx)}function Fn(e,t,n){var r=Sm.exec(e);return r?r[1]?r[1]===t:n(r[2]):!1}function Ix(e){return Mx.test(e)}function Pm(){return!1}function Fx(e){return e.startsWith("url(")}function Ad(e){return Number.isInteger(Number(e))}function zx(e){return jx.test(e)}function Bx(){var e=Q("colors"),t=Q("spacing"),n=Q("blur"),r=Q("brightness"),o=Q("borderColor"),i=Q("borderRadius"),s=Q("borderSpacing"),a=Q("borderWidth"),l=Q("contrast"),u=Q("grayscale"),c=Q("hueRotate"),d=Q("invert"),f=Q("gap"),m=Q("gradientColorStops"),x=Q("gradientColorStopPositions"),y=Q("inset"),S=Q("margin"),g=Q("opacity"),p=Q("padding"),h=Q("saturate"),w=Q("scale"),C=Q("sepia"),T=Q("skew"),E=Q("space"),k=Q("translate"),N=function(){return["auto","contain","none"]},M=function(){return["auto","hidden","clip","visible","scroll"]},$=function(){return["auto",F,t]},V=function(){return[F,t]},q=function(){return["",ot]},j=function(){return["auto",kn,F]},Z=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},z=function(){return["solid","dashed","dotted","double","none"]},ae=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},L=function(){return["start","end","center","between","around","evenly","stretch"]},_=function(){return["","0",F]},O=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},b=function(){return[kn,oi]},B=function(){return[kn,F]};return{cacheSize:500,theme:{colors:[Dr],spacing:[ot],blur:["none","",Vt,F],brightness:b(),borderColor:[e],borderRadius:["none","","full",Vt,F],borderSpacing:V(),borderWidth:q(),contrast:b(),grayscale:_(),hueRotate:B(),invert:_(),gap:V(),gradientColorStops:[e],gradientColorStopPositions:[Dx,El],inset:$(),margin:$(),opacity:b(),padding:V(),saturate:b(),scale:b(),sepia:_(),skew:B(),space:V(),translate:V()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[Vt]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(Z(),[F])}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Vr]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",Vr]}],"grid-cols":[{"grid-cols":[Dr]}],"col-start-end":[{col:["auto",{span:["full",Vr]},F]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Dr]}],"row-start-end":[{row:["auto",{span:[Vr]},F]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal"].concat(L())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(L(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(L(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",F,t]}],"min-w":[{"min-w":["min","max","fit",F,ot]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[Vt]},Vt,F]}],h:[{h:[F,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",F,ot]}],"max-h":[{"max-h":[F,t,"min","max","fit"]}],"font-size":[{text:["base",Vt,El]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",oi]}],"font-family":[{font:[Dr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",kn,oi]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",F,ot]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(z(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",ot]}],"underline-offset":[{"underline-offset":["auto",F,ot]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(Z(),[bx])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",_x]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Vx]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[].concat(z(),["hidden"])}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(z())}],"outline-offset":[{"outline-offset":[F,ot]}],"outline-w":[{outline:[ot]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[ot]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Vt,Ox]}],"shadow-color":[{shadow:[Dr]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":ae()}],"bg-blend":[{"bg-blend":ae()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Vt,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[h]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[Vr,F]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[ot,oi]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var Ux=Rx(Bx);function pn(...e){return Ux(vm(e))}function Md(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function $x(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Md(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Md(e[o],null)}}}}function sn(...e){return v.useCallback($x(...e),e)}function qi(e){const t=Hx(e),n=v.forwardRef((r,o)=>{const{children:i,...s}=r,a=v.Children.toArray(i),l=a.find(Kx);if(l){const u=l.props.children,c=a.map(d=>d===l?v.Children.count(u)>1?v.Children.only(null):v.isValidElement(u)?u.props.children:null:d);return P.jsx(t,{...s,ref:o,children:v.isValidElement(u)?v.cloneElement(u,void 0,c):null})}return P.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var Wx=qi("Slot");function Hx(e){const t=v.forwardRef((n,r)=>{const{children:o,...i}=n,s=v.isValidElement(o)?Yx(o):void 0,a=sn(s,r);if(v.isValidElement(o)){const l=Qx(i,o.props);return o.type!==v.Fragment&&(l.ref=a),v.cloneElement(o,l)}return v.Children.count(o)>1?v.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Gx=Symbol("radix.slottable");function Kx(e){return v.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Gx}function Qx(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{const l=i(...a);return o(...a),l}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Yx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const jd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,_d=vm,Cm=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return _d(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],d=i==null?void 0:i[u];if(c===null)return null;const f=jd(c)||jd(d);return o[u][f]}),a=n&&Object.entries(n).reduce((u,c)=>{let[d,f]=c;return f===void 0||(u[d]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:d,className:f,...m}=c;return Object.entries(m).every(x=>{let[y,S]=x;return Array.isArray(S)?S.includes({...i,...a}[y]):{...i,...a}[y]===S})?[...u,d,f]:u},[]);return _d(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)},Xx=Cm("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),qr=fe.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?Wx:"button";return P.jsx(s,{className:pn(Xx({variant:t,size:n,className:e})),ref:i,...o})});qr.displayName="Button";var Zx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Jx=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Te=(e,t)=>{const n=v.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,children:a,...l},u)=>v.createElement("svg",{ref:u,...Zx,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:`lucide lucide-${Jx(e)}`,...l},[...t.map(([c,d])=>v.createElement(c,d)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n},qx=Te("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]),ew=Te("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),tw=Te("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),nw=Te("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),rw=Te("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),ow=Te("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),iw=Te("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),sw=Te("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),aw=Te("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),lw=Te("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),Em=Te("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),uw=Te("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),cw=Te("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),bd=Te("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Tm=Te("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),dw=Te("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),km=v.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ls=v.createContext({}),Ns=v.createContext(null),As=typeof document<"u",Ou=As?v.useLayoutEffect:v.useEffect,Rm=v.createContext({strict:!1}),Iu=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),fw="framerAppearId",Lm="data-"+Iu(fw);function pw(e,t,n,r){const{visualElement:o}=v.useContext(Ls),i=v.useContext(Rm),s=v.useContext(Ns),a=v.useContext(km).reducedMotion,l=v.useRef();r=r||i.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:o,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;v.useInsertionEffect(()=>{u&&u.update(n,s)});const c=v.useRef(!!(n[Lm]&&!window.HandoffComplete));return Ou(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),v.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function nr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function hw(e,t,n){return v.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):nr(n)&&(n.current=r))},[t])}function Eo(e){return typeof e=="string"||Array.isArray(e)}function Ms(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Fu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],zu=["initial",...Fu];function js(e){return Ms(e.animate)||zu.some(t=>Eo(e[t]))}function Nm(e){return!!(js(e)||e.variants)}function mw(e,t){if(js(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Eo(n)?n:void 0,animate:Eo(r)?r:void 0}}return e.inherit!==!1?t:{}}function gw(e){const{initial:t,animate:n}=mw(e,v.useContext(Ls));return v.useMemo(()=>({initial:t,animate:n}),[Vd(t),Vd(n)])}function Vd(e){return Array.isArray(e)?e.join(" "):e}const Dd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},To={};for(const e in Dd)To[e]={isEnabled:t=>Dd[e].some(n=>!!t[n])};function vw(e){for(const t in e)To[t]={...To[t],...e[t]}}const Bu=v.createContext({}),Am=v.createContext({}),yw=Symbol.for("motionComponentSymbol");function xw({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&vw(e);function i(a,l){let u;const c={...v.useContext(km),...a,layoutId:ww(a)},{isStatic:d}=c,f=gw(a),m=r(a,d);if(!d&&As){f.visualElement=pw(o,m,c,t);const x=v.useContext(Am),y=v.useContext(Rm).strict;f.visualElement&&(u=f.visualElement.loadFeatures(c,y,e,x))}return v.createElement(Ls.Provider,{value:f},u&&f.visualElement?v.createElement(u,{visualElement:f.visualElement,...c}):null,n(o,a,hw(m,f.visualElement,l),m,d,f.visualElement))}const s=v.forwardRef(i);return s[yw]=o,s}function ww({layoutId:e}){const t=v.useContext(Bu).id;return t&&e!==void 0?t+"-"+e:e}function Sw(e){function t(r,o={}){return xw(e(r,o))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,o)=>(n.has(o)||n.set(o,t(o)),n.get(o))})}const Pw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Uu(e){return typeof e!="string"||e.includes("-")?!1:!!(Pw.indexOf(e)>-1||/[A-Z]/.test(e))}const es={};function Cw(e){Object.assign(es,e)}const bo=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],zn=new Set(bo);function Mm(e,{layout:t,layoutId:n}){return zn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!es[e]||e==="opacity")}const Oe=e=>!!(e&&e.getVelocity),Ew={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Tw=bo.length;function kw(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,o){let i="";for(let s=0;s<Tw;s++){const a=bo[s];if(e[a]!==void 0){const l=Ew[a]||a;i+=`${l}(${e[a]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),o?i=o(e,r?"":i):n&&r&&(i="none"),i}const jm=e=>t=>typeof t=="string"&&t.startsWith(e),_m=jm("--"),Tl=jm("var(--"),Rw=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Lw=(e,t)=>t&&typeof e=="number"?t.transform(e):e,an=(e,t,n)=>Math.min(Math.max(n,e),t),Bn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},eo={...Bn,transform:e=>an(0,1,e)},ii={...Bn,default:1},to=e=>Math.round(e*1e5)/1e5,_s=/(-)?([\d]*\.?[\d])+/g,bm=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Nw=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Vo(e){return typeof e=="string"}const Do=e=>({test:t=>Vo(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Dt=Do("deg"),yt=Do("%"),D=Do("px"),Aw=Do("vh"),Mw=Do("vw"),Od={...yt,parse:e=>yt.parse(e)/100,transform:e=>yt.transform(e*100)},Id={...Bn,transform:Math.round},Vm={borderWidth:D,borderTopWidth:D,borderRightWidth:D,borderBottomWidth:D,borderLeftWidth:D,borderRadius:D,radius:D,borderTopLeftRadius:D,borderTopRightRadius:D,borderBottomRightRadius:D,borderBottomLeftRadius:D,width:D,maxWidth:D,height:D,maxHeight:D,size:D,top:D,right:D,bottom:D,left:D,padding:D,paddingTop:D,paddingRight:D,paddingBottom:D,paddingLeft:D,margin:D,marginTop:D,marginRight:D,marginBottom:D,marginLeft:D,rotate:Dt,rotateX:Dt,rotateY:Dt,rotateZ:Dt,scale:ii,scaleX:ii,scaleY:ii,scaleZ:ii,skew:Dt,skewX:Dt,skewY:Dt,distance:D,translateX:D,translateY:D,translateZ:D,x:D,y:D,z:D,perspective:D,transformPerspective:D,opacity:eo,originX:Od,originY:Od,originZ:D,zIndex:Id,fillOpacity:eo,strokeOpacity:eo,numOctaves:Id};function $u(e,t,n,r){const{style:o,vars:i,transform:s,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const f=t[d];if(_m(d)){i[d]=f;continue}const m=Vm[d],x=Lw(f,m);if(zn.has(d)){if(l=!0,s[d]=x,!c)continue;f!==(m.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=x):o[d]=x}if(t.transform||(l||r?o.transform=kw(e.transform,n,c,r):o.transform&&(o.transform="none")),u){const{originX:d="50%",originY:f="50%",originZ:m=0}=a;o.transformOrigin=`${d} ${f} ${m}`}}const Wu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Dm(e,t,n){for(const r in t)!Oe(t[r])&&!Mm(r,n)&&(e[r]=t[r])}function jw({transformTemplate:e},t,n){return v.useMemo(()=>{const r=Wu();return $u(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function _w(e,t,n){const r=e.style||{},o={};return Dm(o,r,e),Object.assign(o,jw(e,t,n)),e.transformValues?e.transformValues(o):o}function bw(e,t,n){const r={},o=_w(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=o,r}const Vw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ts(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Vw.has(e)}let Om=e=>!ts(e);function Dw(e){e&&(Om=t=>t.startsWith("on")?!ts(t):e(t))}try{Dw(require("@emotion/is-prop-valid").default)}catch{}function Ow(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Om(o)||n===!0&&ts(o)||!t&&!ts(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function Fd(e,t,n){return typeof e=="string"?e:D.transform(t+n*e)}function Iw(e,t,n){const r=Fd(t,e.x,e.width),o=Fd(n,e.y,e.height);return`${r} ${o}`}const Fw={offset:"stroke-dashoffset",array:"stroke-dasharray"},zw={offset:"strokeDashoffset",array:"strokeDasharray"};function Bw(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?Fw:zw;e[i.offset]=D.transform(-r);const s=D.transform(t),a=D.transform(n);e[i.array]=`${s} ${a}`}function Hu(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d,f){if($u(e,u,c,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:x,dimensions:y}=e;m.transform&&(y&&(x.transform=m.transform),delete m.transform),y&&(o!==void 0||i!==void 0||x.transform)&&(x.transformOrigin=Iw(y,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&Bw(m,s,a,l,!1)}const Im=()=>({...Wu(),attrs:{}}),Gu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Uw(e,t,n,r){const o=v.useMemo(()=>{const i=Im();return Hu(i,t,{enableHardwareAcceleration:!1},Gu(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Dm(i,e.style,e),o.style={...i,...o.style}}return o}function $w(e=!1){return(n,r,o,{latestValues:i},s)=>{const l=(Uu(n)?Uw:bw)(r,i,s,n),c={...Ow(r,typeof n=="string",e),...l,ref:o},{children:d}=r,f=v.useMemo(()=>Oe(d)?d.get():d,[d]);return v.createElement(n,{...c,children:f})}}function Fm(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const zm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Bm(e,t,n,r){Fm(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(zm.has(o)?o:Iu(o),t.attrs[o])}function Ku(e,t){const{style:n}=e,r={};for(const o in n)(Oe(n[o])||t.style&&Oe(t.style[o])||Mm(o,e))&&(r[o]=n[o]);return r}function Um(e,t){const n=Ku(e,t);for(const r in e)if(Oe(e[r])||Oe(t[r])){const o=bo.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=e[r]}return n}function Qu(e,t,n,r={},o={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),t}function $m(e){const t=v.useRef(null);return t.current===null&&(t.current=e()),t.current}const ns=e=>Array.isArray(e),Ww=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Hw=e=>ns(e)?e[e.length-1]||0:e;function Ci(e){const t=Oe(e)?e.get():e;return Ww(t)?t.toValue():t}function Gw({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,i){const s={latestValues:Kw(r,o,i,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const Wm=e=>(t,n)=>{const r=v.useContext(Ls),o=v.useContext(Ns),i=()=>Gw(e,t,r,o);return n?i():$m(i)};function Kw(e,t,n,r){const o={},i=r(e,{});for(const f in i)o[f]=Ci(i[f]);let{initial:s,animate:a}=e;const l=js(e),u=Nm(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const d=c?a:s;return d&&typeof d!="boolean"&&!Ms(d)&&(Array.isArray(d)?d:[d]).forEach(m=>{const x=Qu(e,m);if(!x)return;const{transitionEnd:y,transition:S,...g}=x;for(const p in g){let h=g[p];if(Array.isArray(h)){const w=c?h.length-1:0;h=h[w]}h!==null&&(o[p]=h)}for(const p in y)o[p]=y[p]}),o}const ie=e=>e;class zd{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Qw(e){let t=new zd,n=new zd,r=0,o=!1,i=!1;const s=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const d=c&&o,f=d?t:n;return u&&s.add(l),f.add(l)&&d&&o&&(r=t.order.length),l},cancel:l=>{n.remove(l),s.delete(l)},process:l=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),s.has(c)&&(a.schedule(c),e())}o=!1,i&&(i=!1,a.process(l))}};return a}const si=["prepare","read","update","preRender","render","postRender"],Yw=40;function Xw(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=si.reduce((d,f)=>(d[f]=Qw(()=>n=!0),d),{}),s=d=>i[d].process(o),a=()=>{const d=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(d-o.timestamp,Yw),1),o.timestamp=d,o.isProcessing=!0,si.forEach(s),o.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,o.isProcessing||e(a)};return{schedule:si.reduce((d,f)=>{const m=i[f];return d[f]=(x,y=!1,S=!1)=>(n||l(),m.schedule(x,y,S)),d},{}),cancel:d=>si.forEach(f=>i[f].cancel(d)),state:o,steps:i}}const{schedule:K,cancel:jt,state:Pe,steps:fa}=Xw(typeof requestAnimationFrame<"u"?requestAnimationFrame:ie,!0),Zw={useVisualState:Wm({scrapeMotionValuesFromProps:Um,createRenderState:Im,onMount:(e,t,{renderState:n,latestValues:r})=>{K.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),K.render(()=>{Hu(n,r,{enableHardwareAcceleration:!1},Gu(t.tagName),e.transformTemplate),Bm(t,n)})}})},Jw={useVisualState:Wm({scrapeMotionValuesFromProps:Ku,createRenderState:Wu})};function qw(e,{forwardMotionProps:t=!1},n,r){return{...Uu(e)?Zw:Jw,preloadedFeatures:n,useRender:$w(t),createVisualElement:r,Component:e}}function Et(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Hm=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function bs(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const eS=e=>t=>Hm(t)&&e(t,bs(t));function kt(e,t,n,r){return Et(e,t,eS(n),r)}const tS=(e,t)=>n=>t(e(n)),en=(...e)=>e.reduce(tS);function Gm(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Bd=Gm("dragHorizontal"),Ud=Gm("dragVertical");function Km(e){let t=!1;if(e==="y")t=Ud();else if(e==="x")t=Bd();else{const n=Bd(),r=Ud();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Qm(){const e=Km(!0);return e?(e(),!1):!0}class hn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function $d(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),o=(i,s)=>{if(i.pointerType==="touch"||Qm())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&K.update(()=>a[r](i,s))};return kt(e.current,n,o,{passive:!e.getProps()[r]})}class nS extends hn{mount(){this.unmount=en($d(this.node,!0),$d(this.node,!1))}unmount(){}}class rS extends hn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=en(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Ym=(e,t)=>t?e===t?!0:Ym(e,t.parentElement):!1;function pa(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,bs(n))}class oS extends hn{constructor(){super(...arguments),this.removeStartListeners=ie,this.removeEndListeners=ie,this.removeAccessibleListeners=ie,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=kt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();K.update(()=>{!d&&!Ym(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),s=kt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=en(i,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||pa("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&K.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Et(this.node.current,"keyup",s),pa("down",(a,l)=>{this.startPress(a,l)})},n=Et(this.node.current,"keydown",t),r=()=>{this.isPressing&&pa("cancel",(i,s)=>this.cancelPress(i,s))},o=Et(this.node.current,"blur",r);this.removeAccessibleListeners=en(n,o)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:o}=this.node.getProps();o&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Qm()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&K.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=kt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Et(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=en(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const kl=new WeakMap,ha=new WeakMap,iS=e=>{const t=kl.get(e.target);t&&t(e)},sS=e=>{e.forEach(iS)};function aS({root:e,...t}){const n=e||document;ha.has(n)||ha.set(n,{});const r=ha.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(sS,{root:e,...t})),r[o]}function lS(e,t,n){const r=aS(t);return kl.set(e,n),r.observe(e),()=>{kl.delete(e),r.unobserve(e)}}const uS={some:0,all:1};class cS extends hn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:uS[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(l)};return lS(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(dS(t,n))&&this.startObserver()}unmount(){}}function dS({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const fS={inView:{Feature:cS},tap:{Feature:oS},focus:{Feature:rS},hover:{Feature:nS}};function Xm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function pS(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function hS(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Vs(e,t,n){const r=e.getProps();return Qu(r,t,n!==void 0?n:r.custom,pS(e),hS(e))}let mS=ie,Yu=ie;const tn=e=>e*1e3,Rt=e=>e/1e3,gS={current:!1},Zm=e=>Array.isArray(e)&&typeof e[0]=="number";function Jm(e){return!!(!e||typeof e=="string"&&qm[e]||Zm(e)||Array.isArray(e)&&e.every(Jm))}const $r=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,qm={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:$r([0,.65,.55,1]),circOut:$r([.55,0,1,.45]),backIn:$r([.31,.01,.66,-.59]),backOut:$r([.33,1.53,.69,.99])};function eg(e){if(e)return Zm(e)?$r(e):Array.isArray(e)?e.map(eg):qm[e]}function vS(e,t,n,{delay:r=0,duration:o,repeat:i=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=eg(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}function yS(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const tg=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,xS=1e-7,wS=12;function SS(e,t,n,r,o){let i,s,a=0;do s=t+(n-t)/2,i=tg(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>xS&&++a<wS);return s}function Oo(e,t,n,r){if(e===t&&n===r)return ie;const o=i=>SS(i,0,1,e,n);return i=>i===0||i===1?i:tg(o(i),t,r)}const PS=Oo(.42,0,1,1),CS=Oo(0,0,.58,1),ng=Oo(.42,0,.58,1),ES=e=>Array.isArray(e)&&typeof e[0]!="number",rg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,og=e=>t=>1-e(1-t),Xu=e=>1-Math.sin(Math.acos(e)),ig=og(Xu),TS=rg(Xu),sg=Oo(.33,1.53,.69,.99),Zu=og(sg),kS=rg(Zu),RS=e=>(e*=2)<1?.5*Zu(e):.5*(2-Math.pow(2,-10*(e-1))),LS={linear:ie,easeIn:PS,easeInOut:ng,easeOut:CS,circIn:Xu,circInOut:TS,circOut:ig,backIn:Zu,backInOut:kS,backOut:sg,anticipate:RS},Wd=e=>{if(Array.isArray(e)){Yu(e.length===4);const[t,n,r,o]=e;return Oo(t,n,r,o)}else if(typeof e=="string")return LS[e];return e},Ju=(e,t)=>n=>!!(Vo(n)&&Nw.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),ag=(e,t,n)=>r=>{if(!Vo(r))return r;const[o,i,s,a]=r.match(_s);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},NS=e=>an(0,255,e),ma={...Bn,transform:e=>Math.round(NS(e))},Rn={test:Ju("rgb","red"),parse:ag("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ma.transform(e)+", "+ma.transform(t)+", "+ma.transform(n)+", "+to(eo.transform(r))+")"};function AS(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const Rl={test:Ju("#"),parse:AS,transform:Rn.transform},rr={test:Ju("hsl","hue"),parse:ag("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+yt.transform(to(t))+", "+yt.transform(to(n))+", "+to(eo.transform(r))+")"},ke={test:e=>Rn.test(e)||Rl.test(e)||rr.test(e),parse:e=>Rn.test(e)?Rn.parse(e):rr.test(e)?rr.parse(e):Rl.parse(e),transform:e=>Vo(e)?e:e.hasOwnProperty("red")?Rn.transform(e):rr.transform(e)},te=(e,t,n)=>-n*e+n*t+e;function ga(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function MS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=ga(l,a,e+1/3),i=ga(l,a,e),s=ga(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}const va=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},jS=[Rl,Rn,rr],_S=e=>jS.find(t=>t.test(e));function Hd(e){const t=_S(e);let n=t.parse(e);return t===rr&&(n=MS(n)),n}const lg=(e,t)=>{const n=Hd(e),r=Hd(t),o={...n};return i=>(o.red=va(n.red,r.red,i),o.green=va(n.green,r.green,i),o.blue=va(n.blue,r.blue,i),o.alpha=te(n.alpha,r.alpha,i),Rn.transform(o))};function bS(e){var t,n;return isNaN(e)&&Vo(e)&&(((t=e.match(_s))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(bm))===null||n===void 0?void 0:n.length)||0)>0}const ug={regex:Rw,countKey:"Vars",token:"${v}",parse:ie},cg={regex:bm,countKey:"Colors",token:"${c}",parse:ke.parse},dg={regex:_s,countKey:"Numbers",token:"${n}",parse:Bn.parse};function ya(e,{regex:t,countKey:n,token:r,parse:o}){const i=e.tokenised.match(t);i&&(e["num"+n]=i.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...i.map(o)))}function rs(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ya(n,ug),ya(n,cg),ya(n,dg),n}function fg(e){return rs(e).values}function pg(e){const{values:t,numColors:n,numVars:r,tokenised:o}=rs(e),i=t.length;return s=>{let a=o;for(let l=0;l<i;l++)l<r?a=a.replace(ug.token,s[l]):l<r+n?a=a.replace(cg.token,ke.transform(s[l])):a=a.replace(dg.token,to(s[l]));return a}}const VS=e=>typeof e=="number"?0:e;function DS(e){const t=fg(e);return pg(e)(t.map(VS))}const ln={test:bS,parse:fg,createTransformer:pg,getAnimatableNone:DS},hg=(e,t)=>n=>`${n>0?t:e}`;function mg(e,t){return typeof e=="number"?n=>te(e,t,n):ke.test(e)?lg(e,t):e.startsWith("var(")?hg(e,t):vg(e,t)}const gg=(e,t)=>{const n=[...e],r=n.length,o=e.map((i,s)=>mg(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}},OS=(e,t)=>{const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=mg(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}},vg=(e,t)=>{const n=ln.createTransformer(t),r=rs(e),o=rs(t);return r.numVars===o.numVars&&r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?en(gg(r.values,o.values),n):hg(e,t)},ko=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Gd=(e,t)=>n=>te(e,t,n);function IS(e){return typeof e=="number"?Gd:typeof e=="string"?ke.test(e)?lg:vg:Array.isArray(e)?gg:typeof e=="object"?OS:Gd}function FS(e,t,n){const r=[],o=n||IS(e[0]),i=e.length-1;for(let s=0;s<i;s++){let a=o(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||ie:t;a=en(l,a)}r.push(a)}return r}function yg(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Yu(i===t.length),i===1)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=FS(t,r,o),a=s.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=ko(e[c],e[c+1],u);return s[c](d)};return n?u=>l(an(e[0],e[i-1],u)):l}function zS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=ko(0,t,r);e.push(te(n,1,o))}}function BS(e){const t=[0];return zS(t,e.length-1),t}function US(e,t){return e.map(n=>n*t)}function $S(e,t){return e.map(()=>t||ng).splice(0,e.length-1)}function os({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=ES(r)?r.map(Wd):Wd(r),i={done:!1,value:t[0]},s=US(n&&n.length===t.length?n:BS(t),e),a=yg(s,t,{ease:Array.isArray(o)?o:$S(t,o)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}function xg(e,t){return t?e*(1e3/t):0}const WS=5;function wg(e,t,n){const r=Math.max(t-WS,0);return xg(n-e(r),t-r)}const xa=.001,HS=.01,Kd=10,GS=.05,KS=1;function QS({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i;mS(e<=tn(Kd));let s=1-t;s=an(GS,KS,s),e=an(HS,Kd,Rt(e)),s<1?(o=u=>{const c=u*s,d=c*e,f=c-n,m=Ll(u,s),x=Math.exp(-d);return xa-f/m*x},i=u=>{const d=u*s*e,f=d*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,x=Math.exp(-d),y=Ll(Math.pow(u,2),s);return(-o(u)+xa>0?-1:1)*((f-m)*x)/y}):(o=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-xa+c*d},i=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=XS(o,i,a);if(e=tn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const YS=12;function XS(e,t,n){let r=n;for(let o=1;o<YS;o++)r=r-e(r)/t(r);return r}function Ll(e,t){return e*Math.sqrt(1-t*t)}const ZS=["duration","bounce"],JS=["stiffness","damping","mass"];function Qd(e,t){return t.some(n=>e[n]!==void 0)}function qS(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Qd(e,JS)&&Qd(e,ZS)){const n=QS(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Sg({keyframes:e,restDelta:t,restSpeed:n,...r}){const o=e[0],i=e[e.length-1],s={done:!1,value:o},{stiffness:a,damping:l,mass:u,duration:c,velocity:d,isResolvedFromDuration:f}=qS({...r,velocity:-Rt(r.velocity||0)}),m=d||0,x=l/(2*Math.sqrt(a*u)),y=i-o,S=Rt(Math.sqrt(a/u)),g=Math.abs(y)<5;n||(n=g?.01:2),t||(t=g?.005:.5);let p;if(x<1){const h=Ll(S,x);p=w=>{const C=Math.exp(-x*S*w);return i-C*((m+x*S*y)/h*Math.sin(h*w)+y*Math.cos(h*w))}}else if(x===1)p=h=>i-Math.exp(-S*h)*(y+(m+S*y)*h);else{const h=S*Math.sqrt(x*x-1);p=w=>{const C=Math.exp(-x*S*w),T=Math.min(h*w,300);return i-C*((m+x*S*y)*Math.sinh(T)+h*y*Math.cosh(T))/h}}return{calculatedDuration:f&&c||null,next:h=>{const w=p(h);if(f)s.done=h>=c;else{let C=m;h!==0&&(x<1?C=wg(p,h,w):C=0);const T=Math.abs(C)<=n,E=Math.abs(i-w)<=t;s.done=T&&E}return s.value=s.done?i:w,s}}}function Yd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},m=k=>a!==void 0&&k<a||l!==void 0&&k>l,x=k=>a===void 0?l:l===void 0||Math.abs(a-k)<Math.abs(l-k)?a:l;let y=n*t;const S=d+y,g=s===void 0?S:s(S);g!==S&&(y=g-d);const p=k=>-y*Math.exp(-k/r),h=k=>g+p(k),w=k=>{const N=p(k),M=h(k);f.done=Math.abs(N)<=u,f.value=f.done?g:M};let C,T;const E=k=>{m(f.value)&&(C=k,T=Sg({keyframes:[f.value,x(f.value)],velocity:wg(h,k,f.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:k=>{let N=!1;return!T&&C===void 0&&(N=!0,w(k),E(k)),C!==void 0&&k>C?T.next(k-C):(!N&&w(k),f)}}}const eP=e=>{const t=({timestamp:n})=>e(n);return{start:()=>K.update(t,!0),stop:()=>jt(t),now:()=>Pe.isProcessing?Pe.timestamp:performance.now()}},Xd=2e4;function Zd(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Xd;)t+=n,r=e.next(t);return t>=Xd?1/0:t}const tP={decay:Yd,inertia:Yd,tween:os,keyframes:os,spring:Sg};function is({autoplay:e=!0,delay:t=0,driver:n=eP,keyframes:r,type:o="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...f}){let m=1,x=!1,y,S;const g=()=>{S=new Promise(b=>{y=b})};g();let p;const h=tP[o]||os;let w;h!==os&&typeof r[0]!="number"&&(w=yg([0,100],r,{clamp:!1}),r=[0,100]);const C=h({...f,keyframes:r});let T;a==="mirror"&&(T=h({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let E="idle",k=null,N=null,M=null;C.calculatedDuration===null&&i&&(C.calculatedDuration=Zd(C));const{calculatedDuration:$}=C;let V=1/0,q=1/0;$!==null&&(V=$+s,q=V*(i+1)-s);let j=0;const Z=b=>{if(N===null)return;m>0&&(N=Math.min(N,b)),m<0&&(N=Math.min(b-q/m,N)),k!==null?j=k:j=Math.round(b-N)*m;const B=j-t*(m>=0?1:-1),H=m>=0?B<0:B>q;j=Math.max(B,0),E==="finished"&&k===null&&(j=q);let Ie=j,Un=C;if(i){const Is=Math.min(j,q)/V;let zo=Math.floor(Is),gn=Is%1;!gn&&Is>=1&&(gn=1),gn===1&&zo--,zo=Math.min(zo,i+1),!!(zo%2)&&(a==="reverse"?(gn=1-gn,s&&(gn-=s/V)):a==="mirror"&&(Un=T)),Ie=an(0,1,gn)*V}const Fe=H?{done:!1,value:r[0]}:Un.next(Ie);w&&(Fe.value=w(Fe.value));let{done:mn}=Fe;!H&&$!==null&&(mn=m>=0?j>=q:j<=0);const Dv=k===null&&(E==="finished"||E==="running"&&mn);return d&&d(Fe.value),Dv&&L(),Fe},z=()=>{p&&p.stop(),p=void 0},ae=()=>{E="idle",z(),y(),g(),N=M=null},L=()=>{E="finished",c&&c(),z(),y()},_=()=>{if(x)return;p||(p=n(Z));const b=p.now();l&&l(),k!==null?N=b-k:(!N||E==="finished")&&(N=b),E==="finished"&&g(),M=N,k=null,E="running",p.start()};e&&_();const O={then(b,B){return S.then(b,B)},get time(){return Rt(j)},set time(b){b=tn(b),j=b,k!==null||!p||m===0?k=b:N=p.now()-b/m},get duration(){const b=C.calculatedDuration===null?Zd(C):C.calculatedDuration;return Rt(b)},get speed(){return m},set speed(b){b===m||!p||(m=b,O.time=Rt(j))},get state(){return E},play:_,pause:()=>{E="paused",k=j},stop:()=>{x=!0,E!=="idle"&&(E="idle",u&&u(),ae())},cancel:()=>{M!==null&&Z(M),ae()},complete:()=>{E="finished"},sample:b=>(N=0,Z(b))};return O}function nP(e){let t;return()=>(t===void 0&&(t=e()),t)}const rP=nP(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),oP=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ai=10,iP=2e4,sP=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Jm(t.ease);function aP(e,t,{onUpdate:n,onComplete:r,...o}){if(!(rP()&&oP.has(t)&&!o.repeatDelay&&o.repeatType!=="mirror"&&o.damping!==0&&o.type!=="inertia"))return!1;let s=!1,a,l,u=!1;const c=()=>{l=new Promise(h=>{a=h})};c();let{keyframes:d,duration:f=300,ease:m,times:x}=o;if(sP(t,o)){const h=is({...o,repeat:0,delay:0});let w={done:!1,value:d[0]};const C=[];let T=0;for(;!w.done&&T<iP;)w=h.sample(T),C.push(w.value),T+=ai;x=void 0,d=C,f=T-ai,m="linear"}const y=vS(e.owner.current,t,d,{...o,duration:f,ease:m,times:x}),S=()=>{u=!1,y.cancel()},g=()=>{u=!0,K.update(S),a(),c()};return y.onfinish=()=>{u||(e.set(yS(d,o)),r&&r(),g())},{then(h,w){return l.then(h,w)},attachTimeline(h){return y.timeline=h,y.onfinish=null,ie},get time(){return Rt(y.currentTime||0)},set time(h){y.currentTime=tn(h)},get speed(){return y.playbackRate},set speed(h){y.playbackRate=h},get duration(){return Rt(f)},play:()=>{s||(y.play(),jt(S))},pause:()=>y.pause(),stop:()=>{if(s=!0,y.playState==="idle")return;const{currentTime:h}=y;if(h){const w=is({...o,autoplay:!1});e.setWithVelocity(w.sample(h-ai).value,w.sample(h).value,ai)}g()},complete:()=>{u||y.finish()},cancel:g}}function lP({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const o=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ie,pause:ie,stop:ie,then:i=>(i(),Promise.resolve()),cancel:ie,complete:ie});return t?is({keyframes:[0,1],duration:0,delay:t,onComplete:o}):o()}const uP={type:"spring",stiffness:500,damping:25,restSpeed:10},cP=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),dP={type:"keyframes",duration:.8},fP={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},pP=(e,{keyframes:t})=>t.length>2?dP:zn.has(e)?e.startsWith("scale")?cP(t[1]):uP:fP,Nl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(ln.test(t)||t==="0")&&!t.startsWith("url(")),hP=new Set(["brightness","contrast","saturate","opacity"]);function mP(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(_s)||[];if(!r)return e;const o=n.replace(r,"");let i=hP.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const gP=/([a-z-]*)\(.*?\)/g,Al={...ln,getAnimatableNone:e=>{const t=e.match(gP);return t?t.map(mP).join(" "):e}},vP={...Vm,color:ke,backgroundColor:ke,outlineColor:ke,fill:ke,stroke:ke,borderColor:ke,borderTopColor:ke,borderRightColor:ke,borderBottomColor:ke,borderLeftColor:ke,filter:Al,WebkitFilter:Al},qu=e=>vP[e];function Pg(e,t){let n=qu(e);return n!==Al&&(n=ln),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Cg=e=>/^0[^.\s]+$/.test(e);function yP(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Cg(e)}function xP(e,t,n,r){const o=Nl(t,n);let i;Array.isArray(n)?i=[...n]:i=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<i.length;u++)i[u]===null&&(i[u]=u===0?s:i[u-1]),yP(i[u])&&l.push(u),typeof i[u]=="string"&&i[u]!=="none"&&i[u]!=="0"&&(a=i[u]);if(o&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];i[c]=Pg(t,a)}return i}function wP({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function ec(e,t){return e[t]||e.default||e}const SP={skipAnimations:!1},tc=(e,t,n,r={})=>o=>{const i=ec(r,e)||{},s=i.delay||r.delay||0;let{elapsed:a=0}=r;a=a-tn(s);const l=xP(t,e,n,i),u=l[0],c=l[l.length-1],d=Nl(e,u),f=Nl(e,c);let m={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-a,onUpdate:x=>{t.set(x),i.onUpdate&&i.onUpdate(x)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(wP(i)||(m={...m,...pP(e,m)}),m.duration&&(m.duration=tn(m.duration)),m.repeatDelay&&(m.repeatDelay=tn(m.repeatDelay)),!d||!f||gS.current||i.type===!1||SP.skipAnimations)return lP(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const x=aP(t,e,m);if(x)return x}return is(m)};function ss(e){return!!(Oe(e)&&e.add)}const Eg=e=>/^\-?\d*\.?\d+$/.test(e);function nc(e,t){e.indexOf(t)===-1&&e.push(t)}function rc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class oc{constructor(){this.subscriptions=[]}add(t){return nc(this.subscriptions,t),()=>rc(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const PP=e=>!isNaN(parseFloat(e));class CP{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,o=!0)=>{this.prev=this.current,this.current=r;const{delta:i,timestamp:s}=Pe;this.lastUpdated!==s&&(this.timeDelta=i,this.lastUpdated=s,K.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>K.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=PP(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new oc);const r=this.events[t].add(n);return t==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?xg(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function wr(e,t){return new CP(e,t)}const Tg=e=>t=>t.test(e),EP={test:e=>e==="auto",parse:e=>e},kg=[Bn,D,yt,Dt,Mw,Aw,EP],Or=e=>kg.find(Tg(e)),TP=[...kg,ke,ln],kP=e=>TP.find(Tg(e));function RP(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,wr(n))}function LP(e,t){const n=Vs(e,t);let{transitionEnd:r={},transition:o={},...i}=n?e.makeTargetAnimatable(n,!1):{};i={...i,...r};for(const s in i){const a=Hw(i[s]);RP(e,s,a)}}function NP(e,t,n){var r,o;const i=Object.keys(t).filter(a=>!e.hasValue(a)),s=i.length;if(s)for(let a=0;a<s;a++){const l=i[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(o=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&o!==void 0?o:t[l]),c!=null&&(typeof c=="string"&&(Eg(c)||Cg(c))?c=parseFloat(c):!kP(c)&&ln.test(u)&&(c=Pg(l,u)),e.addValue(l,wr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function AP(e,t){return t?(t[e]||t.default||t).from:void 0}function MP(e,t,n){const r={};for(const o in e){const i=AP(o,t);if(i!==void 0)r[o]=i;else{const s=n.getValue(o);s&&(r[o]=s.get())}}return r}function jP({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function _P(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Rg(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(i=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const d in a){const f=e.getValue(d),m=a[d];if(!f||m===void 0||c&&jP(c,d))continue;const x={delay:n,elapsed:0,...ec(i||{},d)};if(window.HandoffAppearAnimations){const g=e.getProps()[Lm];if(g){const p=window.HandoffAppearAnimations(g,d,f,K);p!==null&&(x.elapsed=p,x.isHandoff=!0)}}let y=!x.isHandoff&&!_P(f,m);if(x.type==="spring"&&(f.getVelocity()||x.velocity)&&(y=!1),f.animation&&(y=!1),y)continue;f.start(tc(d,f,m,e.shouldReduceMotion&&zn.has(d)?{type:!1}:x));const S=f.animation;ss(l)&&(l.add(d),S.then(()=>l.remove(d))),u.push(S)}return s&&Promise.all(u).then(()=>{s&&LP(e,s)}),u}function Ml(e,t,n={}){const r=Vs(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const i=r?()=>Promise.all(Rg(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=o;return bP(e,t,u+l,c,d,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[l,u]=a==="beforeChildren"?[i,s]:[s,i];return l().then(()=>u())}else return Promise.all([i(),s(n.delay)])}function bP(e,t,n=0,r=0,o=1,i){const s=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(VP).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Ml(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function VP(e,t){return e.sortNodePosition(t)}function DP(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>Ml(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=Ml(e,t,n);else{const o=typeof t=="function"?Vs(e,t,n.custom):t;r=Promise.all(Rg(e,o,n))}return r.then(()=>e.notify("AnimationComplete",t))}const OP=[...Fu].reverse(),IP=Fu.length;function FP(e){return t=>Promise.all(t.map(({animation:n,options:r})=>DP(e,n,r)))}function zP(e){let t=FP(e);const n=UP();let r=!0;const o=(l,u)=>{const c=Vs(e,u);if(c){const{transition:d,transitionEnd:f,...m}=c;l={...l,...m,...f}}return l};function i(l){t=l(e)}function s(l,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},f=[],m=new Set;let x={},y=1/0;for(let g=0;g<IP;g++){const p=OP[g],h=n[p],w=c[p]!==void 0?c[p]:d[p],C=Eo(w),T=p===u?h.isActive:null;T===!1&&(y=g);let E=w===d[p]&&w!==c[p]&&C;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),h.protectedKeys={...x},!h.isActive&&T===null||!w&&!h.prevProp||Ms(w)||typeof w=="boolean")continue;let N=BP(h.prevProp,w)||p===u&&h.isActive&&!E&&C||g>y&&C,M=!1;const $=Array.isArray(w)?w:[w];let V=$.reduce(o,{});T===!1&&(V={});const{prevResolvedValues:q={}}=h,j={...q,...V},Z=z=>{N=!0,m.has(z)&&(M=!0,m.delete(z)),h.needsAnimating[z]=!0};for(const z in j){const ae=V[z],L=q[z];if(x.hasOwnProperty(z))continue;let _=!1;ns(ae)&&ns(L)?_=!Xm(ae,L):_=ae!==L,_?ae!==void 0?Z(z):m.add(z):ae!==void 0&&m.has(z)?Z(z):h.protectedKeys[z]=!0}h.prevProp=w,h.prevResolvedValues=V,h.isActive&&(x={...x,...V}),r&&e.blockInitialAnimation&&(N=!1),N&&(!E||M)&&f.push(...$.map(z=>({animation:z,options:{type:p,...l}})))}if(m.size){const g={};m.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(g[p]=h)}),f.push({animation:g})}let S=!!f.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(f):Promise.resolve()}function a(l,u,c){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(m=>{var x;return(x=m.animationState)===null||x===void 0?void 0:x.setActive(l,u)}),n[l].isActive=u;const f=s(c,l);for(const m in n)n[m].protectedKeys={};return f}return{animateChanges:s,setActive:a,setAnimateFunction:i,getState:()=>n}}function BP(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Xm(t,e):!1}function vn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function UP(){return{animate:vn(!0),whileInView:vn(),whileHover:vn(),whileTap:vn(),whileDrag:vn(),whileFocus:vn(),exit:vn()}}class $P extends hn{constructor(t){super(t),t.animationState||(t.animationState=zP(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Ms(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let WP=0;class HP extends hn{constructor(){super(...arguments),this.id=WP++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===o)return;const i=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const GP={animation:{Feature:$P},exit:{Feature:HP}},Jd=(e,t)=>Math.abs(e-t);function KP(e,t){const n=Jd(e.x,t.x),r=Jd(e.y,t.y);return Math.sqrt(n**2+r**2)}class Lg{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Sa(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=KP(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:x}=d,{timestamp:y}=Pe;this.history.push({...x,timestamp:y});const{onStart:S,onMove:g}=this.handlers;f||(S&&S(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=wa(f,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:x,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Sa(d.type==="pointercancel"?this.lastMoveEventInfo:wa(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,S),x&&x(d,S)},!Hm(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=bs(t),a=wa(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=Pe;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Sa(a,this.history)),this.removeListeners=en(kt(this.contextWindow,"pointermove",this.handlePointerMove),kt(this.contextWindow,"pointerup",this.handlePointerUp),kt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),jt(this.updatePoint)}}function wa(e,t){return t?{point:t(e.point)}:e}function qd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Sa({point:e},t){return{point:e,delta:qd(e,Ng(t)),offset:qd(e,QP(t)),velocity:YP(t,.1)}}function QP(e){return e[0]}function Ng(e){return e[e.length-1]}function YP(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Ng(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>tn(t)));)n--;if(!r)return{x:0,y:0};const i=Rt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function He(e){return e.max-e.min}function jl(e,t=0,n=.01){return Math.abs(e-t)<=n}function ef(e,t,n,r=.5){e.origin=r,e.originPoint=te(t.min,t.max,e.origin),e.scale=He(n)/He(t),(jl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=te(n.min,n.max,e.origin)-e.originPoint,(jl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function no(e,t,n,r){ef(e.x,t.x,n.x,r?r.originX:void 0),ef(e.y,t.y,n.y,r?r.originY:void 0)}function tf(e,t,n){e.min=n.min+t.min,e.max=e.min+He(t)}function XP(e,t,n){tf(e.x,t.x,n.x),tf(e.y,t.y,n.y)}function nf(e,t,n){e.min=t.min-n.min,e.max=e.min+He(t)}function ro(e,t,n){nf(e.x,t.x,n.x),nf(e.y,t.y,n.y)}function ZP(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?te(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?te(n,e,r.max):Math.min(e,n)),e}function rf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function JP(e,{top:t,left:n,bottom:r,right:o}){return{x:rf(e.x,n,o),y:rf(e.y,t,r)}}function of(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function qP(e,t){return{x:of(e.x,t.x),y:of(e.y,t.y)}}function eC(e,t){let n=.5;const r=He(e),o=He(t);return o>r?n=ko(t.min,t.max-r,e.min):r>o&&(n=ko(e.min,e.max-o,t.min)),an(0,1,n)}function tC(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const _l=.35;function nC(e=_l){return e===!1?e=0:e===!0&&(e=_l),{x:sf(e,"left","right"),y:sf(e,"top","bottom")}}function sf(e,t,n){return{min:af(e,t),max:af(e,n)}}function af(e,t){return typeof e=="number"?e:e[t]||0}const lf=()=>({translate:0,scale:1,origin:0,originPoint:0}),or=()=>({x:lf(),y:lf()}),uf=()=>({min:0,max:0}),ue=()=>({x:uf(),y:uf()});function Xe(e){return[e("x"),e("y")]}function Ag({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function rC({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function oC(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Pa(e){return e===void 0||e===1}function bl({scale:e,scaleX:t,scaleY:n}){return!Pa(e)||!Pa(t)||!Pa(n)}function wn(e){return bl(e)||Mg(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Mg(e){return cf(e.x)||cf(e.y)}function cf(e){return e&&e!=="0%"}function as(e,t,n){const r=e-n,o=t*r;return n+o}function df(e,t,n,r,o){return o!==void 0&&(e=as(e,o,r)),as(e,n,r)+t}function Vl(e,t=0,n=1,r,o){e.min=df(e.min,t,n,r,o),e.max=df(e.max,t,n,r,o)}function jg(e,{x:t,y:n}){Vl(e.x,t.translate,t.scale,t.originPoint),Vl(e.y,n.translate,n.scale,n.originPoint)}function iC(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let a=0;a<o;a++){i=n[a],s=i.projectionDelta;const l=i.instance;l&&l.style&&l.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ir(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,jg(e,s)),r&&wn(i.latestValues)&&ir(e,i.latestValues))}t.x=ff(t.x),t.y=ff(t.y)}function ff(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ft(e,t){e.min=e.min+t,e.max=e.max+t}function pf(e,t,[n,r,o]){const i=t[o]!==void 0?t[o]:.5,s=te(e.min,e.max,i);Vl(e,t[n],t[r],s,t.scale)}const sC=["x","scaleX","originX"],aC=["y","scaleY","originY"];function ir(e,t){pf(e.x,t,sC),pf(e.y,t,aC)}function _g(e,t){return Ag(oC(e.getBoundingClientRect(),t))}function lC(e,t,n){const r=_g(e,n),{scroll:o}=t;return o&&(Ft(r.x,o.offset.x),Ft(r.y,o.offset.y)),r}const bg=({current:e})=>e?e.ownerDocument.defaultView:null,uC=new WeakMap;class cC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ue(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(bs(c,"page").point)},i=(c,d)=>{const{drag:f,dragPropagation:m,onDragStart:x}=this.getProps();if(f&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Km(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xe(S=>{let g=this.getAxisMotionValue(S).get()||0;if(yt.test(g)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[S];h&&(g=He(h)*(parseFloat(g)/100))}}this.originPoint[S]=g}),x&&K.update(()=>x(c,d),!1,!0);const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},s=(c,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:x,onDrag:y}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:S}=d;if(m&&this.currentDirection===null){this.currentDirection=dC(S),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",d.point,S),this.updateAxis("y",d.point,S),this.visualElement.render(),y&&y(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Xe(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Lg(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:bg(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&K.update(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!li(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=ZP(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&nr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=JP(o.layoutBox,n):this.constraints=!1,this.elastic=nC(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Xe(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=tC(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nr(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=lC(r,o.root,this.visualElement.getTransformPagePoint());let s=qP(o.layout.layoutBox,i);if(n){const a=n(rC(s));this.hasMutatedConstraints=!!a,a&&(s=Ag(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Xe(c=>{if(!li(c,n,this.currentDirection))return;let d=l&&l[c]||{};s&&(d={min:0,max:0});const f=o?200:1e6,m=o?40:1e7,x={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(c,x)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(tc(t,r,0,n))}stopAnimation(){Xe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Xe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Xe(n=>{const{drag:r}=this.getProps();if(!li(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:a}=o.layout.layoutBox[n];i.set(t[n]-te(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!nr(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Xe(s=>{const a=this.getAxisMotionValue(s);if(a){const l=a.get();o[s]=eC({min:l,max:l},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Xe(s=>{if(!li(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(te(l,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;uC.set(this.visualElement,this);const t=this.visualElement.current,n=kt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();nr(l)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),r();const s=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Xe(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=_l,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function li(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function dC(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class fC extends hn{constructor(t){super(t),this.removeGroupControls=ie,this.removeListeners=ie,this.controls=new cC(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ie}unmount(){this.removeGroupControls(),this.removeListeners()}}const hf=e=>(t,n)=>{e&&K.update(()=>e(t,n))};class pC extends hn{constructor(){super(...arguments),this.removePointerDownListener=ie}onPointerDown(t){this.session=new Lg(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:bg(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:hf(t),onStart:hf(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&K.update(()=>o(i,s))}}}mount(){this.removePointerDownListener=kt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function hC(){const e=v.useContext(Ns);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=v.useId();return v.useEffect(()=>r(o),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}const Ei={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function mf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ir={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(D.test(e))e=parseFloat(e);else return e;const n=mf(e,t.target.x),r=mf(e,t.target.y);return`${n}% ${r}%`}},mC={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=ln.parse(e);if(o.length>5)return r;const i=ln.createTransformer(e),s=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+s]/=a,o[1+s]/=l;const u=te(a,l,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}};class gC extends fe.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;Cw(vC),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Ei.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||K.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Vg(e){const[t,n]=hC(),r=v.useContext(Bu);return fe.createElement(gC,{...e,layoutGroup:r,switchLayoutGroup:v.useContext(Am),isPresent:t,safeToRemove:n})}const vC={borderRadius:{...Ir,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ir,borderTopRightRadius:Ir,borderBottomLeftRadius:Ir,borderBottomRightRadius:Ir,boxShadow:mC},Dg=["TopLeft","TopRight","BottomLeft","BottomRight"],yC=Dg.length,gf=e=>typeof e=="string"?parseFloat(e):e,vf=e=>typeof e=="number"||D.test(e);function xC(e,t,n,r,o,i){o?(e.opacity=te(0,n.opacity!==void 0?n.opacity:1,wC(r)),e.opacityExit=te(t.opacity!==void 0?t.opacity:1,0,SC(r))):i&&(e.opacity=te(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<yC;s++){const a=`border${Dg[s]}Radius`;let l=yf(t,a),u=yf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||vf(l)===vf(u)?(e[a]=Math.max(te(gf(l),gf(u),r),0),(yt.test(u)||yt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=te(t.rotate||0,n.rotate||0,r))}function yf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const wC=Og(0,.5,ig),SC=Og(.5,.95,ie);function Og(e,t,n){return r=>r<e?0:r>t?1:n(ko(e,t,r))}function xf(e,t){e.min=t.min,e.max=t.max}function Qe(e,t){xf(e.x,t.x),xf(e.y,t.y)}function wf(e,t,n,r,o){return e-=t,e=as(e,1/n,r),o!==void 0&&(e=as(e,1/o,r)),e}function PC(e,t=0,n=1,r=.5,o,i=e,s=e){if(yt.test(t)&&(t=parseFloat(t),t=te(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=te(i.min,i.max,r);e===i&&(a-=t),e.min=wf(e.min,t,n,a,o),e.max=wf(e.max,t,n,a,o)}function Sf(e,t,[n,r,o],i,s){PC(e,t[n],t[r],t[o],t.scale,i,s)}const CC=["x","scaleX","originX"],EC=["y","scaleY","originY"];function Pf(e,t,n,r){Sf(e.x,t,CC,n?n.x:void 0,r?r.x:void 0),Sf(e.y,t,EC,n?n.y:void 0,r?r.y:void 0)}function Cf(e){return e.translate===0&&e.scale===1}function Ig(e){return Cf(e.x)&&Cf(e.y)}function TC(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Fg(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Ef(e){return He(e.x)/He(e.y)}class kC{constructor(){this.members=[]}add(t){nc(this.members,t),t.scheduleRender()}remove(t){if(rc(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Tf(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(r=`translate3d(${o}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const RC=(e,t)=>e.depth-t.depth;class LC{constructor(){this.children=[],this.isDirty=!1}add(t){nc(this.children,t),this.isDirty=!0}remove(t){rc(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(RC),this.isDirty=!1,this.children.forEach(t)}}function NC(e,t){const n=performance.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(jt(r),e(i-t))};return K.read(r,!0),()=>jt(r)}function AC(e){window.MotionDebug&&window.MotionDebug.record(e)}function MC(e){return e instanceof SVGElement&&e.tagName!=="svg"}function jC(e,t,n){const r=Oe(e)?e:wr(e);return r.start(tc("",r,t,n)),r.animation}const kf=["","X","Y","Z"],_C={visibility:"hidden"},Rf=1e3;let bC=0;const Sn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function zg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},a=t==null?void 0:t()){this.id=bC++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Sn.totalNodes=Sn.resolvedTargetDeltas=Sn.recalculatedProjection=0,this.nodes.forEach(OC),this.nodes.forEach(UC),this.nodes.forEach($C),this.nodes.forEach(IC),AC(Sn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new LC)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new oc),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=MC(s),this.instance=s;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=NC(f,250),Ei.hasAnimatedSinceResize&&(Ei.hasAnimatedSinceResize=!1,this.nodes.forEach(Nf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||c.getDefaultTransition()||QC,{onLayoutAnimationStart:S,onLayoutAnimationComplete:g}=c.getProps(),p=!this.targetLayout||!Fg(this.targetLayout,x)||m,h=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,h);const w={...ec(y,"layout"),onPlay:S,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else f||Nf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,jt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(WC),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Lf);return}this.isUpdating||this.nodes.forEach(zC),this.isUpdating=!1,this.nodes.forEach(BC),this.nodes.forEach(VC),this.nodes.forEach(DC),this.clearAllSnapshots();const a=performance.now();Pe.delta=an(0,1e3/60,a-Pe.timestamp),Pe.timestamp=a,Pe.isProcessing=!0,fa.update.process(Pe),fa.preRender.process(Pe),fa.render.process(Pe),Pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(FC),this.sharedNodes.forEach(HC)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ue(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Ig(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(a||wn(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),YC(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ue();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(Ft(a.x,l.offset.x),Ft(a.y,l.offset.y)),a}removeElementScroll(s){const a=ue();Qe(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){Qe(a,s);const{scroll:f}=this.root;f&&(Ft(a.x,-f.offset.x),Ft(a.y,-f.offset.y))}Ft(a.x,c.offset.x),Ft(a.y,c.offset.y)}}return a}applyTransform(s,a=!1){const l=ue();Qe(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&ir(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),wn(c.latestValues)&&ir(l,c.latestValues)}return wn(this.latestValues)&&ir(l,this.latestValues),l}removeTransform(s){const a=ue();Qe(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!wn(u.latestValues))continue;bl(u.latestValues)&&u.updateSnapshot();const c=ue(),d=u.measurePageBox();Qe(c,d),Pf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return wn(this.latestValues)&&Pf(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=Pe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),ro(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ue(),this.targetWithTransforms=ue()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),XP(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Qe(this.target,this.layout.layoutBox),jg(this.target,this.targetDelta)):Qe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),ro(this.relativeTargetOrigin,this.target,m.target),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Sn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||bl(this.parent.latestValues)||Mg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Pe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Qe(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;iC(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:x}=a;if(!x){this.projectionTransform&&(this.projectionDelta=or(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=or(),this.projectionDeltaWithTransform=or());const y=this.projectionTransform;no(this.projectionDelta,this.layoutCorrected,x,this.latestValues),this.projectionTransform=Tf(this.projectionDelta,this.treeScale),(this.projectionTransform!==y||this.treeScale.x!==f||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),Sn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=or();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ue(),m=l?l.source:void 0,x=this.layout?this.layout.source:void 0,y=m!==x,S=this.getStack(),g=!S||S.members.length<=1,p=!!(y&&!g&&this.options.crossfade===!0&&!this.path.some(KC));this.animationProgress=0;let h;this.mixTargetDelta=w=>{const C=w/1e3;Af(d.x,s.x,C),Af(d.y,s.y,C),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ro(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),GC(this.relativeTarget,this.relativeTargetOrigin,f,C),h&&TC(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=ue()),Qe(h,this.relativeTarget)),y&&(this.animationValues=c,xC(c,u,this.latestValues,C,p,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(jt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{Ei.hasAnimatedSinceResize=!0,this.currentAnimation=jC(0,Rf,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Rf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Bg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ue();const d=He(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+d;const f=He(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}Qe(a,l),ir(a,c),no(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new kC),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<kf.length;c++){const d="rotate"+kf[c];l[d]&&(u[d]=l[d],s.setStaticValue(d,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return _C;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ci(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=Ci(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!wn(this.latestValues)&&(y.transform=c?c({},""):"none",this.hasProjected=!1),y}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=Tf(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:m,y:x}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${x.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in es){if(f[y]===void 0)continue;const{correct:S,applyTo:g}=es[y],p=u.transform==="none"?f[y]:S(f[y],d);if(g){const h=g.length;for(let w=0;w<h;w++)u[g[w]]=p}else u[y]=p}return this.options.layoutId&&(u.pointerEvents=d===this?Ci(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Lf),this.root.sharedNodes.clear()}}}function VC(e){e.updateLayout()}function DC(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?Xe(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=He(f);f.min=r[d].min,f.max=f.min+m}):Bg(i,n.layoutBox,r)&&Xe(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=He(r[d]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const a=or();no(a,r,n.layoutBox);const l=or();s?no(l,e.applyTransform(o,!0),n.measuredBox):no(l,r,n.layoutBox);const u=!Ig(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const x=ue();ro(x,n.layoutBox,f.layoutBox);const y=ue();ro(y,r,m.layoutBox),Fg(x,y)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=x,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function OC(e){Sn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function IC(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function FC(e){e.clearSnapshot()}function Lf(e){e.clearMeasurements()}function zC(e){e.isLayoutDirty=!1}function BC(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Nf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function UC(e){e.resolveTargetDelta()}function $C(e){e.calcProjection()}function WC(e){e.resetRotation()}function HC(e){e.removeLeadSnapshot()}function Af(e,t,n){e.translate=te(t.translate,0,n),e.scale=te(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Mf(e,t,n,r){e.min=te(t.min,n.min,r),e.max=te(t.max,n.max,r)}function GC(e,t,n,r){Mf(e.x,t.x,n.x,r),Mf(e.y,t.y,n.y,r)}function KC(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const QC={duration:.45,ease:[.4,0,.1,1]},jf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),_f=jf("applewebkit/")&&!jf("chrome/")?Math.round:ie;function bf(e){e.min=_f(e.min),e.max=_f(e.max)}function YC(e){bf(e.x),bf(e.y)}function Bg(e,t,n){return e==="position"||e==="preserve-aspect"&&!jl(Ef(t),Ef(n),.2)}const XC=zg({attachResizeListener:(e,t)=>Et(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ca={current:void 0},Ug=zg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ca.current){const e=new XC({});e.mount(window),e.setOptions({layoutScroll:!0}),Ca.current=e}return Ca.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),ZC={pan:{Feature:pC},drag:{Feature:fC,ProjectionNode:Ug,MeasureLayout:Vg}},JC=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function qC(e){const t=JC.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Dl(e,t,n=1){const[r,o]=qC(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return Eg(s)?parseFloat(s):s}else return Tl(o)?Dl(o,t,n+1):o}function eE(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(o=>{const i=o.get();if(!Tl(i))return;const s=Dl(i,r);s&&o.set(s)});for(const o in t){const i=t[o];if(!Tl(i))continue;const s=Dl(i,r);s&&(t[o]=s,n||(n={}),n[o]===void 0&&(n[o]=i))}return{target:t,transitionEnd:n}}const tE=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),$g=e=>tE.has(e),nE=e=>Object.keys(e).some($g),Vf=e=>e===Bn||e===D,Df=(e,t)=>parseFloat(e.split(", ")[t]),Of=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/);if(o)return Df(o[1],t);{const i=r.match(/^matrix\((.+)\)$/);return i?Df(i[1],e):0}},rE=new Set(["x","y","z"]),oE=bo.filter(e=>!rE.has(e));function iE(e){const t=[];return oE.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Sr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Of(4,13),y:Of(5,14)};Sr.translateX=Sr.x;Sr.translateY=Sr.y;const sE=(e,t,n)=>{const r=t.measureViewportBox(),o=t.current,i=getComputedStyle(o),{display:s}=i,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=Sr[u](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=Sr[u](l,i)}),e},aE=(e,t,n={},r={})=>{t={...t},r={...r};const o=Object.keys(t).filter($g);let i=[],s=!1;const a=[];if(o.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],d=Or(c);const f=t[l];let m;if(ns(f)){const x=f.length,y=f[0]===null?1:0;c=f[y],d=Or(c);for(let S=y;S<x&&f[S]!==null;S++)m?Yu(Or(f[S])===m):m=Or(f[S])}else m=Or(f);if(d!==m)if(Vf(d)&&Vf(m)){const x=u.get();typeof x=="string"&&u.set(parseFloat(x)),typeof f=="string"?t[l]=parseFloat(f):Array.isArray(f)&&m===D&&(t[l]=f.map(parseFloat))}else d!=null&&d.transform&&(m!=null&&m.transform)&&(c===0||f===0)?c===0?u.set(m.transform(c)):t[l]=d.transform(f):(s||(i=iE(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=sE(t,e,a);return i.length&&i.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),As&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function lE(e,t,n,r){return nE(t)?aE(e,t,n,r):{target:t,transitionEnd:r}}const uE=(e,t,n,r)=>{const o=eE(e,t,r);return t=o.target,r=o.transitionEnd,lE(e,t,n,r)},Ol={current:null},Wg={current:!1};function cE(){if(Wg.current=!0,!!As)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ol.current=e.matches;e.addListener(t),t()}else Ol.current=!1}function dE(e,t,n){const{willChange:r}=t;for(const o in t){const i=t[o],s=n[o];if(Oe(i))e.addValue(o,i),ss(r)&&r.add(o);else if(Oe(s))e.addValue(o,wr(i,{owner:e})),ss(r)&&r.remove(o);else if(s!==i)if(e.hasValue(o)){const a=e.getValue(o);!a.hasAnimated&&a.set(i)}else{const a=e.getStaticValue(o);e.addValue(o,wr(a!==void 0?a:i,{owner:e}))}}for(const o in n)t[o]===void 0&&e.removeValue(o);return t}const If=new WeakMap,Hg=Object.keys(To),fE=Hg.length,Ff=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],pE=zu.length;class hE{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,visualState:i},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>K.render(this.render,!1,!0);const{latestValues:a,renderState:l}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=s,this.isControllingVariants=js(n),this.isVariantNode=Nm(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const f=c[d];a[d]!==void 0&&Oe(f)&&(f.set(a[d],!1),ss(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,If.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Wg.current||cE(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ol.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){If.delete(this.current),this.projection&&this.projection.unmount(),jt(this.notifyUpdate),jt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=zn.has(t),o=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&K.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{o(),i()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,o,i){let s,a;for(let l=0;l<fE;l++){const u=Hg[l],{isEnabled:c,Feature:d,ProjectionNode:f,MeasureLayout:m}=To[u];f&&(s=f),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),m&&(a=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:d,layoutScroll:f,layoutRoot:m}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||d&&nr(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:i,layoutScroll:f,layoutRoot:m})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ue()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Ff.length;r++){const o=Ff[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i=t["on"+o];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=dE(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<pE;r++){const o=zu[r],i=this.props[o];(Eo(i)||i===!1)&&(n[o]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=wr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,o=typeof r=="string"||typeof r=="object"?(n=Qu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Oe(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new oc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Gg extends hE{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:o},i){let s=MP(r,t||{},this);if(o&&(n&&(n=o(n)),r&&(r=o(r)),s&&(s=o(s))),i){NP(this,r,s);const a=uE(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function mE(e){return window.getComputedStyle(e)}class gE extends Gg{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(zn.has(n)){const r=qu(n);return r&&r.default||0}else{const r=mE(t),o=(_m(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return _g(t,n)}build(t,n,r,o){$u(t,n,r,o.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Ku(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Oe(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,o){Fm(t,n,r,o)}}class vE extends Gg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(zn.has(n)){const r=qu(n);return r&&r.default||0}return n=zm.has(n)?n:Iu(n),t.getAttribute(n)}measureInstanceViewportBox(){return ue()}scrapeMotionValuesFromProps(t,n){return Um(t,n)}build(t,n,r,o){Hu(t,n,r,this.isSVGTag,o.transformTemplate)}renderInstance(t,n,r,o){Bm(t,n,r,o)}mount(t){this.isSVGTag=Gu(t.tagName),super.mount(t)}}const yE=(e,t)=>Uu(e)?new vE(t,{enableHardwareAcceleration:!1}):new gE(t,{enableHardwareAcceleration:!0}),xE={layout:{ProjectionNode:Ug,MeasureLayout:Vg}},wE={...GP,...fS,...ZC,...xE},SE=Sw((e,t)=>qw(e,t,wE,yE));function Kg(){const e=v.useRef(!1);return Ou(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function PE(){const e=Kg(),[t,n]=v.useState(0),r=v.useCallback(()=>{e.current&&n(t+1)},[t]);return[v.useCallback(()=>K.postRender(r),[r]),t]}class CE extends v.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function EE({children:e,isPresent:t}){const n=v.useId(),r=v.useRef(null),o=v.useRef({width:0,height:0,top:0,left:0});return v.useInsertionEffect(()=>{const{width:i,height:s,top:a,left:l}=o.current;if(t||!r.current||!i||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${s}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),v.createElement(CE,{isPresent:t,childRef:r,sizeRef:o},v.cloneElement(e,{ref:r}))}const Ea=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const a=$m(TE),l=v.useId(),u=v.useMemo(()=>({id:l,initial:t,isPresent:n,custom:o,onExitComplete:c=>{a.set(c,!0);for(const d of a.values())if(!d)return;r&&r()},register:c=>(a.set(c,!1),()=>a.delete(c))}),i?void 0:[n]);return v.useMemo(()=>{a.forEach((c,d)=>a.set(d,!1))},[n]),v.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=v.createElement(EE,{isPresent:n},e)),v.createElement(Ns.Provider,{value:u},e)};function TE(){return new Map}function kE(e){return v.useEffect(()=>()=>e(),[])}const Pn=e=>e.key||"";function RE(e,t){e.forEach(n=>{const r=Pn(n);t.set(r,n)})}function LE(e){const t=[];return v.Children.forEach(e,n=>{v.isValidElement(n)&&t.push(n)}),t}const NE=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:o,presenceAffectsLayout:i=!0,mode:s="sync"})=>{const a=v.useContext(Bu).forceRender||PE()[0],l=Kg(),u=LE(e);let c=u;const d=v.useRef(new Map).current,f=v.useRef(c),m=v.useRef(new Map).current,x=v.useRef(!0);if(Ou(()=>{x.current=!1,RE(u,m),f.current=c}),kE(()=>{x.current=!0,m.clear(),d.clear()}),x.current)return v.createElement(v.Fragment,null,c.map(p=>v.createElement(Ea,{key:Pn(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:i,mode:s},p)));c=[...c];const y=f.current.map(Pn),S=u.map(Pn),g=y.length;for(let p=0;p<g;p++){const h=y[p];S.indexOf(h)===-1&&!d.has(h)&&d.set(h,void 0)}return s==="wait"&&d.size&&(c=[]),d.forEach((p,h)=>{if(S.indexOf(h)!==-1)return;const w=m.get(h);if(!w)return;const C=y.indexOf(h);let T=p;if(!T){const E=()=>{d.delete(h);const k=Array.from(m.keys()).filter(N=>!S.includes(N));if(k.forEach(N=>m.delete(N)),f.current=u.filter(N=>{const M=Pn(N);return M===h||k.includes(M)}),!d.size){if(l.current===!1)return;a(),r&&r()}};T=v.createElement(Ea,{key:Pn(w),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:i,mode:s},w),d.set(h,T)}c.splice(C,0,T)}),c=c.map(p=>{const h=p.key;return d.has(h)?p:v.createElement(Ea,{key:Pn(p),isPresent:!0,presenceAffectsLayout:i,mode:s},p)}),v.createElement(v.Fragment,null,d.size?c:c.map(p=>v.cloneElement(p)))},zf=[{name:"Home",path:"/",icon:nw},{name:"About",path:"/about",icon:bd},{name:"Services",path:"/services",icon:ow},{name:"Projects",path:"/projects",icon:qx},{name:"Team",path:"/team",icon:bd},{name:"Blog",path:"/blog",icon:tw},{name:"Testimonials",path:"/testimonials",icon:uw},{name:"Features",path:"/features",icon:dw},{name:"Contact",path:"/contact",icon:Em}],AE=()=>{const[e,t]=v.useState(!1),n=()=>t(!e),r="text-primary font-semibold border-b-2 border-primary",o="text-foreground hover:text-primary transition-colors duration-300";return P.jsxs("nav",{className:"bg-background/80 backdrop-blur-md shadow-sm sticky top-0 z-50",children:[P.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:P.jsxs("div",{className:"flex items-center justify-between h-20",children:[P.jsxs(ht,{to:"/",className:"flex items-center",children:[P.jsx("img",{src:"/geostrat-logo.png",className:"h-10 w-auto mr-2",alt:"GEOSTRATDRC Logo",onError:i=>{console.log("Logo failed to load:",i.target.src),i.target.style.display="none"},onLoad:()=>console.log("Logo loaded successfully")}),P.jsxs("span",{className:"font-jost text-2xl font-bold text-primary",children:["GEOSTRAT",P.jsx("span",{className:"text-secondary",children:"DRC"})]})]}),P.jsx("div",{className:"hidden md:flex items-center space-x-6",children:zf.slice(0,5).map(i=>P.jsx(kd,{to:i.path,className:({isActive:s})=>`${s?r:o} font-medium text-sm`,children:i.name},i.name))}),P.jsx("div",{className:"hidden md:flex items-center",children:P.jsx(qr,{asChild:!0,variant:"default",className:"font-jost",children:P.jsx(ht,{to:"/quote",children:"Get a Quote"})})}),P.jsx("div",{className:"md:hidden flex items-center",children:P.jsx(qr,{variant:"ghost",onClick:n,"aria-label":"Toggle menu",children:e?P.jsx(Tm,{className:"h-6 w-6 text-primary"}):P.jsx(lw,{className:"h-6 w-6 text-primary"})})})]})}),P.jsx(NE,{children:e&&P.jsx(SE.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"md:hidden bg-background shadow-lg absolute w-full",children:P.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center",children:[zf.map(i=>P.jsx(kd,{to:i.path,onClick:n,className:({isActive:s})=>`block px-3 py-2 rounded-md text-base font-medium w-full text-center ${s?"bg-primary/10 text-primary":"text-foreground hover:bg-gray-100"}`,children:P.jsxs("div",{className:"flex items-center justify-center",children:[P.jsx(i.icon,{className:"h-5 w-5 mr-2"}),i.name]})},i.name)),P.jsx(qr,{asChild:!0,variant:"default",className:"w-full mt-4 font-jost",onClick:n,children:P.jsx(ht,{to:"/quote",children:"Get a Quote"})})]})})})]})},Qg=fe.forwardRef(({className:e,type:t,...n},r)=>P.jsx("input",{type:t,className:pn("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Qg.displayName="Input";class Io{constructor(t=0,n="Network Error"){this.status=t,this.text=n}}const ME=()=>{if(!(typeof localStorage>"u"))return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}},ve={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:ME()},ic=e=>e?typeof e=="string"?{publicKey:e}:e.toString()==="[object Object]"?e:{}:{},jE=(e,t="https://api.emailjs.com")=>{if(!e)return;const n=ic(e);ve.publicKey=n.publicKey,ve.blockHeadless=n.blockHeadless,ve.storageProvider=n.storageProvider,ve.blockList=n.blockList,ve.limitRate=n.limitRate,ve.origin=n.origin||t},Yg=async(e,t,n={})=>{const r=await fetch(ve.origin+e,{method:"POST",headers:n,body:t}),o=await r.text(),i=new Io(r.status,o);if(r.ok)return i;throw i},Xg=(e,t,n)=>{if(!e||typeof e!="string")throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||typeof t!="string")throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!n||typeof n!="string")throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},_E=e=>{if(e&&e.toString()!=="[object Object]")throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},Zg=e=>e.webdriver||!e.languages||e.languages.length===0,Jg=()=>new Io(451,"Unavailable For Headless Browser"),bE=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if(typeof t!="string")throw"The BlockList watchVariable has to be a string"},VE=e=>{var t;return!((t=e.list)!=null&&t.length)||!e.watchVariable},DE=(e,t)=>e instanceof FormData?e.get(t):e[t],qg=(e,t)=>{if(VE(e))return!1;bE(e.list,e.watchVariable);const n=DE(t,e.watchVariable);return typeof n!="string"?!1:e.list.includes(n)},ev=()=>new Io(403,"Forbidden"),OE=(e,t)=>{if(typeof e!="number"||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&typeof t!="string")throw"The LimitRate ID has to be a non-empty string"},IE=async(e,t,n)=>{const r=Number(await n.get(e)||0);return t-Date.now()+r},tv=async(e,t,n)=>{if(!t.throttle||!n)return!1;OE(t.throttle,t.id);const r=t.id||e;return await IE(r,t.throttle,n)>0?!0:(await n.set(r,Date.now().toString()),!1)},nv=()=>new Io(429,"Too Many Requests"),FE=async(e,t,n,r)=>{const o=ic(r),i=o.publicKey||ve.publicKey,s=o.blockHeadless||ve.blockHeadless,a=o.storageProvider||ve.storageProvider,l={...ve.blockList,...o.blockList},u={...ve.limitRate,...o.limitRate};return s&&Zg(navigator)?Promise.reject(Jg()):(Xg(i,e,t),_E(n),n&&qg(l,n)?Promise.reject(ev()):await tv(location.pathname,u,a)?Promise.reject(nv()):Yg("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:i,service_id:e,template_id:t,template_params:n}),{"Content-type":"application/json"}))},zE=e=>{if(!e||e.nodeName!=="FORM")throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},BE=e=>typeof e=="string"?document.querySelector(e):e,UE=async(e,t,n,r)=>{const o=ic(r),i=o.publicKey||ve.publicKey,s=o.blockHeadless||ve.blockHeadless,a=ve.storageProvider||o.storageProvider,l={...ve.blockList,...o.blockList},u={...ve.limitRate,...o.limitRate};if(s&&Zg(navigator))return Promise.reject(Jg());const c=BE(n);Xg(i,e,t),zE(c);const d=new FormData(c);return qg(l,d)?Promise.reject(ev()):await tv(location.pathname,u,a)?Promise.reject(nv()):(d.append("lib_version","4.4.1"),d.append("service_id",e),d.append("template_id",t),d.append("user_id",i),Yg("/api/v1.0/email/send-form",d))},Ds={init:jE,send:FE,sendForm:UE,EmailJSResponseStatus:Io},nn={serviceId:"service_u2ws3my",templateId:"template_lkht3b5",publicKey:"JQmvnvifq58HU89sd",recipientEmail:"<EMAIL>"},$E=()=>{Ds.init(nn.publicKey)},ok=async e=>{try{const t={to_email:nn.recipientEmail,from_name:e.name,from_email:e.email,subject:e.subject||"New Contact Form Submission",message:e.message,phone:e.phone||"Not provided",company:e.company||"Not provided",submission_date:new Date().toLocaleString(),website:"GEOSTRATDRC Website"},n=await Ds.send(nn.serviceId,nn.templateId,t);return console.log("Email sent successfully:",n),{success:!0,response:n}}catch(t){return console.error("Email sending failed:",t),{success:!1,error:t}}},ik=async e=>{try{const t={to_email:nn.recipientEmail,from_name:e.name,from_email:e.email,subject:`Quote Request: ${e.projectTitle}`,message:`
PROJECT DETAILS:
- Title: ${e.projectTitle}
- Description: ${e.projectDescription}
- Services: ${e.services.join(", ")}
- Budget: ${e.estimatedBudget}
- Timeline: ${e.desiredTimeline}
- Additional Info: ${e.additionalInfo||"None"}

CONTACT DETAILS:
- Name: ${e.name}
- Email: ${e.email}
- Phone: ${e.phone||"Not provided"}
- Company: ${e.company||"Not provided"}
      `,phone:e.phone||"Not provided",company:e.company||"Not provided",services:e.services.join(", "),project_title:e.projectTitle,project_description:e.projectDescription,budget:e.estimatedBudget,timeline:e.desiredTimeline,submission_date:new Date().toLocaleString(),website:"GEOSTRATDRC Website"},n=await Ds.send(nn.serviceId,"template_quote",t);return console.log("Quote email sent successfully:",n),{success:!0,response:n}}catch(t){return console.error("Quote email sending failed:",t),{success:!1,error:t}}},WE=async e=>{try{const t={to_email:nn.recipientEmail,from_email:e,subject:"New Newsletter Subscription",message:`New newsletter subscription from: ${e}`,submission_date:new Date().toLocaleString(),website:"GEOSTRATDRC Website"},n=await Ds.send(nn.serviceId,"template_newsletter",t);return console.log("Newsletter email sent successfully:",n),{success:!0,response:n}}catch(t){return console.error("Newsletter email sending failed:",t),{success:!1,error:t}}},HE=1;let Ta=0;function GE(){return Ta=(Ta+1)%Number.MAX_VALUE,Ta.toString()}const Re={state:{toasts:[]},listeners:[],getState:()=>Re.state,setState:e=>{typeof e=="function"?Re.state=e(Re.state):Re.state={...Re.state,...e},Re.listeners.forEach(t=>t(Re.state))},subscribe:e=>(Re.listeners.push(e),()=>{Re.listeners=Re.listeners.filter(t=>t!==e)})},KE=({...e})=>{const t=GE(),n=o=>Re.setState(i=>({...i,toasts:i.toasts.map(s=>s.id===t?{...s,...o}:s)})),r=()=>Re.setState(o=>({...o,toasts:o.toasts.filter(i=>i.id!==t)}));return Re.setState(o=>({...o,toasts:[{...e,id:t,dismiss:r},...o.toasts].slice(0,HE)})),{id:t,dismiss:r,update:n}};function rv(){const[e,t]=v.useState(Re.getState());return v.useEffect(()=>Re.subscribe(r=>{t(r)}),[]),v.useEffect(()=>{const n=[];return e.toasts.forEach(r=>{if(r.duration===1/0)return;const o=setTimeout(()=>{r.dismiss()},r.duration||5e3);n.push(o)}),()=>{n.forEach(r=>clearTimeout(r))}},[e.toasts]),{toast:KE,toasts:e.toasts}}const QE=()=>{const e=new Date().getFullYear(),{toast:t}=rv();v.useEffect(()=>{$E()},[]);const n=async i=>{i.preventDefault();const a=new FormData(i.target).get("email");if(!a){t({title:"Email Required",description:"Please enter your email address.",variant:"destructive"});return}try{if((await WE(a)).success)t({title:"Subscription Successful!",description:"Thank you for subscribing to our newsletter. We'<NAME_EMAIL>.",variant:"default"}),i.target.reset();else throw new Error("Email sending failed")}catch(l){console.error("Failed to send newsletter subscription:",l),t({title:"Subscription Noted",description:"Your subscription was noted but email notification failed. We'll still add you to our list.",variant:"default"}),i.target.reset()}},r=[{name:"About Us",path:"/about"},{name:"Services",path:"/services"},{name:"Projects",path:"/projects"},{name:"Contact Us",path:"/contact"},{name:"Blog",path:"/blog"}],o=[{name:"Climate-Smart Agriculture",path:"/services#climate-smart-agriculture"},{name:"Natural Risk Assessments",path:"/services#natural-risk-assessments"},{name:"Spatial Data Analytics",path:"/services#spatial-data-analytics"},{name:"Urban Planning",path:"/services#urban-planning"},{name:"Environmental Monitoring",path:"/services#environmental-monitoring"},{name:"Natural Resources Management",path:"/services#natural-resources-management"}];return P.jsx("footer",{className:"bg-gradient-to-r from-primary via-teal-600 to-secondary text-primary-foreground pt-16 pb-8 font-sans",children:P.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[P.jsxs("div",{children:[P.jsxs(ht,{to:"/",className:"flex items-center mb-4",children:[P.jsx("img",{src:"/geostrat-logo.png",className:"h-10 w-auto mr-2 filter brightness-0 invert",alt:"GEOSTRATDRC Logo White",onError:i=>{console.log("Footer logo failed to load:",i.target.src),i.target.style.display="none"},onLoad:()=>console.log("Footer logo loaded successfully")}),P.jsxs("span",{className:"font-jost text-2xl font-bold",children:["GEOSTRAT",P.jsx("span",{className:"opacity-80",children:"DRC"})]})]}),P.jsx("p",{className:"text-sm opacity-90 mb-4",children:"Leading geospatial innovation in Central Africa. We provide cutting-edge solutions for a sustainable future."}),P.jsxs("div",{className:"flex space-x-4",children:[P.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:P.jsx(ew,{size:20})}),P.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:P.jsx(cw,{size:20})}),P.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:P.jsx(iw,{size:20})}),P.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:P.jsx(rw,{size:20})})]})]}),P.jsxs("div",{children:[P.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Quick Links"}),P.jsx("ul",{className:"space-y-2",children:r.map(i=>P.jsx("li",{children:P.jsx(ht,{to:i.path,className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity",children:i.name})},i.name))})]}),P.jsxs("div",{children:[P.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Our Services"}),P.jsxs("ul",{className:"space-y-2",children:[o.map(i=>P.jsx("li",{children:P.jsx(ht,{to:i.path,className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity",children:i.name})},i.name)),P.jsx("li",{children:P.jsx(ht,{to:"/services",className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity font-semibold",children:"View All Services..."})})]})]}),P.jsxs("div",{children:[P.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Stay Updated"}),P.jsx("p",{className:"text-sm opacity-90 mb-3",children:"Subscribe to our newsletter for the latest updates and insights."}),P.jsxs("form",{onSubmit:n,className:"flex flex-col sm:flex-row gap-2",children:[P.jsx(Qg,{type:"email",name:"email",placeholder:"Enter your email",className:"bg-white/20 border-white/30 placeholder-white/70 text-white focus:bg-white/30 focus:ring-white/50 flex-grow",required:!0}),P.jsx(qr,{type:"submit",variant:"outline",className:"bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary font-jost",children:"Subscribe"})]})]})]}),P.jsxs("div",{className:"border-t border-white/20 pt-8 mt-8 text-center md:text-left",children:[P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 text-sm",children:[P.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[P.jsx(aw,{size:18,className:"mr-2 opacity-80"}),P.jsx("span",{className:"opacity-90",children:"Bukavu, Democratic Republic of Congo"})]}),P.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[P.jsx(Em,{size:18,className:"mr-2 opacity-80"}),P.jsx("span",{className:"opacity-90",children:"+243 973 583 690"})]}),P.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[P.jsx(sw,{size:18,className:"mr-2 opacity-80"}),P.jsx("span",{className:"opacity-90",children:"<EMAIL>"})]})]}),P.jsxs("p",{className:"text-sm opacity-80",children:["© ",e," GEOSTRATDRC. All Rights Reserved.",P.jsx(ht,{to:"/privacy-policy",className:"hover:underline ml-2",children:"Privacy Policy"})," |",P.jsx(ht,{to:"/terms-of-service",className:"hover:underline ml-1",children:"Terms of Service"})]})]})]})})};function YE(){const{pathname:e}=kr();return v.useEffect(()=>{window.scrollTo(0,0)},[e]),null}const XE=({children:e})=>P.jsxs("div",{className:"flex flex-col min-h-screen",children:[P.jsx(YE,{}),P.jsx(AE,{}),P.jsx("main",{className:"flex-grow",children:e}),P.jsx(QE,{})]});function Be(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function sk(e,t){const n=v.createContext(t),r=i=>{const{children:s,...a}=i,l=v.useMemo(()=>a,Object.values(a));return P.jsx(n.Provider,{value:l,children:s})};r.displayName=e+"Provider";function o(i){const s=v.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function ov(e,t=[]){let n=[];function r(i,s){const a=v.createContext(s),l=n.length;n=[...n,s];const u=d=>{var g;const{scope:f,children:m,...x}=d,y=((g=f==null?void 0:f[e])==null?void 0:g[l])||a,S=v.useMemo(()=>x,Object.values(x));return P.jsx(y.Provider,{value:S,children:m})};u.displayName=i+"Provider";function c(d,f){var y;const m=((y=f==null?void 0:f[e])==null?void 0:y[l])||a,x=v.useContext(m);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>v.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return v.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,ZE(o,...t)]}function ZE(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return v.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function JE(e){const t=e+"CollectionProvider",[n,r]=ov(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=y=>{const{scope:S,children:g}=y,p=fe.useRef(null),h=fe.useRef(new Map).current;return P.jsx(o,{scope:S,itemMap:h,collectionRef:p,children:g})};s.displayName=t;const a=e+"CollectionSlot",l=qi(a),u=fe.forwardRef((y,S)=>{const{scope:g,children:p}=y,h=i(a,g),w=sn(S,h.collectionRef);return P.jsx(l,{ref:w,children:p})});u.displayName=a;const c=e+"CollectionItemSlot",d="data-radix-collection-item",f=qi(c),m=fe.forwardRef((y,S)=>{const{scope:g,children:p,...h}=y,w=fe.useRef(null),C=sn(S,w),T=i(c,g);return fe.useEffect(()=>(T.itemMap.set(w,{ref:w,...h}),()=>void T.itemMap.delete(w))),P.jsx(f,{[d]:"",ref:C,children:p})});m.displayName=c;function x(y){const S=i(e+"CollectionConsumer",y);return fe.useCallback(()=>{const p=S.collectionRef.current;if(!p)return[];const h=Array.from(p.querySelectorAll(`[${d}]`));return Array.from(S.itemMap.values()).sort((T,E)=>h.indexOf(T.ref.current)-h.indexOf(E.ref.current))},[S.collectionRef,S.itemMap])}return[{Provider:s,Slot:u,ItemSlot:m},x,r]}var qE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],xt=qE.reduce((e,t)=>{const n=qi(`Primitive.${t}`),r=v.forwardRef((o,i)=>{const{asChild:s,...a}=o,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),P.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function iv(e,t){e&&Es.flushSync(()=>e.dispatchEvent(t))}function Vn(e){const t=v.useRef(e);return v.useEffect(()=>{t.current=e}),v.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function eT(e,t=globalThis==null?void 0:globalThis.document){const n=Vn(e);v.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var tT="DismissableLayer",Il="dismissableLayer.update",nT="dismissableLayer.pointerDownOutside",rT="dismissableLayer.focusOutside",Bf,sv=v.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),av=v.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=v.useContext(sv),[c,d]=v.useState(null),f=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=v.useState({}),x=sn(t,E=>d(E)),y=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=y.indexOf(S),p=c?y.indexOf(c):-1,h=u.layersWithOutsidePointerEventsDisabled.size>0,w=p>=g,C=iT(E=>{const k=E.target,N=[...u.branches].some(M=>M.contains(k));!w||N||(o==null||o(E),s==null||s(E),E.defaultPrevented||a==null||a())},f),T=sT(E=>{const k=E.target;[...u.branches].some(M=>M.contains(k))||(i==null||i(E),s==null||s(E),E.defaultPrevented||a==null||a())},f);return eT(E=>{p===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&a&&(E.preventDefault(),a()))},f),v.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Bf=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Uf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=Bf)}},[c,f,n,u]),v.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Uf())},[c,u]),v.useEffect(()=>{const E=()=>m({});return document.addEventListener(Il,E),()=>document.removeEventListener(Il,E)},[]),P.jsx(xt.div,{...l,ref:x,style:{pointerEvents:h?w?"auto":"none":void 0,...e.style},onFocusCapture:Be(e.onFocusCapture,T.onFocusCapture),onBlurCapture:Be(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:Be(e.onPointerDownCapture,C.onPointerDownCapture)})});av.displayName=tT;var oT="DismissableLayerBranch",lv=v.forwardRef((e,t)=>{const n=v.useContext(sv),r=v.useRef(null),o=sn(t,r);return v.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),P.jsx(xt.div,{...e,ref:o})});lv.displayName=oT;function iT(e,t=globalThis==null?void 0:globalThis.document){const n=Vn(e),r=v.useRef(!1),o=v.useRef(()=>{});return v.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){uv(nT,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function sT(e,t=globalThis==null?void 0:globalThis.document){const n=Vn(e),r=v.useRef(!1);return v.useEffect(()=>{const o=i=>{i.target&&!r.current&&uv(rT,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Uf(){const e=new CustomEvent(Il);document.dispatchEvent(e)}function uv(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?iv(o,i):o.dispatchEvent(i)}var aT=av,lT=lv,Ro=globalThis!=null&&globalThis.document?v.useLayoutEffect:()=>{},uT="Portal",cv=v.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=v.useState(!1);Ro(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?u1.createPortal(P.jsx(xt.div,{...r,ref:t}),s):null});cv.displayName=uT;function cT(e,t){return v.useReducer((n,r)=>t[n][r]??n,e)}var dv=e=>{const{present:t,children:n}=e,r=dT(t),o=typeof n=="function"?n({present:r.isPresent}):v.Children.only(n),i=sn(r.ref,fT(o));return typeof n=="function"||r.isPresent?v.cloneElement(o,{ref:i}):null};dv.displayName="Presence";function dT(e){const[t,n]=v.useState(),r=v.useRef(null),o=v.useRef(e),i=v.useRef("none"),s=e?"mounted":"unmounted",[a,l]=cT(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return v.useEffect(()=>{const u=ui(r.current);i.current=a==="mounted"?u:"none"},[a]),Ro(()=>{const u=r.current,c=o.current;if(c!==e){const f=i.current,m=ui(u);e?l("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(c&&f!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Ro(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,d=m=>{const y=ui(r.current).includes(m.animationName);if(m.target===t&&y&&(l("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},f=m=>{m.target===t&&(i.current=ui(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:v.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function ui(e){return(e==null?void 0:e.animationName)||"none"}function fT(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var pT=tp[" useInsertionEffect ".trim().toString()]||Ro;function hT({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=mT({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:o;{const c=v.useRef(e!==void 0);v.useEffect(()=>{const d=c.current;d!==a&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=a},[a,r])}const u=v.useCallback(c=>{var d;if(a){const f=gT(c)?c(e):c;f!==e&&((d=s.current)==null||d.call(s,f))}else i(c)},[a,e,i,s]);return[l,u]}function mT({defaultProp:e,onChange:t}){const[n,r]=v.useState(e),o=v.useRef(n),i=v.useRef(t);return pT(()=>{i.current=t},[t]),v.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function gT(e){return typeof e=="function"}var vT=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),yT="VisuallyHidden",sc=v.forwardRef((e,t)=>P.jsx(xt.span,{...e,ref:t,style:{...vT,...e.style}}));sc.displayName=yT;var ac="ToastProvider",[lc,xT,wT]=JE("Toast"),[fv,ak]=ov("Toast",[wT]),[ST,Os]=fv(ac),pv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=v.useState(null),[u,c]=v.useState(0),d=v.useRef(!1),f=v.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${ac}\`. Expected non-empty \`string\`.`),P.jsx(lc.Provider,{scope:t,children:P.jsx(ST,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:v.useCallback(()=>c(m=>m+1),[]),onToastRemove:v.useCallback(()=>c(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:s})})};pv.displayName=ac;var hv="ToastViewport",PT=["F8"],Fl="toast.viewportPause",zl="toast.viewportResume",mv=v.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=PT,label:o="Notifications ({hotkey})",...i}=e,s=Os(hv,n),a=xT(n),l=v.useRef(null),u=v.useRef(null),c=v.useRef(null),d=v.useRef(null),f=sn(t,d,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;v.useEffect(()=>{const S=g=>{var h;r.length!==0&&r.every(w=>g[w]||g.code===w)&&((h=d.current)==null||h.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),v.useEffect(()=>{const S=l.current,g=d.current;if(x&&S&&g){const p=()=>{if(!s.isClosePausedRef.current){const T=new CustomEvent(Fl);g.dispatchEvent(T),s.isClosePausedRef.current=!0}},h=()=>{if(s.isClosePausedRef.current){const T=new CustomEvent(zl);g.dispatchEvent(T),s.isClosePausedRef.current=!1}},w=T=>{!S.contains(T.relatedTarget)&&h()},C=()=>{S.contains(document.activeElement)||h()};return S.addEventListener("focusin",p),S.addEventListener("focusout",w),S.addEventListener("pointermove",p),S.addEventListener("pointerleave",C),window.addEventListener("blur",p),window.addEventListener("focus",h),()=>{S.removeEventListener("focusin",p),S.removeEventListener("focusout",w),S.removeEventListener("pointermove",p),S.removeEventListener("pointerleave",C),window.removeEventListener("blur",p),window.removeEventListener("focus",h)}}},[x,s.isClosePausedRef]);const y=v.useCallback(({tabbingDirection:S})=>{const p=a().map(h=>{const w=h.ref.current,C=[w,...VT(w)];return S==="forwards"?C:C.reverse()});return(S==="forwards"?p.reverse():p).flat()},[a]);return v.useEffect(()=>{const S=d.current;if(S){const g=p=>{var C,T,E;const h=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!h){const k=document.activeElement,N=p.shiftKey;if(p.target===S&&N){(C=u.current)==null||C.focus();return}const V=y({tabbingDirection:N?"backwards":"forwards"}),q=V.findIndex(j=>j===k);ka(V.slice(q+1))?p.preventDefault():N?(T=u.current)==null||T.focus():(E=c.current)==null||E.focus()}};return S.addEventListener("keydown",g),()=>S.removeEventListener("keydown",g)}},[a,y]),P.jsxs(lT,{ref:l,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&P.jsx(Bl,{ref:u,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"forwards"});ka(S)}}),P.jsx(lc.Slot,{scope:n,children:P.jsx(xt.ol,{tabIndex:-1,...i,ref:f})}),x&&P.jsx(Bl,{ref:c,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"backwards"});ka(S)}})]})});mv.displayName=hv;var gv="ToastFocusProxy",Bl=v.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Os(gv,n);return P.jsx(sc,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Bl.displayName=gv;var Fo="Toast",CT="toast.swipeStart",ET="toast.swipeMove",TT="toast.swipeCancel",kT="toast.swipeEnd",vv=v.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a,l]=hT({prop:r,defaultProp:o??!0,onChange:i,caller:Fo});return P.jsx(dv,{present:n||a,children:P.jsx(NT,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:Vn(e.onPause),onResume:Vn(e.onResume),onSwipeStart:Be(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Be(e.onSwipeMove,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:Be(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Be(e.onSwipeEnd,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});vv.displayName=Fo;var[RT,LT]=fv(Fo,{onClose(){}}),NT=v.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:c,onSwipeMove:d,onSwipeCancel:f,onSwipeEnd:m,...x}=e,y=Os(Fo,n),[S,g]=v.useState(null),p=sn(t,j=>g(j)),h=v.useRef(null),w=v.useRef(null),C=o||y.duration,T=v.useRef(0),E=v.useRef(C),k=v.useRef(0),{onToastAdd:N,onToastRemove:M}=y,$=Vn(()=>{var Z;(S==null?void 0:S.contains(document.activeElement))&&((Z=y.viewport)==null||Z.focus()),s()}),V=v.useCallback(j=>{!j||j===1/0||(window.clearTimeout(k.current),T.current=new Date().getTime(),k.current=window.setTimeout($,j))},[$]);v.useEffect(()=>{const j=y.viewport;if(j){const Z=()=>{V(E.current),u==null||u()},z=()=>{const ae=new Date().getTime()-T.current;E.current=E.current-ae,window.clearTimeout(k.current),l==null||l()};return j.addEventListener(Fl,z),j.addEventListener(zl,Z),()=>{j.removeEventListener(Fl,z),j.removeEventListener(zl,Z)}}},[y.viewport,C,l,u,V]),v.useEffect(()=>{i&&!y.isClosePausedRef.current&&V(C)},[i,C,y.isClosePausedRef,V]),v.useEffect(()=>(N(),()=>M()),[N,M]);const q=v.useMemo(()=>S?Ev(S):null,[S]);return y.viewport?P.jsxs(P.Fragment,{children:[q&&P.jsx(AT,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:q}),P.jsx(RT,{scope:n,onClose:$,children:Es.createPortal(P.jsx(lc.ItemSlot,{scope:n,children:P.jsx(aT,{asChild:!0,onEscapeKeyDown:Be(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||$(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:P.jsx(xt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...x,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Be(e.onKeyDown,j=>{j.key==="Escape"&&(a==null||a(j.nativeEvent),j.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:Be(e.onPointerDown,j=>{j.button===0&&(h.current={x:j.clientX,y:j.clientY})}),onPointerMove:Be(e.onPointerMove,j=>{if(!h.current)return;const Z=j.clientX-h.current.x,z=j.clientY-h.current.y,ae=!!w.current,L=["left","right"].includes(y.swipeDirection),_=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,O=L?_(0,Z):0,b=L?0:_(0,z),B=j.pointerType==="touch"?10:2,H={x:O,y:b},Ie={originalEvent:j,delta:H};ae?(w.current=H,ci(ET,d,Ie,{discrete:!1})):$f(H,y.swipeDirection,B)?(w.current=H,ci(CT,c,Ie,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(Z)>B||Math.abs(z)>B)&&(h.current=null)}),onPointerUp:Be(e.onPointerUp,j=>{const Z=w.current,z=j.target;if(z.hasPointerCapture(j.pointerId)&&z.releasePointerCapture(j.pointerId),w.current=null,h.current=null,Z){const ae=j.currentTarget,L={originalEvent:j,delta:Z};$f(Z,y.swipeDirection,y.swipeThreshold)?ci(kT,m,L,{discrete:!0}):ci(TT,f,L,{discrete:!0}),ae.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),AT=e=>{const{__scopeToast:t,children:n,...r}=e,o=Os(Fo,t),[i,s]=v.useState(!1),[a,l]=v.useState(!1);return _T(()=>s(!0)),v.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:P.jsx(cv,{asChild:!0,children:P.jsx(sc,{...r,children:i&&P.jsxs(P.Fragment,{children:[o.label," ",n]})})})},MT="ToastTitle",yv=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return P.jsx(xt.div,{...r,ref:t})});yv.displayName=MT;var jT="ToastDescription",xv=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return P.jsx(xt.div,{...r,ref:t})});xv.displayName=jT;var wv="ToastAction",Sv=v.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?P.jsx(Cv,{altText:n,asChild:!0,children:P.jsx(uc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${wv}\`. Expected non-empty \`string\`.`),null)});Sv.displayName=wv;var Pv="ToastClose",uc=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=LT(Pv,n);return P.jsx(Cv,{asChild:!0,children:P.jsx(xt.button,{type:"button",...r,ref:t,onClick:Be(e.onClick,o.onClose)})})});uc.displayName=Pv;var Cv=v.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return P.jsx(xt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Ev(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),bT(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Ev(r))}}),t}function ci(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?iv(o,i):o.dispatchEvent(i)}var $f=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function _T(e=()=>{}){const t=Vn(e);Ro(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function bT(e){return e.nodeType===e.ELEMENT_NODE}function VT(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ka(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var DT=pv,Tv=mv,kv=vv,Rv=yv,Lv=xv,Nv=Sv,Av=uc;const OT=DT,Mv=fe.forwardRef(({className:e,...t},n)=>P.jsx(Tv,{ref:n,className:pn("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Mv.displayName=Tv.displayName;const IT=Cm("data-[swipe=move]:transition-none group relative pointer-events-auto flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full data-[state=closed]:slide-out-to-right-full",{variants:{variant:{default:"bg-background border",destructive:"group destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),jv=fe.forwardRef(({className:e,variant:t,...n},r)=>P.jsx(kv,{ref:r,className:pn(IT({variant:t}),e),...n}));jv.displayName=kv.displayName;const FT=fe.forwardRef(({className:e,...t},n)=>P.jsx(Nv,{ref:n,className:pn("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-destructive/30 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));FT.displayName=Nv.displayName;const _v=fe.forwardRef(({className:e,...t},n)=>P.jsx(Av,{ref:n,className:pn("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:P.jsx(Tm,{className:"h-4 w-4"})}));_v.displayName=Av.displayName;const bv=fe.forwardRef(({className:e,...t},n)=>P.jsx(Rv,{ref:n,className:pn("text-sm font-semibold",e),...t}));bv.displayName=Rv.displayName;const Vv=fe.forwardRef(({className:e,...t},n)=>P.jsx(Lv,{ref:n,className:pn("text-sm opacity-90",e),...t}));Vv.displayName=Lv.displayName;function zT(){const{toasts:e}=rv();return P.jsxs(OT,{children:[e.map(({id:t,title:n,description:r,action:o,...i})=>P.jsxs(jv,{...i,children:[P.jsxs("div",{className:"grid gap-1",children:[n&&P.jsx(bv,{children:n}),r&&P.jsx(Vv,{children:r})]}),o,P.jsx(_v,{})]},t)),P.jsx(Mv,{})]})}const BT={theme:"system",setTheme:()=>null},UT=v.createContext(BT);function $T({children:e,defaultTheme:t="system",storageKey:n="vite-ui-theme",...r}){const[o,i]=v.useState(()=>localStorage.getItem(n)||t);v.useEffect(()=>{const a=window.document.documentElement;if(a.classList.remove("light","dark"),o==="system"){const l=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";a.classList.add(l);return}a.classList.add(o)},[o]);const s={theme:o,setTheme:a=>{localStorage.setItem(n,a),i(a)}};return P.jsx(UT.Provider,{...r,value:s,children:e})}const WT=v.lazy(()=>dt(()=>import("./HomePage-4f7a40f8.js"),["assets/HomePage-4f7a40f8.js","assets/card-68470f4b.js","assets/label-12fa0dfc.js","assets/dialog-0b95d70f.js","assets/chevron-left-cae1cdca.js","assets/chevron-right-8e26c4f0.js","assets/award-d5fdc0a9.js","assets/leaf-4dcb147e.js","assets/shield-alert-40a56e37.js","assets/alert-triangle-a66aa18c.js"])),HT=v.lazy(()=>dt(()=>import("./AboutPage-760cd0ab.js"),["assets/AboutPage-760cd0ab.js","assets/card-68470f4b.js","assets/leaf-4dcb147e.js","assets/award-d5fdc0a9.js"])),GT=v.lazy(()=>dt(()=>import("./ServicesPage-87f69014.js"),["assets/ServicesPage-87f69014.js","assets/card-68470f4b.js","assets/chevron-right-8e26c4f0.js","assets/leaf-4dcb147e.js","assets/shield-alert-40a56e37.js","assets/alert-triangle-a66aa18c.js"])),KT=v.lazy(()=>dt(()=>import("./ProjectsPage-64ebb186.js"),["assets/ProjectsPage-64ebb186.js","assets/card-68470f4b.js","assets/dialog-0b95d70f.js"])),QT=v.lazy(()=>dt(()=>import("./TeamPage-b437feb7.js"),["assets/TeamPage-b437feb7.js","assets/card-68470f4b.js","assets/avatar-124fd946.js","assets/award-d5fdc0a9.js"])),YT=v.lazy(()=>dt(()=>import("./BlogPage-44c2fd0e.js"),["assets/BlogPage-44c2fd0e.js","assets/card-68470f4b.js","assets/user-81d45b0d.js","assets/chevron-left-cae1cdca.js","assets/chevron-right-8e26c4f0.js"])),XT=v.lazy(()=>dt(()=>import("./ContactPage-1aa888ae.js"),["assets/ContactPage-1aa888ae.js","assets/label-12fa0dfc.js","assets/card-68470f4b.js"])),ZT=v.lazy(()=>dt(()=>import("./TestimonialsPage-6f480527.js"),["assets/TestimonialsPage-6f480527.js","assets/card-68470f4b.js","assets/avatar-124fd946.js","assets/chevron-left-cae1cdca.js","assets/chevron-right-8e26c4f0.js"])),JT=v.lazy(()=>dt(()=>import("./FeaturesPage-9850f406.js"),["assets/FeaturesPage-9850f406.js","assets/card-68470f4b.js"])),qT=v.lazy(()=>dt(()=>import("./QuotePage-a32ed82a.js"),["assets/QuotePage-a32ed82a.js","assets/label-12fa0dfc.js","assets/card-68470f4b.js","assets/user-81d45b0d.js"])),ek=v.lazy(()=>dt(()=>import("./NotFoundPage-130b65bb.js"),["assets/NotFoundPage-130b65bb.js","assets/alert-triangle-a66aa18c.js"]));function tk(){return P.jsx($T,{defaultTheme:"light",storageKey:"vite-ui-theme",children:P.jsxs(ux,{children:[P.jsx(XE,{children:P.jsx(v.Suspense,{fallback:P.jsx("div",{className:"flex justify-center items-center h-screen w-screen text-2xl font-jost",children:"Loading GEOSTRATDRC..."}),children:P.jsxs(tx,{children:[P.jsx(Ye,{path:"/",element:P.jsx(WT,{})}),P.jsx(Ye,{path:"/about",element:P.jsx(HT,{})}),P.jsx(Ye,{path:"/services",element:P.jsx(GT,{})}),P.jsx(Ye,{path:"/projects",element:P.jsx(KT,{})}),P.jsx(Ye,{path:"/team",element:P.jsx(QT,{})}),P.jsx(Ye,{path:"/blog",element:P.jsx(YT,{})}),P.jsx(Ye,{path:"/contact",element:P.jsx(XT,{})}),P.jsx(Ye,{path:"/testimonials",element:P.jsx(ZT,{})}),P.jsx(Ye,{path:"/features",element:P.jsx(JT,{})}),P.jsx(Ye,{path:"/quote",element:P.jsx(qT,{})}),P.jsx(Ye,{path:"*",element:P.jsx(ek,{})})]})})}),P.jsx(zT,{})]})})}Ra.createRoot(document.getElementById("root")).render(P.jsx(fe.StrictMode,{children:P.jsx(tk,{})}));export{tw as A,qr as B,ik as C,av as D,Cm as E,ew as F,nw as H,Qg as I,ht as L,aw as M,xt as P,fe as R,uw as S,cw as T,bd as U,Tm as X,dw as Z,qx as a,Ro as b,Te as c,tp as d,Vn as e,sn as f,ov as g,Be as h,dv as i,P as j,sk as k,hT as l,SE as m,cv as n,qi as o,pn as p,iw as q,v as r,sw as s,$E as t,rv as u,Em as v,rw as w,ok as x,ow as y,kr as z};
