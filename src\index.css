
@import url('https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 224 71.4% 4.1%; /* Dark Blue/Black for text */

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: 195 80% 40%; /* Teal/Blue - Example: HSL(195, 80%, 40%) */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 195 60% 65%; /* Lighter Teal/Blue */
    --secondary-foreground: 224 71.4% 4.1%;

    --muted: 220 14.3% 95.9%; /* Light Gray */
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%; /* Light Gray - can be adjusted */
    --accent-foreground: 220 8.9% 46.1%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 195 80% 40%; /* Teal/Blue for focus rings */

    --radius: 0.5rem;
  }

  .dark {
    /* Define dark mode colors if needed, for now, we focus on light mode */
    --background: 224 71.4% 4.1%;
    --foreground: 0 0% 98%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 0 0% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 0 0% 98%;

    --primary: 195 70% 50%; /* Slightly brighter Teal/Blue for dark mode */
    --primary-foreground: 0 0% 98%;

    --secondary: 195 50% 30%;
    --secondary-foreground: 0 0% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 195 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-jost;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar (optional) */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}
::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}
