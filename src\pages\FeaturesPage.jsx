
import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Cpu, Layers, BarChartBig, ShieldCheck, Users, FastForward as ClockFast, Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const companyFeatures = [
  { title: "Advanced Geospatial Technology", icon: <Cpu className="h-10 w-10" />, description: "Utilizing state-of-the-art GIS software, remote sensing technologies, AI, and machine learning algorithms to deliver precise and insightful data.", image: "feature_technology.jpg", alt: "Server room with glowing data streams" },
  { title: "Comprehensive Data Analytics", icon: <BarChartBig className="h-10 w-10" />, description: "Transforming complex datasets into clear, actionable intelligence through sophisticated spatial analysis, modeling, and visualization techniques.", image: "feature_analytics.jpg", alt: "Complex data visualization dashboard" },
  { title: "Customized Solutions", icon: <Layers className="h-10 w-10" />, description: "Tailoring our services and methodologies to meet the unique needs and objectives of each client and project, ensuring optimal outcomes.", image: "feature_customization.jpg", alt: "Puzzle pieces fitting together to form a solution" },
  { title: "Expert Multidisciplinary Team", icon: <Users className="h-10 w-10" />, description: "A diverse team of experienced GIS specialists, data scientists, environmental experts, urban planners, and project managers dedicated to excellence.", image: "feature_team.jpg", alt: "Diverse team collaborating around a table" },
  { title: "Proven Methodologies", icon: <ShieldCheck className="h-10 w-10" />, description: "Employing industry best practices and rigorous quality assurance processes to ensure reliable, accurate, and impactful results for every engagement.", image: "feature_methodology.jpg", alt: "Flowchart showing a structured process" },
  { title: "Timely & Efficient Delivery", icon: <ClockFast className="h-10 w-10" />, description: "Committed to delivering projects on schedule and within budget, leveraging agile methodologies for adaptive and responsive project management.", image: "feature_delivery.jpg", alt: "Clock with gears turning rapidly" },
  { title: "Innovative Problem Solving", icon: <Lightbulb className="h-10 w-10" />, description: "Continuously exploring new approaches and technologies to tackle complex challenges and provide forward-thinking solutions.", image: "feature_innovation.jpg", alt: "Bright lightbulb symbolizing a new idea" },
];

const technologyShowcase = [
  { name: "Satellite Imagery Analysis (High-Resolution)", description: "Access to and expertise in processing various satellite data (optical, radar) for detailed land cover mapping, change detection, and monitoring." },
  { name: "Drone (UAV) Technology", description: "Deployment of UAVs for high-precision aerial surveys, 3D modeling, and site-specific data collection." },
  { name: "AI & Machine Learning", description: "Application of AI/ML for automated feature extraction, predictive modeling, and anomaly detection in geospatial datasets." },
  { name: "Cloud-Based GIS Platforms", description: "Leveraging scalable cloud infrastructure for data storage, processing, and collaborative mapping solutions." },
  { name: "Advanced GIS Software Suites", description: "Proficiency in leading GIS software (e.g., ArcGIS, QGIS) and specialized analytical tools." },
];

const FeaturesPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Our Capabilities & Features
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Discover the cutting-edge technologies, expert methodologies, and unique advantages that set GEOSTRATDRC apart.
          </motion.p>
        </div>
      </section>

      {/* Company Features Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Zap className="h-16 w-16 text-primary mx-auto mb-4" />
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">What Makes Us Unique</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              We combine deep local knowledge with global technological advancements to deliver unparalleled geospatial solutions.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {companyFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full group hover:shadow-xl transition-shadow duration-300">
                  <CardHeader className="items-center text-center">
                    <div className="p-4 bg-primary/10 rounded-full inline-block mb-4 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      {React.cloneElement(feature.icon, { className: "h-10 w-10" })}
                    </div>
                    <CardTitle className="font-jost text-xl text-primary group-hover:text-primary/90">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <img  
                      src={feature.image} 
                      alt={feature.alt} 
                      className="w-full h-40 object-cover rounded-md mb-4 group-hover:opacity-90 transition-opacity"
                     src="https://images.unsplash.com/photo-1686061593213-98dad7c599b9" />
                    <CardDescription className="text-foreground/80">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Showcase Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Cpu className="h-16 w-16 text-primary mx-auto mb-4" />
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Technology at Our Core</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              We leverage a powerful suite of modern technologies to ensure precision, efficiency, and innovation in all our projects.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {technologyShowcase.map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="p-6 h-full bg-background">
                  <CardTitle className="font-jost text-lg text-primary mb-2">{tech.name}</CardTitle>
                  <CardDescription className="text-foreground/70">{tech.description}</CardDescription>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Methodology Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">Our Approach to Excellence</h2>
              <p className="text-lg text-foreground/90 mb-4">
                Our project methodology is designed for clarity, collaboration, and outstanding results. We follow a structured yet flexible process:
              </p>
              <ul className="space-y-3 text-foreground/80">
                <li className="flex items-start"><ShieldCheck className="h-5 w-5 mr-2 mt-1 text-primary flex-shrink-0" /><strong>Discovery & Planning:</strong> Understanding your needs, defining scope, and meticulous project planning.</li>
                <li className="flex items-start"><ShieldCheck className="h-5 w-5 mr-2 mt-1 text-primary flex-shrink-0" /><strong>Data Acquisition & Processing:</strong> Gathering relevant data from diverse sources and ensuring its quality and accuracy.</li>
                <li className="flex items-start"><ShieldCheck className="h-5 w-5 mr-2 mt-1 text-primary flex-shrink-0" /><strong>Analysis & Modeling:</strong> Applying advanced analytical techniques to derive meaningful insights.</li>
                <li className="flex items-start"><ShieldCheck className="h-5 w-5 mr-2 mt-1 text-primary flex-shrink-0" /><strong>Visualization & Reporting:</strong> Presenting findings in clear, accessible formats for informed decision-making.</li>
                <li className="flex items-start"><ShieldCheck className="h-5 w-5 mr-2 mt-1 text-primary flex-shrink-0" /><strong>Implementation & Support:</strong> Assisting with solution deployment and providing ongoing support as needed.</li>
              </ul>
            </motion.div>
            <motion.div
              className="relative group"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <img  
                src="process_flow_diagram.jpg" 
                alt="Diagram illustrating a structured project workflow" 
                className="rounded-lg shadow-xl w-full h-auto object-cover"
               src="https://images.unsplash.com/photo-1688413399498-e35ed74b554f" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 bg-gradient-to-r from-primary/80 via-teal-600/80 to-secondary/80 text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold mb-6">
            Leverage Our Capabilities for Your Success
          </h2>
          <p className="text-lg opacity-90 max-w-xl mx-auto mb-8">
            Discover how GEOSTRATDRC's advanced features and expert approach can transform your projects and initiatives.
          </p>
          <Button size="lg" variant="outline" asChild className="font-jost bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
            <Link to="/services">Explore Our Services</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default FeaturesPage;
