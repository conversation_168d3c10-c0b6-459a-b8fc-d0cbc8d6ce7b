function Pv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function Fd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var zd={exports:{}},is={},Bd={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ko=Symbol.for("react.element"),Ev=Symbol.for("react.portal"),Tv=Symbol.for("react.fragment"),kv=Symbol.for("react.strict_mode"),Rv=Symbol.for("react.profiler"),Lv=Symbol.for("react.provider"),Nv=Symbol.for("react.context"),Av=Symbol.for("react.forward_ref"),Mv=Symbol.for("react.suspense"),jv=Symbol.for("react.memo"),_v=Symbol.for("react.lazy"),ic=Symbol.iterator;function Dv(e){return e===null||typeof e!="object"?null:(e=ic&&e[ic]||e["@@iterator"],typeof e=="function"?e:null)}var Ud={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},$d=Object.assign,Wd={};function wr(e,t,n){this.props=e,this.context=t,this.refs=Wd,this.updater=n||Ud}wr.prototype.isReactComponent={};wr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};wr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Hd(){}Hd.prototype=wr.prototype;function Il(e,t,n){this.props=e,this.context=t,this.refs=Wd,this.updater=n||Ud}var Fl=Il.prototype=new Hd;Fl.constructor=Il;$d(Fl,wr.prototype);Fl.isPureReactComponent=!0;var sc=Array.isArray,Gd=Object.prototype.hasOwnProperty,zl={current:null},Kd={key:!0,ref:!0,__self:!0,__source:!0};function Qd(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Gd.call(t,r)&&!Kd.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ko,type:e,key:i,ref:s,props:o,_owner:zl.current}}function Vv(e,t){return{$$typeof:ko,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Bl(e){return typeof e=="object"&&e!==null&&e.$$typeof===ko}function Ov(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ac=/\/+/g;function Vs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ov(""+e.key):t.toString(36)}function li(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ko:case Ev:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Vs(s,0):r,sc(o)?(n="",e!=null&&(n=e.replace(ac,"$&/")+"/"),li(o,t,n,"",function(u){return u})):o!=null&&(Bl(o)&&(o=Vv(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(ac,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",sc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Vs(i,a);s+=li(i,t,n,l,o)}else if(l=Dv(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Vs(i,a++),s+=li(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Io(e,t,n){if(e==null)return e;var r=[],o=0;return li(e,r,"","",function(i){return t.call(n,i,o++)}),r}function bv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ne={current:null},ui={transition:null},Iv={ReactCurrentDispatcher:Ne,ReactCurrentBatchConfig:ui,ReactCurrentOwner:zl};function Xd(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:Io,forEach:function(e,t,n){Io(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Io(e,function(){t++}),t},toArray:function(e){return Io(e,function(t){return t})||[]},only:function(e){if(!Bl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=wr;I.Fragment=Tv;I.Profiler=Rv;I.PureComponent=Il;I.StrictMode=kv;I.Suspense=Mv;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Iv;I.act=Xd;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=$d({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=zl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Gd.call(t,l)&&!Kd.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ko,type:e.type,key:o,ref:i,props:r,_owner:s}};I.createContext=function(e){return e={$$typeof:Nv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Lv,_context:e},e.Consumer=e};I.createElement=Qd;I.createFactory=function(e){var t=Qd.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:Av,render:e}};I.isValidElement=Bl;I.lazy=function(e){return{$$typeof:_v,_payload:{_status:-1,_result:e},_init:bv}};I.memo=function(e,t){return{$$typeof:jv,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};I.unstable_act=Xd;I.useCallback=function(e,t){return Ne.current.useCallback(e,t)};I.useContext=function(e){return Ne.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return Ne.current.useDeferredValue(e)};I.useEffect=function(e,t){return Ne.current.useEffect(e,t)};I.useId=function(){return Ne.current.useId()};I.useImperativeHandle=function(e,t,n){return Ne.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return Ne.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return Ne.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return Ne.current.useMemo(e,t)};I.useReducer=function(e,t,n){return Ne.current.useReducer(e,t,n)};I.useRef=function(e){return Ne.current.useRef(e)};I.useState=function(e){return Ne.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return Ne.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return Ne.current.useTransition()};I.version="18.3.1";Bd.exports=I;var v=Bd.exports;const de=Fd(v),Yd=Pv({__proto__:null,default:de},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fv=v,zv=Symbol.for("react.element"),Bv=Symbol.for("react.fragment"),Uv=Object.prototype.hasOwnProperty,$v=Fv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wv={key:!0,ref:!0,__self:!0,__source:!0};function Zd(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Uv.call(t,r)&&!Wv.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:zv,type:e,key:i,ref:s,props:o,_owner:$v.current}}is.Fragment=Bv;is.jsx=Zd;is.jsxs=Zd;zd.exports=is;var C=zd.exports,Pa={},qd={exports:{}},He={},Jd={exports:{}},ep={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,_){var b=L.length;L.push(_);e:for(;0<b;){var D=b-1>>>1,B=L[D];if(0<o(B,_))L[D]=_,L[b]=B,b=D;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var _=L[0],b=L.pop();if(b!==_){L[0]=b;e:for(var D=0,B=L.length,H=B>>>1;D<H;){var be=2*(D+1)-1,zn=L[be],Ie=be+1,pn=L[Ie];if(0>o(zn,b))Ie<B&&0>o(pn,zn)?(L[D]=pn,L[Ie]=b,D=Ie):(L[D]=zn,L[be]=b,D=be);else if(Ie<B&&0>o(pn,b))L[D]=pn,L[Ie]=b,D=Ie;else break e}}return _}function o(L,_){var b=L.sortIndex-_.sortIndex;return b!==0?b:L.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,f=null,d=3,m=!1,x=!1,y=!1,S=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(L){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=L)r(u),_.sortIndex=_.expirationTime,t(l,_);else break;_=n(u)}}function w(L){if(y=!1,h(L),!x)if(n(l)!==null)x=!0,z(P);else{var _=n(u);_!==null&&ae(w,_.startTime-L)}}function P(L,_){x=!1,y&&(y=!1,g(k),k=-1),m=!0;var b=d;try{for(h(_),f=n(l);f!==null&&(!(f.expirationTime>_)||L&&!$());){var D=f.callback;if(typeof D=="function"){f.callback=null,d=f.priorityLevel;var B=D(f.expirationTime<=_);_=e.unstable_now(),typeof B=="function"?f.callback=B:f===n(l)&&r(l),h(_)}else r(l);f=n(l)}if(f!==null)var H=!0;else{var be=n(u);be!==null&&ae(w,be.startTime-_),H=!1}return H}finally{f=null,d=b,m=!1}}var T=!1,E=null,k=-1,N=5,M=-1;function $(){return!(e.unstable_now()-M<N)}function V(){if(E!==null){var L=e.unstable_now();M=L;var _=!0;try{_=E(!0,L)}finally{_?J():(T=!1,E=null)}}else T=!1}var J;if(typeof p=="function")J=function(){p(V)};else if(typeof MessageChannel<"u"){var j=new MessageChannel,Z=j.port2;j.port1.onmessage=V,J=function(){Z.postMessage(null)}}else J=function(){S(V,0)};function z(L){E=L,T||(T=!0,J())}function ae(L,_){k=S(function(){L(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){x||m||(x=!0,z(P))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(L){switch(d){case 1:case 2:case 3:var _=3;break;default:_=d}var b=d;d=_;try{return L()}finally{d=b}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,_){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var b=d;d=L;try{return _()}finally{d=b}},e.unstable_scheduleCallback=function(L,_,b){var D=e.unstable_now();switch(typeof b=="object"&&b!==null?(b=b.delay,b=typeof b=="number"&&0<b?D+b:D):b=D,L){case 1:var B=-1;break;case 2:B=250;break;case 5:B=**********;break;case 4:B=1e4;break;default:B=5e3}return B=b+B,L={id:c++,callback:_,priorityLevel:L,startTime:b,expirationTime:B,sortIndex:-1},b>D?(L.sortIndex=b,t(u,L),n(l)===null&&L===n(u)&&(y?(g(k),k=-1):y=!0,ae(w,b-D))):(L.sortIndex=B,t(l,L),x||m||(x=!0,z(P))),L},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(L){var _=d;return function(){var b=d;d=_;try{return L.apply(this,arguments)}finally{d=b}}}})(ep);Jd.exports=ep;var Hv=Jd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gv=v,$e=Hv;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var tp=new Set,no={};function Dn(e,t){cr(e,t),cr(e+"Capture",t)}function cr(e,t){for(no[e]=t,e=0;e<t.length;e++)tp.add(t[e])}var Rt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ea=Object.prototype.hasOwnProperty,Kv=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,lc={},uc={};function Qv(e){return Ea.call(uc,e)?!0:Ea.call(lc,e)?!1:Kv.test(e)?uc[e]=!0:(lc[e]=!0,!1)}function Xv(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Yv(e,t,n,r){if(t===null||typeof t>"u"||Xv(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ae(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new Ae(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new Ae(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new Ae(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new Ae(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new Ae(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new Ae(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new Ae(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new Ae(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new Ae(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ul=/[\-:]([a-z])/g;function $l(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ul,$l);ye[t]=new Ae(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ul,$l);ye[t]=new Ae(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ul,$l);ye[t]=new Ae(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new Ae(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new Ae(e,1,!1,e.toLowerCase(),null,!0,!0)});function Wl(e,t,n,r){var o=ye.hasOwnProperty(t)?ye[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Yv(t,n,o,r)&&(n=null),r||o===null?Qv(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var jt=Gv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Fo=Symbol.for("react.element"),Un=Symbol.for("react.portal"),$n=Symbol.for("react.fragment"),Hl=Symbol.for("react.strict_mode"),Ta=Symbol.for("react.profiler"),np=Symbol.for("react.provider"),rp=Symbol.for("react.context"),Gl=Symbol.for("react.forward_ref"),ka=Symbol.for("react.suspense"),Ra=Symbol.for("react.suspense_list"),Kl=Symbol.for("react.memo"),Ot=Symbol.for("react.lazy"),op=Symbol.for("react.offscreen"),cc=Symbol.iterator;function Tr(e){return e===null||typeof e!="object"?null:(e=cc&&e[cc]||e["@@iterator"],typeof e=="function"?e:null)}var re=Object.assign,Os;function br(e){if(Os===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Os=t&&t[1]||""}return`
`+Os+e}var bs=!1;function Is(e,t){if(!e||bs)return"";bs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{bs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?br(e):""}function Zv(e){switch(e.tag){case 5:return br(e.type);case 16:return br("Lazy");case 13:return br("Suspense");case 19:return br("SuspenseList");case 0:case 2:case 15:return e=Is(e.type,!1),e;case 11:return e=Is(e.type.render,!1),e;case 1:return e=Is(e.type,!0),e;default:return""}}function La(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $n:return"Fragment";case Un:return"Portal";case Ta:return"Profiler";case Hl:return"StrictMode";case ka:return"Suspense";case Ra:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rp:return(e.displayName||"Context")+".Consumer";case np:return(e._context.displayName||"Context")+".Provider";case Gl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Kl:return t=e.displayName||null,t!==null?t:La(e.type)||"Memo";case Ot:t=e._payload,e=e._init;try{return La(e(t))}catch{}}return null}function qv(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return La(t);case 8:return t===Hl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function tn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ip(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jv(e){var t=ip(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zo(e){e._valueTracker||(e._valueTracker=Jv(e))}function sp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ip(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ci(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Na(e,t){var n=t.checked;return re({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function fc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=tn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ap(e,t){t=t.checked,t!=null&&Wl(e,"checked",t,!1)}function Aa(e,t){ap(e,t);var n=tn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ma(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ma(e,t.type,tn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function dc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ma(e,t,n){(t!=="number"||Ci(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ir=Array.isArray;function or(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+tn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ja(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return re({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function pc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(Ir(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:tn(n)}}function lp(e,t){var n=tn(t.value),r=tn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function hc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function up(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function _a(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?up(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Bo,cp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Bo=Bo||document.createElement("div"),Bo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Bo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ro(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ur={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ey=["Webkit","ms","Moz","O"];Object.keys(Ur).forEach(function(e){ey.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ur[t]=Ur[e]})});function fp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ur.hasOwnProperty(e)&&Ur[e]?(""+t).trim():t+"px"}function dp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=fp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var ty=re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Da(e,t){if(t){if(ty[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Va(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Oa=null;function Ql(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ba=null,ir=null,sr=null;function mc(e){if(e=No(e)){if(typeof ba!="function")throw Error(R(280));var t=e.stateNode;t&&(t=cs(t),ba(e.stateNode,e.type,t))}}function pp(e){ir?sr?sr.push(e):sr=[e]:ir=e}function hp(){if(ir){var e=ir,t=sr;if(sr=ir=null,mc(e),t)for(e=0;e<t.length;e++)mc(t[e])}}function mp(e,t){return e(t)}function gp(){}var Fs=!1;function vp(e,t,n){if(Fs)return e(t,n);Fs=!0;try{return mp(e,t,n)}finally{Fs=!1,(ir!==null||sr!==null)&&(gp(),hp())}}function oo(e,t){var n=e.stateNode;if(n===null)return null;var r=cs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Ia=!1;if(Rt)try{var kr={};Object.defineProperty(kr,"passive",{get:function(){Ia=!0}}),window.addEventListener("test",kr,kr),window.removeEventListener("test",kr,kr)}catch{Ia=!1}function ny(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var $r=!1,Pi=null,Ei=!1,Fa=null,ry={onError:function(e){$r=!0,Pi=e}};function oy(e,t,n,r,o,i,s,a,l){$r=!1,Pi=null,ny.apply(ry,arguments)}function iy(e,t,n,r,o,i,s,a,l){if(oy.apply(this,arguments),$r){if($r){var u=Pi;$r=!1,Pi=null}else throw Error(R(198));Ei||(Ei=!0,Fa=u)}}function Vn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function yp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function gc(e){if(Vn(e)!==e)throw Error(R(188))}function sy(e){var t=e.alternate;if(!t){if(t=Vn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return gc(o),e;if(i===r)return gc(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function xp(e){return e=sy(e),e!==null?wp(e):null}function wp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=wp(e);if(t!==null)return t;e=e.sibling}return null}var Sp=$e.unstable_scheduleCallback,vc=$e.unstable_cancelCallback,ay=$e.unstable_shouldYield,ly=$e.unstable_requestPaint,le=$e.unstable_now,uy=$e.unstable_getCurrentPriorityLevel,Xl=$e.unstable_ImmediatePriority,Cp=$e.unstable_UserBlockingPriority,Ti=$e.unstable_NormalPriority,cy=$e.unstable_LowPriority,Pp=$e.unstable_IdlePriority,ss=null,mt=null;function fy(e){if(mt&&typeof mt.onCommitFiberRoot=="function")try{mt.onCommitFiberRoot(ss,e,void 0,(e.current.flags&128)===128)}catch{}}var at=Math.clz32?Math.clz32:hy,dy=Math.log,py=Math.LN2;function hy(e){return e>>>=0,e===0?32:31-(dy(e)/py|0)|0}var Uo=64,$o=4194304;function Fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ki(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Fr(a):(i&=s,i!==0&&(r=Fr(i)))}else s=n&~o,s!==0?r=Fr(s):i!==0&&(r=Fr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-at(t),o=1<<n,r|=e[n],t&=~o;return r}function my(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function gy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-at(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=my(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function za(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ep(){var e=Uo;return Uo<<=1,!(Uo&4194240)&&(Uo=64),e}function zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ro(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-at(t),e[t]=n}function vy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-at(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Yl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var W=0;function Tp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var kp,Zl,Rp,Lp,Np,Ba=!1,Wo=[],Wt=null,Ht=null,Gt=null,io=new Map,so=new Map,Ft=[],yy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function yc(e,t){switch(e){case"focusin":case"focusout":Wt=null;break;case"dragenter":case"dragleave":Ht=null;break;case"mouseover":case"mouseout":Gt=null;break;case"pointerover":case"pointerout":io.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":so.delete(t.pointerId)}}function Rr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=No(t),t!==null&&Zl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function xy(e,t,n,r,o){switch(t){case"focusin":return Wt=Rr(Wt,e,t,n,r,o),!0;case"dragenter":return Ht=Rr(Ht,e,t,n,r,o),!0;case"mouseover":return Gt=Rr(Gt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return io.set(i,Rr(io.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,so.set(i,Rr(so.get(i)||null,e,t,n,r,o)),!0}return!1}function Ap(e){var t=Sn(e.target);if(t!==null){var n=Vn(t);if(n!==null){if(t=n.tag,t===13){if(t=yp(n),t!==null){e.blockedOn=t,Np(e.priority,function(){Rp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ua(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Oa=r,n.target.dispatchEvent(r),Oa=null}else return t=No(n),t!==null&&Zl(t),e.blockedOn=n,!1;t.shift()}return!0}function xc(e,t,n){ci(e)&&n.delete(t)}function wy(){Ba=!1,Wt!==null&&ci(Wt)&&(Wt=null),Ht!==null&&ci(Ht)&&(Ht=null),Gt!==null&&ci(Gt)&&(Gt=null),io.forEach(xc),so.forEach(xc)}function Lr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ba||(Ba=!0,$e.unstable_scheduleCallback($e.unstable_NormalPriority,wy)))}function ao(e){function t(o){return Lr(o,e)}if(0<Wo.length){Lr(Wo[0],e);for(var n=1;n<Wo.length;n++){var r=Wo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Wt!==null&&Lr(Wt,e),Ht!==null&&Lr(Ht,e),Gt!==null&&Lr(Gt,e),io.forEach(t),so.forEach(t),n=0;n<Ft.length;n++)r=Ft[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ft.length&&(n=Ft[0],n.blockedOn===null);)Ap(n),n.blockedOn===null&&Ft.shift()}var ar=jt.ReactCurrentBatchConfig,Ri=!0;function Sy(e,t,n,r){var o=W,i=ar.transition;ar.transition=null;try{W=1,ql(e,t,n,r)}finally{W=o,ar.transition=i}}function Cy(e,t,n,r){var o=W,i=ar.transition;ar.transition=null;try{W=4,ql(e,t,n,r)}finally{W=o,ar.transition=i}}function ql(e,t,n,r){if(Ri){var o=Ua(e,t,n,r);if(o===null)Ys(e,t,r,Li,n),yc(e,r);else if(xy(o,e,t,n,r))r.stopPropagation();else if(yc(e,r),t&4&&-1<yy.indexOf(e)){for(;o!==null;){var i=No(o);if(i!==null&&kp(i),i=Ua(e,t,n,r),i===null&&Ys(e,t,r,Li,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ys(e,t,r,null,n)}}var Li=null;function Ua(e,t,n,r){if(Li=null,e=Ql(r),e=Sn(e),e!==null)if(t=Vn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=yp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Li=e,null}function Mp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(uy()){case Xl:return 1;case Cp:return 4;case Ti:case cy:return 16;case Pp:return 536870912;default:return 16}default:return 16}}var Bt=null,Jl=null,fi=null;function jp(){if(fi)return fi;var e,t=Jl,n=t.length,r,o="value"in Bt?Bt.value:Bt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return fi=o.slice(e,1<r?1-r:void 0)}function di(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ho(){return!0}function wc(){return!1}function Ge(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ho:wc,this.isPropagationStopped=wc,this}return re(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ho)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ho)},persist:function(){},isPersistent:Ho}),t}var Sr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},eu=Ge(Sr),Lo=re({},Sr,{view:0,detail:0}),Py=Ge(Lo),Bs,Us,Nr,as=re({},Lo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:tu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nr&&(Nr&&e.type==="mousemove"?(Bs=e.screenX-Nr.screenX,Us=e.screenY-Nr.screenY):Us=Bs=0,Nr=e),Bs)},movementY:function(e){return"movementY"in e?e.movementY:Us}}),Sc=Ge(as),Ey=re({},as,{dataTransfer:0}),Ty=Ge(Ey),ky=re({},Lo,{relatedTarget:0}),$s=Ge(ky),Ry=re({},Sr,{animationName:0,elapsedTime:0,pseudoElement:0}),Ly=Ge(Ry),Ny=re({},Sr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ay=Ge(Ny),My=re({},Sr,{data:0}),Cc=Ge(My),jy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_y={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dy[e])?!!t[e]:!1}function tu(){return Vy}var Oy=re({},Lo,{key:function(e){if(e.key){var t=jy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=di(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?_y[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:tu,charCode:function(e){return e.type==="keypress"?di(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?di(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),by=Ge(Oy),Iy=re({},as,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Pc=Ge(Iy),Fy=re({},Lo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:tu}),zy=Ge(Fy),By=re({},Sr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Uy=Ge(By),$y=re({},as,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Wy=Ge($y),Hy=[9,13,27,32],nu=Rt&&"CompositionEvent"in window,Wr=null;Rt&&"documentMode"in document&&(Wr=document.documentMode);var Gy=Rt&&"TextEvent"in window&&!Wr,_p=Rt&&(!nu||Wr&&8<Wr&&11>=Wr),Ec=String.fromCharCode(32),Tc=!1;function Dp(e,t){switch(e){case"keyup":return Hy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function Ky(e,t){switch(e){case"compositionend":return Vp(t);case"keypress":return t.which!==32?null:(Tc=!0,Ec);case"textInput":return e=t.data,e===Ec&&Tc?null:e;default:return null}}function Qy(e,t){if(Wn)return e==="compositionend"||!nu&&Dp(e,t)?(e=jp(),fi=Jl=Bt=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _p&&t.locale!=="ko"?null:t.data;default:return null}}var Xy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function kc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Xy[e.type]:t==="textarea"}function Op(e,t,n,r){pp(r),t=Ni(t,"onChange"),0<t.length&&(n=new eu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Hr=null,lo=null;function Yy(e){Kp(e,0)}function ls(e){var t=Kn(e);if(sp(t))return e}function Zy(e,t){if(e==="change")return t}var bp=!1;if(Rt){var Ws;if(Rt){var Hs="oninput"in document;if(!Hs){var Rc=document.createElement("div");Rc.setAttribute("oninput","return;"),Hs=typeof Rc.oninput=="function"}Ws=Hs}else Ws=!1;bp=Ws&&(!document.documentMode||9<document.documentMode)}function Lc(){Hr&&(Hr.detachEvent("onpropertychange",Ip),lo=Hr=null)}function Ip(e){if(e.propertyName==="value"&&ls(lo)){var t=[];Op(t,lo,e,Ql(e)),vp(Yy,t)}}function qy(e,t,n){e==="focusin"?(Lc(),Hr=t,lo=n,Hr.attachEvent("onpropertychange",Ip)):e==="focusout"&&Lc()}function Jy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ls(lo)}function e0(e,t){if(e==="click")return ls(t)}function t0(e,t){if(e==="input"||e==="change")return ls(t)}function n0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ut=typeof Object.is=="function"?Object.is:n0;function uo(e,t){if(ut(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ea.call(t,o)||!ut(e[o],t[o]))return!1}return!0}function Nc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ac(e,t){var n=Nc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Nc(n)}}function Fp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zp(){for(var e=window,t=Ci();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ci(e.document)}return t}function ru(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function r0(e){var t=zp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fp(n.ownerDocument.documentElement,n)){if(r!==null&&ru(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Ac(n,i);var s=Ac(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var o0=Rt&&"documentMode"in document&&11>=document.documentMode,Hn=null,$a=null,Gr=null,Wa=!1;function Mc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Wa||Hn==null||Hn!==Ci(r)||(r=Hn,"selectionStart"in r&&ru(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gr&&uo(Gr,r)||(Gr=r,r=Ni($a,"onSelect"),0<r.length&&(t=new eu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hn)))}function Go(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Gn={animationend:Go("Animation","AnimationEnd"),animationiteration:Go("Animation","AnimationIteration"),animationstart:Go("Animation","AnimationStart"),transitionend:Go("Transition","TransitionEnd")},Gs={},Bp={};Rt&&(Bp=document.createElement("div").style,"AnimationEvent"in window||(delete Gn.animationend.animation,delete Gn.animationiteration.animation,delete Gn.animationstart.animation),"TransitionEvent"in window||delete Gn.transitionend.transition);function us(e){if(Gs[e])return Gs[e];if(!Gn[e])return e;var t=Gn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Bp)return Gs[e]=t[n];return e}var Up=us("animationend"),$p=us("animationiteration"),Wp=us("animationstart"),Hp=us("transitionend"),Gp=new Map,jc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function an(e,t){Gp.set(e,t),Dn(t,[e])}for(var Ks=0;Ks<jc.length;Ks++){var Qs=jc[Ks],i0=Qs.toLowerCase(),s0=Qs[0].toUpperCase()+Qs.slice(1);an(i0,"on"+s0)}an(Up,"onAnimationEnd");an($p,"onAnimationIteration");an(Wp,"onAnimationStart");an("dblclick","onDoubleClick");an("focusin","onFocus");an("focusout","onBlur");an(Hp,"onTransitionEnd");cr("onMouseEnter",["mouseout","mouseover"]);cr("onMouseLeave",["mouseout","mouseover"]);cr("onPointerEnter",["pointerout","pointerover"]);cr("onPointerLeave",["pointerout","pointerover"]);Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),a0=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function _c(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,iy(r,t,void 0,e),e.currentTarget=null}function Kp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;_c(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;_c(o,a,u),i=l}}}if(Ei)throw e=Fa,Ei=!1,Fa=null,e}function X(e,t){var n=t[Xa];n===void 0&&(n=t[Xa]=new Set);var r=e+"__bubble";n.has(r)||(Qp(t,e,2,!1),n.add(r))}function Xs(e,t,n){var r=0;t&&(r|=4),Qp(n,e,r,t)}var Ko="_reactListening"+Math.random().toString(36).slice(2);function co(e){if(!e[Ko]){e[Ko]=!0,tp.forEach(function(n){n!=="selectionchange"&&(a0.has(n)||Xs(n,!1,e),Xs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ko]||(t[Ko]=!0,Xs("selectionchange",!1,t))}}function Qp(e,t,n,r){switch(Mp(t)){case 1:var o=Sy;break;case 4:o=Cy;break;default:o=ql}n=o.bind(null,t,n,e),o=void 0,!Ia||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ys(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Sn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}vp(function(){var u=i,c=Ql(n),f=[];e:{var d=Gp.get(e);if(d!==void 0){var m=eu,x=e;switch(e){case"keypress":if(di(n)===0)break e;case"keydown":case"keyup":m=by;break;case"focusin":x="focus",m=$s;break;case"focusout":x="blur",m=$s;break;case"beforeblur":case"afterblur":m=$s;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Sc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Ty;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=zy;break;case Up:case $p:case Wp:m=Ly;break;case Hp:m=Uy;break;case"scroll":m=Py;break;case"wheel":m=Wy;break;case"copy":case"cut":case"paste":m=Ay;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Pc}var y=(t&4)!==0,S=!y&&e==="scroll",g=y?d!==null?d+"Capture":null:d;y=[];for(var p=u,h;p!==null;){h=p;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,g!==null&&(w=oo(p,g),w!=null&&y.push(fo(p,w,h)))),S)break;p=p.return}0<y.length&&(d=new m(d,x,null,n,c),f.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==Oa&&(x=n.relatedTarget||n.fromElement)&&(Sn(x)||x[Lt]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=u,x=x?Sn(x):null,x!==null&&(S=Vn(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=u),m!==x)){if(y=Sc,w="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Pc,w="onPointerLeave",g="onPointerEnter",p="pointer"),S=m==null?d:Kn(m),h=x==null?d:Kn(x),d=new y(w,p+"leave",m,n,c),d.target=S,d.relatedTarget=h,w=null,Sn(c)===u&&(y=new y(g,p+"enter",x,n,c),y.target=h,y.relatedTarget=S,w=y),S=w,m&&x)t:{for(y=m,g=x,p=0,h=y;h;h=Bn(h))p++;for(h=0,w=g;w;w=Bn(w))h++;for(;0<p-h;)y=Bn(y),p--;for(;0<h-p;)g=Bn(g),h--;for(;p--;){if(y===g||g!==null&&y===g.alternate)break t;y=Bn(y),g=Bn(g)}y=null}else y=null;m!==null&&Dc(f,d,m,y,!1),x!==null&&S!==null&&Dc(f,S,x,y,!0)}}e:{if(d=u?Kn(u):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var P=Zy;else if(kc(d))if(bp)P=t0;else{P=Jy;var T=qy}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(P=e0);if(P&&(P=P(e,u))){Op(f,P,n,c);break e}T&&T(e,d,u),e==="focusout"&&(T=d._wrapperState)&&T.controlled&&d.type==="number"&&Ma(d,"number",d.value)}switch(T=u?Kn(u):window,e){case"focusin":(kc(T)||T.contentEditable==="true")&&(Hn=T,$a=u,Gr=null);break;case"focusout":Gr=$a=Hn=null;break;case"mousedown":Wa=!0;break;case"contextmenu":case"mouseup":case"dragend":Wa=!1,Mc(f,n,c);break;case"selectionchange":if(o0)break;case"keydown":case"keyup":Mc(f,n,c)}var E;if(nu)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Wn?Dp(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(_p&&n.locale!=="ko"&&(Wn||k!=="onCompositionStart"?k==="onCompositionEnd"&&Wn&&(E=jp()):(Bt=c,Jl="value"in Bt?Bt.value:Bt.textContent,Wn=!0)),T=Ni(u,k),0<T.length&&(k=new Cc(k,e,null,n,c),f.push({event:k,listeners:T}),E?k.data=E:(E=Vp(n),E!==null&&(k.data=E)))),(E=Gy?Ky(e,n):Qy(e,n))&&(u=Ni(u,"onBeforeInput"),0<u.length&&(c=new Cc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=E))}Kp(f,t)})}function fo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ni(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=oo(e,n),i!=null&&r.unshift(fo(e,i,o)),i=oo(e,t),i!=null&&r.push(fo(e,i,o))),e=e.return}return r}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Dc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=oo(n,i),l!=null&&s.unshift(fo(n,l,a))):o||(l=oo(n,i),l!=null&&s.push(fo(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var l0=/\r\n?/g,u0=/\u0000|\uFFFD/g;function Vc(e){return(typeof e=="string"?e:""+e).replace(l0,`
`).replace(u0,"")}function Qo(e,t,n){if(t=Vc(t),Vc(e)!==t&&n)throw Error(R(425))}function Ai(){}var Ha=null,Ga=null;function Ka(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Qa=typeof setTimeout=="function"?setTimeout:void 0,c0=typeof clearTimeout=="function"?clearTimeout:void 0,Oc=typeof Promise=="function"?Promise:void 0,f0=typeof queueMicrotask=="function"?queueMicrotask:typeof Oc<"u"?function(e){return Oc.resolve(null).then(e).catch(d0)}:Qa;function d0(e){setTimeout(function(){throw e})}function Zs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ao(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ao(t)}function Kt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function bc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Cr=Math.random().toString(36).slice(2),ht="__reactFiber$"+Cr,po="__reactProps$"+Cr,Lt="__reactContainer$"+Cr,Xa="__reactEvents$"+Cr,p0="__reactListeners$"+Cr,h0="__reactHandles$"+Cr;function Sn(e){var t=e[ht];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Lt]||n[ht]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=bc(e);e!==null;){if(n=e[ht])return n;e=bc(e)}return t}e=n,n=e.parentNode}return null}function No(e){return e=e[ht]||e[Lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Kn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function cs(e){return e[po]||null}var Ya=[],Qn=-1;function ln(e){return{current:e}}function Y(e){0>Qn||(e.current=Ya[Qn],Ya[Qn]=null,Qn--)}function G(e,t){Qn++,Ya[Qn]=e.current,e.current=t}var nn={},Pe=ln(nn),_e=ln(!1),Ln=nn;function fr(e,t){var n=e.type.contextTypes;if(!n)return nn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function De(e){return e=e.childContextTypes,e!=null}function Mi(){Y(_e),Y(Pe)}function Ic(e,t,n){if(Pe.current!==nn)throw Error(R(168));G(Pe,t),G(_e,n)}function Xp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,qv(e)||"Unknown",o));return re({},n,r)}function ji(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||nn,Ln=Pe.current,G(Pe,e),G(_e,_e.current),!0}function Fc(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=Xp(e,t,Ln),r.__reactInternalMemoizedMergedChildContext=e,Y(_e),Y(Pe),G(Pe,e)):Y(_e),G(_e,n)}var wt=null,fs=!1,qs=!1;function Yp(e){wt===null?wt=[e]:wt.push(e)}function m0(e){fs=!0,Yp(e)}function un(){if(!qs&&wt!==null){qs=!0;var e=0,t=W;try{var n=wt;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}wt=null,fs=!1}catch(o){throw wt!==null&&(wt=wt.slice(e+1)),Sp(Xl,un),o}finally{W=t,qs=!1}}return null}var Xn=[],Yn=0,_i=null,Di=0,Ye=[],Ze=0,Nn=null,St=1,Ct="";function gn(e,t){Xn[Yn++]=Di,Xn[Yn++]=_i,_i=e,Di=t}function Zp(e,t,n){Ye[Ze++]=St,Ye[Ze++]=Ct,Ye[Ze++]=Nn,Nn=e;var r=St;e=Ct;var o=32-at(r)-1;r&=~(1<<o),n+=1;var i=32-at(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,St=1<<32-at(t)+o|n<<o|r,Ct=i+e}else St=1<<i|n<<o|r,Ct=e}function ou(e){e.return!==null&&(gn(e,1),Zp(e,1,0))}function iu(e){for(;e===_i;)_i=Xn[--Yn],Xn[Yn]=null,Di=Xn[--Yn],Xn[Yn]=null;for(;e===Nn;)Nn=Ye[--Ze],Ye[Ze]=null,Ct=Ye[--Ze],Ye[Ze]=null,St=Ye[--Ze],Ye[Ze]=null}var Ue=null,Be=null,q=!1,st=null;function qp(e,t){var n=qe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function zc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ue=e,Be=Kt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ue=e,Be=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Nn!==null?{id:St,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=qe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ue=e,Be=null,!0):!1;default:return!1}}function Za(e){return(e.mode&1)!==0&&(e.flags&128)===0}function qa(e){if(q){var t=Be;if(t){var n=t;if(!zc(e,t)){if(Za(e))throw Error(R(418));t=Kt(n.nextSibling);var r=Ue;t&&zc(e,t)?qp(r,n):(e.flags=e.flags&-4097|2,q=!1,Ue=e)}}else{if(Za(e))throw Error(R(418));e.flags=e.flags&-4097|2,q=!1,Ue=e}}}function Bc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ue=e}function Xo(e){if(e!==Ue)return!1;if(!q)return Bc(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ka(e.type,e.memoizedProps)),t&&(t=Be)){if(Za(e))throw Jp(),Error(R(418));for(;t;)qp(e,t),t=Kt(t.nextSibling)}if(Bc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Be=Kt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Be=null}}else Be=Ue?Kt(e.stateNode.nextSibling):null;return!0}function Jp(){for(var e=Be;e;)e=Kt(e.nextSibling)}function dr(){Be=Ue=null,q=!1}function su(e){st===null?st=[e]:st.push(e)}var g0=jt.ReactCurrentBatchConfig;function Ar(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Yo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Uc(e){var t=e._init;return t(e._payload)}function eh(e){function t(g,p){if(e){var h=g.deletions;h===null?(g.deletions=[p],g.flags|=16):h.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function o(g,p){return g=Zt(g,p),g.index=0,g.sibling=null,g}function i(g,p,h){return g.index=h,e?(h=g.alternate,h!==null?(h=h.index,h<p?(g.flags|=2,p):h):(g.flags|=2,p)):(g.flags|=1048576,p)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,p,h,w){return p===null||p.tag!==6?(p=ia(h,g.mode,w),p.return=g,p):(p=o(p,h),p.return=g,p)}function l(g,p,h,w){var P=h.type;return P===$n?c(g,p,h.props.children,w,h.key):p!==null&&(p.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Ot&&Uc(P)===p.type)?(w=o(p,h.props),w.ref=Ar(g,p,h),w.return=g,w):(w=xi(h.type,h.key,h.props,null,g.mode,w),w.ref=Ar(g,p,h),w.return=g,w)}function u(g,p,h,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=sa(h,g.mode,w),p.return=g,p):(p=o(p,h.children||[]),p.return=g,p)}function c(g,p,h,w,P){return p===null||p.tag!==7?(p=Rn(h,g.mode,w,P),p.return=g,p):(p=o(p,h),p.return=g,p)}function f(g,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ia(""+p,g.mode,h),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Fo:return h=xi(p.type,p.key,p.props,null,g.mode,h),h.ref=Ar(g,null,p),h.return=g,h;case Un:return p=sa(p,g.mode,h),p.return=g,p;case Ot:var w=p._init;return f(g,w(p._payload),h)}if(Ir(p)||Tr(p))return p=Rn(p,g.mode,h,null),p.return=g,p;Yo(g,p)}return null}function d(g,p,h,w){var P=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return P!==null?null:a(g,p,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Fo:return h.key===P?l(g,p,h,w):null;case Un:return h.key===P?u(g,p,h,w):null;case Ot:return P=h._init,d(g,p,P(h._payload),w)}if(Ir(h)||Tr(h))return P!==null?null:c(g,p,h,w,null);Yo(g,h)}return null}function m(g,p,h,w,P){if(typeof w=="string"&&w!==""||typeof w=="number")return g=g.get(h)||null,a(p,g,""+w,P);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Fo:return g=g.get(w.key===null?h:w.key)||null,l(p,g,w,P);case Un:return g=g.get(w.key===null?h:w.key)||null,u(p,g,w,P);case Ot:var T=w._init;return m(g,p,h,T(w._payload),P)}if(Ir(w)||Tr(w))return g=g.get(h)||null,c(p,g,w,P,null);Yo(p,w)}return null}function x(g,p,h,w){for(var P=null,T=null,E=p,k=p=0,N=null;E!==null&&k<h.length;k++){E.index>k?(N=E,E=null):N=E.sibling;var M=d(g,E,h[k],w);if(M===null){E===null&&(E=N);break}e&&E&&M.alternate===null&&t(g,E),p=i(M,p,k),T===null?P=M:T.sibling=M,T=M,E=N}if(k===h.length)return n(g,E),q&&gn(g,k),P;if(E===null){for(;k<h.length;k++)E=f(g,h[k],w),E!==null&&(p=i(E,p,k),T===null?P=E:T.sibling=E,T=E);return q&&gn(g,k),P}for(E=r(g,E);k<h.length;k++)N=m(E,g,k,h[k],w),N!==null&&(e&&N.alternate!==null&&E.delete(N.key===null?k:N.key),p=i(N,p,k),T===null?P=N:T.sibling=N,T=N);return e&&E.forEach(function($){return t(g,$)}),q&&gn(g,k),P}function y(g,p,h,w){var P=Tr(h);if(typeof P!="function")throw Error(R(150));if(h=P.call(h),h==null)throw Error(R(151));for(var T=P=null,E=p,k=p=0,N=null,M=h.next();E!==null&&!M.done;k++,M=h.next()){E.index>k?(N=E,E=null):N=E.sibling;var $=d(g,E,M.value,w);if($===null){E===null&&(E=N);break}e&&E&&$.alternate===null&&t(g,E),p=i($,p,k),T===null?P=$:T.sibling=$,T=$,E=N}if(M.done)return n(g,E),q&&gn(g,k),P;if(E===null){for(;!M.done;k++,M=h.next())M=f(g,M.value,w),M!==null&&(p=i(M,p,k),T===null?P=M:T.sibling=M,T=M);return q&&gn(g,k),P}for(E=r(g,E);!M.done;k++,M=h.next())M=m(E,g,k,M.value,w),M!==null&&(e&&M.alternate!==null&&E.delete(M.key===null?k:M.key),p=i(M,p,k),T===null?P=M:T.sibling=M,T=M);return e&&E.forEach(function(V){return t(g,V)}),q&&gn(g,k),P}function S(g,p,h,w){if(typeof h=="object"&&h!==null&&h.type===$n&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Fo:e:{for(var P=h.key,T=p;T!==null;){if(T.key===P){if(P=h.type,P===$n){if(T.tag===7){n(g,T.sibling),p=o(T,h.props.children),p.return=g,g=p;break e}}else if(T.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Ot&&Uc(P)===T.type){n(g,T.sibling),p=o(T,h.props),p.ref=Ar(g,T,h),p.return=g,g=p;break e}n(g,T);break}else t(g,T);T=T.sibling}h.type===$n?(p=Rn(h.props.children,g.mode,w,h.key),p.return=g,g=p):(w=xi(h.type,h.key,h.props,null,g.mode,w),w.ref=Ar(g,p,h),w.return=g,g=w)}return s(g);case Un:e:{for(T=h.key;p!==null;){if(p.key===T)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(g,p.sibling),p=o(p,h.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=sa(h,g.mode,w),p.return=g,g=p}return s(g);case Ot:return T=h._init,S(g,p,T(h._payload),w)}if(Ir(h))return x(g,p,h,w);if(Tr(h))return y(g,p,h,w);Yo(g,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(g,p.sibling),p=o(p,h),p.return=g,g=p):(n(g,p),p=ia(h,g.mode,w),p.return=g,g=p),s(g)):n(g,p)}return S}var pr=eh(!0),th=eh(!1),Vi=ln(null),Oi=null,Zn=null,au=null;function lu(){au=Zn=Oi=null}function uu(e){var t=Vi.current;Y(Vi),e._currentValue=t}function Ja(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lr(e,t){Oi=e,au=Zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(je=!0),e.firstContext=null)}function et(e){var t=e._currentValue;if(au!==e)if(e={context:e,memoizedValue:t,next:null},Zn===null){if(Oi===null)throw Error(R(308));Zn=e,Oi.dependencies={lanes:0,firstContext:e}}else Zn=Zn.next=e;return t}var Cn=null;function cu(e){Cn===null?Cn=[e]:Cn.push(e)}function nh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,cu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Nt(e,r)}function Nt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var bt=!1;function fu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function rh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Qt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Nt(e,n)}return o=r.interleaved,o===null?(t.next=t,cu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Nt(e,n)}function pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Yl(e,n)}}function $c(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function bi(e,t,n,r){var o=e.updateQueue;bt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var f=o.baseState;s=0,c=u=l=null,a=i;do{var d=a.lane,m=a.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,y=a;switch(d=t,m=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){f=x.call(m,f,d);break e}f=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,d=typeof x=="function"?x.call(m,f,d):x,d==null)break e;f=re({},f,d);break e;case 2:bt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else m={eventTime:m,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=m,l=f):c=c.next=m,s|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(1);if(c===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Mn|=s,e.lanes=s,e.memoizedState=f}}function Wc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Ao={},gt=ln(Ao),ho=ln(Ao),mo=ln(Ao);function Pn(e){if(e===Ao)throw Error(R(174));return e}function du(e,t){switch(G(mo,t),G(ho,e),G(gt,Ao),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:_a(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=_a(t,e)}Y(gt),G(gt,t)}function hr(){Y(gt),Y(ho),Y(mo)}function oh(e){Pn(mo.current);var t=Pn(gt.current),n=_a(t,e.type);t!==n&&(G(ho,e),G(gt,n))}function pu(e){ho.current===e&&(Y(gt),Y(ho))}var ee=ln(0);function Ii(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Js=[];function hu(){for(var e=0;e<Js.length;e++)Js[e]._workInProgressVersionPrimary=null;Js.length=0}var hi=jt.ReactCurrentDispatcher,ea=jt.ReactCurrentBatchConfig,An=0,ne=null,fe=null,he=null,Fi=!1,Kr=!1,go=0,v0=0;function xe(){throw Error(R(321))}function mu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ut(e[n],t[n]))return!1;return!0}function gu(e,t,n,r,o,i){if(An=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,hi.current=e===null||e.memoizedState===null?S0:C0,e=n(r,o),Kr){i=0;do{if(Kr=!1,go=0,25<=i)throw Error(R(301));i+=1,he=fe=null,t.updateQueue=null,hi.current=P0,e=n(r,o)}while(Kr)}if(hi.current=zi,t=fe!==null&&fe.next!==null,An=0,he=fe=ne=null,Fi=!1,t)throw Error(R(300));return e}function vu(){var e=go!==0;return go=0,e}function dt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return he===null?ne.memoizedState=he=e:he=he.next=e,he}function tt(){if(fe===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=fe.next;var t=he===null?ne.memoizedState:he.next;if(t!==null)he=t,fe=e;else{if(e===null)throw Error(R(310));fe=e,e={memoizedState:fe.memoizedState,baseState:fe.baseState,baseQueue:fe.baseQueue,queue:fe.queue,next:null},he===null?ne.memoizedState=he=e:he=he.next=e}return he}function vo(e,t){return typeof t=="function"?t(e):t}function ta(e){var t=tt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=fe,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var c=u.lane;if((An&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,ne.lanes|=c,Mn|=c}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,ut(r,t.memoizedState)||(je=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ne.lanes|=i,Mn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function na(e){var t=tt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);ut(i,t.memoizedState)||(je=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ih(){}function sh(e,t){var n=ne,r=tt(),o=t(),i=!ut(r.memoizedState,o);if(i&&(r.memoizedState=o,je=!0),r=r.queue,yu(uh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||he!==null&&he.memoizedState.tag&1){if(n.flags|=2048,yo(9,lh.bind(null,n,r,o,t),void 0,null),me===null)throw Error(R(349));An&30||ah(n,t,o)}return o}function ah(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function lh(e,t,n,r){t.value=n,t.getSnapshot=r,ch(t)&&fh(e)}function uh(e,t,n){return n(function(){ch(t)&&fh(e)})}function ch(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ut(e,n)}catch{return!0}}function fh(e){var t=Nt(e,1);t!==null&&lt(t,e,1,-1)}function Hc(e){var t=dt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vo,lastRenderedState:e},t.queue=e,e=e.dispatch=w0.bind(null,ne,e),[t.memoizedState,e]}function yo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function dh(){return tt().memoizedState}function mi(e,t,n,r){var o=dt();ne.flags|=e,o.memoizedState=yo(1|t,n,void 0,r===void 0?null:r)}function ds(e,t,n,r){var o=tt();r=r===void 0?null:r;var i=void 0;if(fe!==null){var s=fe.memoizedState;if(i=s.destroy,r!==null&&mu(r,s.deps)){o.memoizedState=yo(t,n,i,r);return}}ne.flags|=e,o.memoizedState=yo(1|t,n,i,r)}function Gc(e,t){return mi(8390656,8,e,t)}function yu(e,t){return ds(2048,8,e,t)}function ph(e,t){return ds(4,2,e,t)}function hh(e,t){return ds(4,4,e,t)}function mh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function gh(e,t,n){return n=n!=null?n.concat([e]):null,ds(4,4,mh.bind(null,t,e),n)}function xu(){}function vh(e,t){var n=tt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&mu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function yh(e,t){var n=tt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&mu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function xh(e,t,n){return An&21?(ut(n,t)||(n=Ep(),ne.lanes|=n,Mn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,je=!0),e.memoizedState=n)}function y0(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=ea.transition;ea.transition={};try{e(!1),t()}finally{W=n,ea.transition=r}}function wh(){return tt().memoizedState}function x0(e,t,n){var r=Yt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Sh(e))Ch(t,n);else if(n=nh(e,t,n,r),n!==null){var o=Le();lt(n,e,r,o),Ph(n,t,r)}}function w0(e,t,n){var r=Yt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Sh(e))Ch(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,ut(a,s)){var l=t.interleaved;l===null?(o.next=o,cu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=nh(e,t,o,r),n!==null&&(o=Le(),lt(n,e,r,o),Ph(n,t,r))}}function Sh(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function Ch(e,t){Kr=Fi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ph(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Yl(e,n)}}var zi={readContext:et,useCallback:xe,useContext:xe,useEffect:xe,useImperativeHandle:xe,useInsertionEffect:xe,useLayoutEffect:xe,useMemo:xe,useReducer:xe,useRef:xe,useState:xe,useDebugValue:xe,useDeferredValue:xe,useTransition:xe,useMutableSource:xe,useSyncExternalStore:xe,useId:xe,unstable_isNewReconciler:!1},S0={readContext:et,useCallback:function(e,t){return dt().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:Gc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,mi(4194308,4,mh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return mi(4,2,e,t)},useMemo:function(e,t){var n=dt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=dt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=x0.bind(null,ne,e),[r.memoizedState,e]},useRef:function(e){var t=dt();return e={current:e},t.memoizedState=e},useState:Hc,useDebugValue:xu,useDeferredValue:function(e){return dt().memoizedState=e},useTransition:function(){var e=Hc(!1),t=e[0];return e=y0.bind(null,e[1]),dt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ne,o=dt();if(q){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),me===null)throw Error(R(349));An&30||ah(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Gc(uh.bind(null,r,i,e),[e]),r.flags|=2048,yo(9,lh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=dt(),t=me.identifierPrefix;if(q){var n=Ct,r=St;n=(r&~(1<<32-at(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=go++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=v0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},C0={readContext:et,useCallback:vh,useContext:et,useEffect:yu,useImperativeHandle:gh,useInsertionEffect:ph,useLayoutEffect:hh,useMemo:yh,useReducer:ta,useRef:dh,useState:function(){return ta(vo)},useDebugValue:xu,useDeferredValue:function(e){var t=tt();return xh(t,fe.memoizedState,e)},useTransition:function(){var e=ta(vo)[0],t=tt().memoizedState;return[e,t]},useMutableSource:ih,useSyncExternalStore:sh,useId:wh,unstable_isNewReconciler:!1},P0={readContext:et,useCallback:vh,useContext:et,useEffect:yu,useImperativeHandle:gh,useInsertionEffect:ph,useLayoutEffect:hh,useMemo:yh,useReducer:na,useRef:dh,useState:function(){return na(vo)},useDebugValue:xu,useDeferredValue:function(e){var t=tt();return fe===null?t.memoizedState=e:xh(t,fe.memoizedState,e)},useTransition:function(){var e=na(vo)[0],t=tt().memoizedState;return[e,t]},useMutableSource:ih,useSyncExternalStore:sh,useId:wh,unstable_isNewReconciler:!1};function ot(e,t){if(e&&e.defaultProps){t=re({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function el(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:re({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ps={isMounted:function(e){return(e=e._reactInternals)?Vn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Le(),o=Yt(e),i=Et(r,o);i.payload=t,n!=null&&(i.callback=n),t=Qt(e,i,o),t!==null&&(lt(t,e,o,r),pi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Le(),o=Yt(e),i=Et(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Qt(e,i,o),t!==null&&(lt(t,e,o,r),pi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Le(),r=Yt(e),o=Et(n,r);o.tag=2,t!=null&&(o.callback=t),t=Qt(e,o,r),t!==null&&(lt(t,e,r,n),pi(t,e,r))}};function Kc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!uo(n,r)||!uo(o,i):!0}function Eh(e,t,n){var r=!1,o=nn,i=t.contextType;return typeof i=="object"&&i!==null?i=et(i):(o=De(t)?Ln:Pe.current,r=t.contextTypes,i=(r=r!=null)?fr(e,o):nn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ps,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Qc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ps.enqueueReplaceState(t,t.state,null)}function tl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},fu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=et(i):(i=De(t)?Ln:Pe.current,o.context=fr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(el(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ps.enqueueReplaceState(o,o.state,null),bi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function mr(e,t){try{var n="",r=t;do n+=Zv(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ra(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function nl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var E0=typeof WeakMap=="function"?WeakMap:Map;function Th(e,t,n){n=Et(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ui||(Ui=!0,dl=r),nl(e,t)},n}function kh(e,t,n){n=Et(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){nl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){nl(e,t),typeof r!="function"&&(Xt===null?Xt=new Set([this]):Xt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Xc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new E0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=I0.bind(null,e,t,n),t.then(e,e))}function Yc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Zc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Et(-1,1),t.tag=2,Qt(n,t,1))),n.lanes|=1),e)}var T0=jt.ReactCurrentOwner,je=!1;function Re(e,t,n,r){t.child=e===null?th(t,null,n,r):pr(t,e.child,n,r)}function qc(e,t,n,r,o){n=n.render;var i=t.ref;return lr(t,o),r=gu(e,t,n,r,i,o),n=vu(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,At(e,t,o)):(q&&n&&ou(t),t.flags|=1,Re(e,t,r,o),t.child)}function Jc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ru(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Rh(e,t,i,r,o)):(e=xi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:uo,n(s,r)&&e.ref===t.ref)return At(e,t,o)}return t.flags|=1,e=Zt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Rh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(uo(i,r)&&e.ref===t.ref)if(je=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(je=!0);else return t.lanes=e.lanes,At(e,t,o)}return rl(e,t,n,r,o)}function Lh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(Jn,Fe),Fe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,G(Jn,Fe),Fe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,G(Jn,Fe),Fe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,G(Jn,Fe),Fe|=r;return Re(e,t,o,n),t.child}function Nh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function rl(e,t,n,r,o){var i=De(n)?Ln:Pe.current;return i=fr(t,i),lr(t,o),n=gu(e,t,n,r,i,o),r=vu(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,At(e,t,o)):(q&&r&&ou(t),t.flags|=1,Re(e,t,n,o),t.child)}function ef(e,t,n,r,o){if(De(n)){var i=!0;ji(t)}else i=!1;if(lr(t,o),t.stateNode===null)gi(e,t),Eh(t,n,r),tl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=et(u):(u=De(n)?Ln:Pe.current,u=fr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Qc(t,s,r,u),bt=!1;var d=t.memoizedState;s.state=d,bi(t,r,s,o),l=t.memoizedState,a!==r||d!==l||_e.current||bt?(typeof c=="function"&&(el(t,n,c,r),l=t.memoizedState),(a=bt||Kc(t,n,a,r,d,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,rh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:ot(t.type,a),s.props=u,f=t.pendingProps,d=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=et(l):(l=De(n)?Ln:Pe.current,l=fr(t,l));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||d!==l)&&Qc(t,s,r,l),bt=!1,d=t.memoizedState,s.state=d,bi(t,r,s,o);var x=t.memoizedState;a!==f||d!==x||_e.current||bt?(typeof m=="function"&&(el(t,n,m,r),x=t.memoizedState),(u=bt||Kc(t,n,u,r,d,x,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return ol(e,t,n,r,i,o)}function ol(e,t,n,r,o,i){Nh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Fc(t,n,!1),At(e,t,i);r=t.stateNode,T0.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=pr(t,e.child,null,i),t.child=pr(t,null,a,i)):Re(e,t,a,i),t.memoizedState=r.state,o&&Fc(t,n,!0),t.child}function Ah(e){var t=e.stateNode;t.pendingContext?Ic(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ic(e,t.context,!1),du(e,t.containerInfo)}function tf(e,t,n,r,o){return dr(),su(o),t.flags|=256,Re(e,t,n,r),t.child}var il={dehydrated:null,treeContext:null,retryLane:0};function sl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Mh(e,t,n){var r=t.pendingProps,o=ee.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),G(ee,o&1),e===null)return qa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=gs(s,r,0,null),e=Rn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=sl(n),t.memoizedState=il,e):wu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return k0(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Zt(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Zt(a,i):(i=Rn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?sl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=il,r}return i=e.child,e=i.sibling,r=Zt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function wu(e,t){return t=gs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Zo(e,t,n,r){return r!==null&&su(r),pr(t,e.child,null,n),e=wu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function k0(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=ra(Error(R(422))),Zo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=gs({mode:"visible",children:r.children},o,0,null),i=Rn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&pr(t,e.child,null,s),t.child.memoizedState=sl(s),t.memoizedState=il,i);if(!(t.mode&1))return Zo(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=ra(i,r,void 0),Zo(e,t,s,r)}if(a=(s&e.childLanes)!==0,je||a){if(r=me,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Nt(e,o),lt(r,e,o,-1))}return ku(),r=ra(Error(R(421))),Zo(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=F0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Be=Kt(o.nextSibling),Ue=t,q=!0,st=null,e!==null&&(Ye[Ze++]=St,Ye[Ze++]=Ct,Ye[Ze++]=Nn,St=e.id,Ct=e.overflow,Nn=t),t=wu(t,r.children),t.flags|=4096,t)}function nf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ja(e.return,t,n)}function oa(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function jh(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Re(e,t,r.children,n),r=ee.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&nf(e,n,t);else if(e.tag===19)nf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(G(ee,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ii(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),oa(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ii(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}oa(t,!0,n,null,i);break;case"together":oa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function At(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Zt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Zt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function R0(e,t,n){switch(t.tag){case 3:Ah(t),dr();break;case 5:oh(t);break;case 1:De(t.type)&&ji(t);break;case 4:du(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;G(Vi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(G(ee,ee.current&1),t.flags|=128,null):n&t.child.childLanes?Mh(e,t,n):(G(ee,ee.current&1),e=At(e,t,n),e!==null?e.sibling:null);G(ee,ee.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return jh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),G(ee,ee.current),r)break;return null;case 22:case 23:return t.lanes=0,Lh(e,t,n)}return At(e,t,n)}var _h,al,Dh,Vh;_h=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};al=function(){};Dh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Pn(gt.current);var i=null;switch(n){case"input":o=Na(e,o),r=Na(e,r),i=[];break;case"select":o=re({},o,{value:void 0}),r=re({},r,{value:void 0}),i=[];break;case"textarea":o=ja(e,o),r=ja(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ai)}Da(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(no.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(no.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&X("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Vh=function(e,t,n,r){n!==r&&(t.flags|=4)};function Mr(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function L0(e,t,n){var r=t.pendingProps;switch(iu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return De(t.type)&&Mi(),we(t),null;case 3:return r=t.stateNode,hr(),Y(_e),Y(Pe),hu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Xo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,st!==null&&(ml(st),st=null))),al(e,t),we(t),null;case 5:pu(t);var o=Pn(mo.current);if(n=t.type,e!==null&&t.stateNode!=null)Dh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return we(t),null}if(e=Pn(gt.current),Xo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ht]=t,r[po]=i,e=(t.mode&1)!==0,n){case"dialog":X("cancel",r),X("close",r);break;case"iframe":case"object":case"embed":X("load",r);break;case"video":case"audio":for(o=0;o<zr.length;o++)X(zr[o],r);break;case"source":X("error",r);break;case"img":case"image":case"link":X("error",r),X("load",r);break;case"details":X("toggle",r);break;case"input":fc(r,i),X("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},X("invalid",r);break;case"textarea":pc(r,i),X("invalid",r)}Da(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Qo(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Qo(r.textContent,a,e),o=["children",""+a]):no.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&X("scroll",r)}switch(n){case"input":zo(r),dc(r,i,!0);break;case"textarea":zo(r),hc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ai)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=up(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[ht]=t,e[po]=r,_h(e,t,!1,!1),t.stateNode=e;e:{switch(s=Va(n,r),n){case"dialog":X("cancel",e),X("close",e),o=r;break;case"iframe":case"object":case"embed":X("load",e),o=r;break;case"video":case"audio":for(o=0;o<zr.length;o++)X(zr[o],e);o=r;break;case"source":X("error",e),o=r;break;case"img":case"image":case"link":X("error",e),X("load",e),o=r;break;case"details":X("toggle",e),o=r;break;case"input":fc(e,r),o=Na(e,r),X("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=re({},r,{value:void 0}),X("invalid",e);break;case"textarea":pc(e,r),o=ja(e,r),X("invalid",e);break;default:o=r}Da(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?dp(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&cp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ro(e,l):typeof l=="number"&&ro(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(no.hasOwnProperty(i)?l!=null&&i==="onScroll"&&X("scroll",e):l!=null&&Wl(e,i,l,s))}switch(n){case"input":zo(e),dc(e,r,!1);break;case"textarea":zo(e),hc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+tn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?or(e,!!r.multiple,i,!1):r.defaultValue!=null&&or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ai)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return we(t),null;case 6:if(e&&t.stateNode!=null)Vh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Pn(mo.current),Pn(gt.current),Xo(t)){if(r=t.stateNode,n=t.memoizedProps,r[ht]=t,(i=r.nodeValue!==n)&&(e=Ue,e!==null))switch(e.tag){case 3:Qo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Qo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ht]=t,t.stateNode=r}return we(t),null;case 13:if(Y(ee),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Be!==null&&t.mode&1&&!(t.flags&128))Jp(),dr(),t.flags|=98560,i=!1;else if(i=Xo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[ht]=t}else dr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;we(t),i=!1}else st!==null&&(ml(st),st=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ee.current&1?pe===0&&(pe=3):ku())),t.updateQueue!==null&&(t.flags|=4),we(t),null);case 4:return hr(),al(e,t),e===null&&co(t.stateNode.containerInfo),we(t),null;case 10:return uu(t.type._context),we(t),null;case 17:return De(t.type)&&Mi(),we(t),null;case 19:if(Y(ee),i=t.memoizedState,i===null)return we(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Mr(i,!1);else{if(pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ii(e),s!==null){for(t.flags|=128,Mr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return G(ee,ee.current&1|2),t.child}e=e.sibling}i.tail!==null&&le()>gr&&(t.flags|=128,r=!0,Mr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ii(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Mr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!q)return we(t),null}else 2*le()-i.renderingStartTime>gr&&n!==1073741824&&(t.flags|=128,r=!0,Mr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=le(),t.sibling=null,n=ee.current,G(ee,r?n&1|2:n&1),t):(we(t),null);case 22:case 23:return Tu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Fe&1073741824&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function N0(e,t){switch(iu(t),t.tag){case 1:return De(t.type)&&Mi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return hr(),Y(_e),Y(Pe),hu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return pu(t),null;case 13:if(Y(ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));dr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Y(ee),null;case 4:return hr(),null;case 10:return uu(t.type._context),null;case 22:case 23:return Tu(),null;case 24:return null;default:return null}}var qo=!1,Ce=!1,A0=typeof WeakSet=="function"?WeakSet:Set,A=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(e,t,r)}else n.current=null}function ll(e,t,n){try{n()}catch(r){oe(e,t,r)}}var rf=!1;function M0(e,t){if(Ha=Ri,e=zp(),ru(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||o!==0&&f.nodeType!==3||(a=s+o),f!==i||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++u===o&&(a=s),d===i&&++c===r&&(l=s),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ga={focusedElem:e,selectionRange:n},Ri=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,S=x.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?y:ot(t.type,y),S);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(w){oe(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return x=rf,rf=!1,x}function Qr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&ll(t,n,i)}o=o.next}while(o!==r)}}function hs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ul(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Oh(e){var t=e.alternate;t!==null&&(e.alternate=null,Oh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ht],delete t[po],delete t[Xa],delete t[p0],delete t[h0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function bh(e){return e.tag===5||e.tag===3||e.tag===4}function of(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function cl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ai));else if(r!==4&&(e=e.child,e!==null))for(cl(e,t,n),e=e.sibling;e!==null;)cl(e,t,n),e=e.sibling}function fl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fl(e,t,n),e=e.sibling;e!==null;)fl(e,t,n),e=e.sibling}var ge=null,it=!1;function _t(e,t,n){for(n=n.child;n!==null;)Ih(e,t,n),n=n.sibling}function Ih(e,t,n){if(mt&&typeof mt.onCommitFiberUnmount=="function")try{mt.onCommitFiberUnmount(ss,n)}catch{}switch(n.tag){case 5:Ce||qn(n,t);case 6:var r=ge,o=it;ge=null,_t(e,t,n),ge=r,it=o,ge!==null&&(it?(e=ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ge.removeChild(n.stateNode));break;case 18:ge!==null&&(it?(e=ge,n=n.stateNode,e.nodeType===8?Zs(e.parentNode,n):e.nodeType===1&&Zs(e,n),ao(e)):Zs(ge,n.stateNode));break;case 4:r=ge,o=it,ge=n.stateNode.containerInfo,it=!0,_t(e,t,n),ge=r,it=o;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&ll(n,t,s),o=o.next}while(o!==r)}_t(e,t,n);break;case 1:if(!Ce&&(qn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){oe(n,t,a)}_t(e,t,n);break;case 21:_t(e,t,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,_t(e,t,n),Ce=r):_t(e,t,n);break;default:_t(e,t,n)}}function sf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new A0),t.forEach(function(r){var o=z0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function nt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ge=a.stateNode,it=!1;break e;case 3:ge=a.stateNode.containerInfo,it=!0;break e;case 4:ge=a.stateNode.containerInfo,it=!0;break e}a=a.return}if(ge===null)throw Error(R(160));Ih(i,s,o),ge=null,it=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){oe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Fh(t,e),t=t.sibling}function Fh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),ft(e),r&4){try{Qr(3,e,e.return),hs(3,e)}catch(y){oe(e,e.return,y)}try{Qr(5,e,e.return)}catch(y){oe(e,e.return,y)}}break;case 1:nt(t,e),ft(e),r&512&&n!==null&&qn(n,n.return);break;case 5:if(nt(t,e),ft(e),r&512&&n!==null&&qn(n,n.return),e.flags&32){var o=e.stateNode;try{ro(o,"")}catch(y){oe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&ap(o,i),Va(a,s);var u=Va(a,i);for(s=0;s<l.length;s+=2){var c=l[s],f=l[s+1];c==="style"?dp(o,f):c==="dangerouslySetInnerHTML"?cp(o,f):c==="children"?ro(o,f):Wl(o,c,f,u)}switch(a){case"input":Aa(o,i);break;case"textarea":lp(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?or(o,!!i.multiple,m,!1):d!==!!i.multiple&&(i.defaultValue!=null?or(o,!!i.multiple,i.defaultValue,!0):or(o,!!i.multiple,i.multiple?[]:"",!1))}o[po]=i}catch(y){oe(e,e.return,y)}}break;case 6:if(nt(t,e),ft(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){oe(e,e.return,y)}}break;case 3:if(nt(t,e),ft(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ao(t.containerInfo)}catch(y){oe(e,e.return,y)}break;case 4:nt(t,e),ft(e);break;case 13:nt(t,e),ft(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Pu=le())),r&4&&sf(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ce=(u=Ce)||c,nt(t,e),Ce=u):nt(t,e),ft(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(A=e,c=e.child;c!==null;){for(f=A=c;A!==null;){switch(d=A,m=d.child,d.tag){case 0:case 11:case 14:case 15:Qr(4,d,d.return);break;case 1:qn(d,d.return);var x=d.stateNode;if(typeof x.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){oe(r,n,y)}}break;case 5:qn(d,d.return);break;case 22:if(d.memoizedState!==null){lf(f);continue}}m!==null?(m.return=d,A=m):lf(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=fp("display",s))}catch(y){oe(e,e.return,y)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){oe(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:nt(t,e),ft(e),r&4&&sf(e);break;case 21:break;default:nt(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(bh(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ro(o,""),r.flags&=-33);var i=of(e);fl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=of(e);cl(e,a,s);break;default:throw Error(R(161))}}catch(l){oe(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function j0(e,t,n){A=e,zh(e)}function zh(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var o=A,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||qo;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||Ce;a=qo;var u=Ce;if(qo=s,(Ce=l)&&!u)for(A=o;A!==null;)s=A,l=s.child,s.tag===22&&s.memoizedState!==null?uf(o):l!==null?(l.return=s,A=l):uf(o);for(;i!==null;)A=i,zh(i),i=i.sibling;A=o,qo=a,Ce=u}af(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,A=i):af(e)}}function af(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ce||hs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ot(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Wc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Wc(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&ao(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ce||t.flags&512&&ul(t)}catch(d){oe(t,t.return,d)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function lf(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function uf(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{hs(4,t)}catch(l){oe(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){oe(t,o,l)}}var i=t.return;try{ul(t)}catch(l){oe(t,i,l)}break;case 5:var s=t.return;try{ul(t)}catch(l){oe(t,s,l)}}}catch(l){oe(t,t.return,l)}if(t===e){A=null;break}var a=t.sibling;if(a!==null){a.return=t.return,A=a;break}A=t.return}}var _0=Math.ceil,Bi=jt.ReactCurrentDispatcher,Su=jt.ReactCurrentOwner,Je=jt.ReactCurrentBatchConfig,U=0,me=null,ce=null,ve=0,Fe=0,Jn=ln(0),pe=0,xo=null,Mn=0,ms=0,Cu=0,Xr=null,Me=null,Pu=0,gr=1/0,xt=null,Ui=!1,dl=null,Xt=null,Jo=!1,Ut=null,$i=0,Yr=0,pl=null,vi=-1,yi=0;function Le(){return U&6?le():vi!==-1?vi:vi=le()}function Yt(e){return e.mode&1?U&2&&ve!==0?ve&-ve:g0.transition!==null?(yi===0&&(yi=Ep()),yi):(e=W,e!==0||(e=window.event,e=e===void 0?16:Mp(e.type)),e):1}function lt(e,t,n,r){if(50<Yr)throw Yr=0,pl=null,Error(R(185));Ro(e,n,r),(!(U&2)||e!==me)&&(e===me&&(!(U&2)&&(ms|=n),pe===4&&zt(e,ve)),Ve(e,r),n===1&&U===0&&!(t.mode&1)&&(gr=le()+500,fs&&un()))}function Ve(e,t){var n=e.callbackNode;gy(e,t);var r=ki(e,e===me?ve:0);if(r===0)n!==null&&vc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&vc(n),t===1)e.tag===0?m0(cf.bind(null,e)):Yp(cf.bind(null,e)),f0(function(){!(U&6)&&un()}),n=null;else{switch(Tp(r)){case 1:n=Xl;break;case 4:n=Cp;break;case 16:n=Ti;break;case 536870912:n=Pp;break;default:n=Ti}n=Qh(n,Bh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Bh(e,t){if(vi=-1,yi=0,U&6)throw Error(R(327));var n=e.callbackNode;if(ur()&&e.callbackNode!==n)return null;var r=ki(e,e===me?ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Wi(e,r);else{t=r;var o=U;U|=2;var i=$h();(me!==e||ve!==t)&&(xt=null,gr=le()+500,kn(e,t));do try{O0();break}catch(a){Uh(e,a)}while(1);lu(),Bi.current=i,U=o,ce!==null?t=0:(me=null,ve=0,t=pe)}if(t!==0){if(t===2&&(o=za(e),o!==0&&(r=o,t=hl(e,o))),t===1)throw n=xo,kn(e,0),zt(e,r),Ve(e,le()),n;if(t===6)zt(e,r);else{if(o=e.current.alternate,!(r&30)&&!D0(o)&&(t=Wi(e,r),t===2&&(i=za(e),i!==0&&(r=i,t=hl(e,i))),t===1))throw n=xo,kn(e,0),zt(e,r),Ve(e,le()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:vn(e,Me,xt);break;case 3:if(zt(e,r),(r&130023424)===r&&(t=Pu+500-le(),10<t)){if(ki(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Le(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Qa(vn.bind(null,e,Me,xt),t);break}vn(e,Me,xt);break;case 4:if(zt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-at(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*_0(r/1960))-r,10<r){e.timeoutHandle=Qa(vn.bind(null,e,Me,xt),r);break}vn(e,Me,xt);break;case 5:vn(e,Me,xt);break;default:throw Error(R(329))}}}return Ve(e,le()),e.callbackNode===n?Bh.bind(null,e):null}function hl(e,t){var n=Xr;return e.current.memoizedState.isDehydrated&&(kn(e,t).flags|=256),e=Wi(e,t),e!==2&&(t=Me,Me=n,t!==null&&ml(t)),e}function ml(e){Me===null?Me=e:Me.push.apply(Me,e)}function D0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ut(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zt(e,t){for(t&=~Cu,t&=~ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function cf(e){if(U&6)throw Error(R(327));ur();var t=ki(e,0);if(!(t&1))return Ve(e,le()),null;var n=Wi(e,t);if(e.tag!==0&&n===2){var r=za(e);r!==0&&(t=r,n=hl(e,r))}if(n===1)throw n=xo,kn(e,0),zt(e,t),Ve(e,le()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,vn(e,Me,xt),Ve(e,le()),null}function Eu(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(gr=le()+500,fs&&un())}}function jn(e){Ut!==null&&Ut.tag===0&&!(U&6)&&ur();var t=U;U|=1;var n=Je.transition,r=W;try{if(Je.transition=null,W=1,e)return e()}finally{W=r,Je.transition=n,U=t,!(U&6)&&un()}}function Tu(){Fe=Jn.current,Y(Jn)}function kn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,c0(n)),ce!==null)for(n=ce.return;n!==null;){var r=n;switch(iu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Mi();break;case 3:hr(),Y(_e),Y(Pe),hu();break;case 5:pu(r);break;case 4:hr();break;case 13:Y(ee);break;case 19:Y(ee);break;case 10:uu(r.type._context);break;case 22:case 23:Tu()}n=n.return}if(me=e,ce=e=Zt(e.current,null),ve=Fe=t,pe=0,xo=null,Cu=ms=Mn=0,Me=Xr=null,Cn!==null){for(t=0;t<Cn.length;t++)if(n=Cn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Cn=null}return e}function Uh(e,t){do{var n=ce;try{if(lu(),hi.current=zi,Fi){for(var r=ne.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Fi=!1}if(An=0,he=fe=ne=null,Kr=!1,go=0,Su.current=null,n===null||n.return===null){pe=1,xo=t,ce=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=ve,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=Yc(s);if(m!==null){m.flags&=-257,Zc(m,s,a,i,t),m.mode&1&&Xc(i,u,t),t=m,l=u;var x=t.updateQueue;if(x===null){var y=new Set;y.add(l),t.updateQueue=y}else x.add(l);break e}else{if(!(t&1)){Xc(i,u,t),ku();break e}l=Error(R(426))}}else if(q&&a.mode&1){var S=Yc(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Zc(S,s,a,i,t),su(mr(l,a));break e}}i=l=mr(l,a),pe!==4&&(pe=2),Xr===null?Xr=[i]:Xr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=Th(i,l,t);$c(i,g);break e;case 1:a=l;var p=i.type,h=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Xt===null||!Xt.has(h)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=kh(i,a,t);$c(i,w);break e}}i=i.return}while(i!==null)}Hh(n)}catch(P){t=P,ce===n&&n!==null&&(ce=n=n.return);continue}break}while(1)}function $h(){var e=Bi.current;return Bi.current=zi,e===null?zi:e}function ku(){(pe===0||pe===3||pe===2)&&(pe=4),me===null||!(Mn&268435455)&&!(ms&268435455)||zt(me,ve)}function Wi(e,t){var n=U;U|=2;var r=$h();(me!==e||ve!==t)&&(xt=null,kn(e,t));do try{V0();break}catch(o){Uh(e,o)}while(1);if(lu(),U=n,Bi.current=r,ce!==null)throw Error(R(261));return me=null,ve=0,pe}function V0(){for(;ce!==null;)Wh(ce)}function O0(){for(;ce!==null&&!ay();)Wh(ce)}function Wh(e){var t=Kh(e.alternate,e,Fe);e.memoizedProps=e.pendingProps,t===null?Hh(e):ce=t,Su.current=null}function Hh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=N0(n,t),n!==null){n.flags&=32767,ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{pe=6,ce=null;return}}else if(n=L0(n,t,Fe),n!==null){ce=n;return}if(t=t.sibling,t!==null){ce=t;return}ce=t=e}while(t!==null);pe===0&&(pe=5)}function vn(e,t,n){var r=W,o=Je.transition;try{Je.transition=null,W=1,b0(e,t,n,r)}finally{Je.transition=o,W=r}return null}function b0(e,t,n,r){do ur();while(Ut!==null);if(U&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(vy(e,i),e===me&&(ce=me=null,ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Jo||(Jo=!0,Qh(Ti,function(){return ur(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Je.transition,Je.transition=null;var s=W;W=1;var a=U;U|=4,Su.current=null,M0(e,n),Fh(n,e),r0(Ga),Ri=!!Ha,Ga=Ha=null,e.current=n,j0(n),ly(),U=a,W=s,Je.transition=i}else e.current=n;if(Jo&&(Jo=!1,Ut=e,$i=o),i=e.pendingLanes,i===0&&(Xt=null),fy(n.stateNode),Ve(e,le()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ui)throw Ui=!1,e=dl,dl=null,e;return $i&1&&e.tag!==0&&ur(),i=e.pendingLanes,i&1?e===pl?Yr++:(Yr=0,pl=e):Yr=0,un(),null}function ur(){if(Ut!==null){var e=Tp($i),t=Je.transition,n=W;try{if(Je.transition=null,W=16>e?16:e,Ut===null)var r=!1;else{if(e=Ut,Ut=null,$i=0,U&6)throw Error(R(331));var o=U;for(U|=4,A=e.current;A!==null;){var i=A,s=i.child;if(A.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(A=u;A!==null;){var c=A;switch(c.tag){case 0:case 11:case 15:Qr(8,c,i)}var f=c.child;if(f!==null)f.return=c,A=f;else for(;A!==null;){c=A;var d=c.sibling,m=c.return;if(Oh(c),c===u){A=null;break}if(d!==null){d.return=m,A=d;break}A=m}}}var x=i.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var S=y.sibling;y.sibling=null,y=S}while(y!==null)}}A=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,A=s;else e:for(;A!==null;){if(i=A,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Qr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,A=g;break e}A=i.return}}var p=e.current;for(A=p;A!==null;){s=A;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,A=h;else e:for(s=p;A!==null;){if(a=A,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:hs(9,a)}}catch(P){oe(a,a.return,P)}if(a===s){A=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,A=w;break e}A=a.return}}if(U=o,un(),mt&&typeof mt.onPostCommitFiberRoot=="function")try{mt.onPostCommitFiberRoot(ss,e)}catch{}r=!0}return r}finally{W=n,Je.transition=t}}return!1}function ff(e,t,n){t=mr(n,t),t=Th(e,t,1),e=Qt(e,t,1),t=Le(),e!==null&&(Ro(e,1,t),Ve(e,t))}function oe(e,t,n){if(e.tag===3)ff(e,e,n);else for(;t!==null;){if(t.tag===3){ff(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Xt===null||!Xt.has(r))){e=mr(n,e),e=kh(t,e,1),t=Qt(t,e,1),e=Le(),t!==null&&(Ro(t,1,e),Ve(t,e));break}}t=t.return}}function I0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Le(),e.pingedLanes|=e.suspendedLanes&n,me===e&&(ve&n)===n&&(pe===4||pe===3&&(ve&130023424)===ve&&500>le()-Pu?kn(e,0):Cu|=n),Ve(e,t)}function Gh(e,t){t===0&&(e.mode&1?(t=$o,$o<<=1,!($o&130023424)&&($o=4194304)):t=1);var n=Le();e=Nt(e,t),e!==null&&(Ro(e,t,n),Ve(e,n))}function F0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Gh(e,n)}function z0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),Gh(e,n)}var Kh;Kh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||_e.current)je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return je=!1,R0(e,t,n);je=!!(e.flags&131072)}else je=!1,q&&t.flags&1048576&&Zp(t,Di,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;gi(e,t),e=t.pendingProps;var o=fr(t,Pe.current);lr(t,n),o=gu(null,t,r,e,o,n);var i=vu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,De(r)?(i=!0,ji(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,fu(t),o.updater=ps,t.stateNode=o,o._reactInternals=t,tl(t,r,e,n),t=ol(null,t,r,!0,i,n)):(t.tag=0,q&&i&&ou(t),Re(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(gi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=U0(r),e=ot(r,e),o){case 0:t=rl(null,t,r,e,n);break e;case 1:t=ef(null,t,r,e,n);break e;case 11:t=qc(null,t,r,e,n);break e;case 14:t=Jc(null,t,r,ot(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ot(r,o),rl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ot(r,o),ef(e,t,r,o,n);case 3:e:{if(Ah(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,rh(e,t),bi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=mr(Error(R(423)),t),t=tf(e,t,r,n,o);break e}else if(r!==o){o=mr(Error(R(424)),t),t=tf(e,t,r,n,o);break e}else for(Be=Kt(t.stateNode.containerInfo.firstChild),Ue=t,q=!0,st=null,n=th(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(dr(),r===o){t=At(e,t,n);break e}Re(e,t,r,n)}t=t.child}return t;case 5:return oh(t),e===null&&qa(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ka(r,o)?s=null:i!==null&&Ka(r,i)&&(t.flags|=32),Nh(e,t),Re(e,t,s,n),t.child;case 6:return e===null&&qa(t),null;case 13:return Mh(e,t,n);case 4:return du(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=pr(t,null,r,n):Re(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ot(r,o),qc(e,t,r,o,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,G(Vi,r._currentValue),r._currentValue=s,i!==null)if(ut(i.value,s)){if(i.children===o.children&&!_e.current){t=At(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Et(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Ja(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Ja(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Re(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,lr(t,n),o=et(o),r=r(o),t.flags|=1,Re(e,t,r,n),t.child;case 14:return r=t.type,o=ot(r,t.pendingProps),o=ot(r.type,o),Jc(e,t,r,o,n);case 15:return Rh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ot(r,o),gi(e,t),t.tag=1,De(r)?(e=!0,ji(t)):e=!1,lr(t,n),Eh(t,r,o),tl(t,r,o,n),ol(null,t,r,!0,e,n);case 19:return jh(e,t,n);case 22:return Lh(e,t,n)}throw Error(R(156,t.tag))};function Qh(e,t){return Sp(e,t)}function B0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function qe(e,t,n,r){return new B0(e,t,n,r)}function Ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function U0(e){if(typeof e=="function")return Ru(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Gl)return 11;if(e===Kl)return 14}return 2}function Zt(e,t){var n=e.alternate;return n===null?(n=qe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function xi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ru(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case $n:return Rn(n.children,o,i,t);case Hl:s=8,o|=8;break;case Ta:return e=qe(12,n,t,o|2),e.elementType=Ta,e.lanes=i,e;case ka:return e=qe(13,n,t,o),e.elementType=ka,e.lanes=i,e;case Ra:return e=qe(19,n,t,o),e.elementType=Ra,e.lanes=i,e;case op:return gs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case np:s=10;break e;case rp:s=9;break e;case Gl:s=11;break e;case Kl:s=14;break e;case Ot:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=qe(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Rn(e,t,n,r){return e=qe(7,e,r,t),e.lanes=n,e}function gs(e,t,n,r){return e=qe(22,e,r,t),e.elementType=op,e.lanes=n,e.stateNode={isHidden:!1},e}function ia(e,t,n){return e=qe(6,e,null,t),e.lanes=n,e}function sa(e,t,n){return t=qe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $0(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=zs(0),this.expirationTimes=zs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Lu(e,t,n,r,o,i,s,a,l){return e=new $0(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=qe(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},fu(i),e}function W0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Un,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Xh(e){if(!e)return nn;e=e._reactInternals;e:{if(Vn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(De(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(De(n))return Xp(e,n,t)}return t}function Yh(e,t,n,r,o,i,s,a,l){return e=Lu(n,r,!0,e,o,i,s,a,l),e.context=Xh(null),n=e.current,r=Le(),o=Yt(n),i=Et(r,o),i.callback=t??null,Qt(n,i,o),e.current.lanes=o,Ro(e,o,r),Ve(e,r),e}function vs(e,t,n,r){var o=t.current,i=Le(),s=Yt(o);return n=Xh(n),t.context===null?t.context=n:t.pendingContext=n,t=Et(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Qt(o,t,s),e!==null&&(lt(e,o,s,i),pi(e,o,s)),s}function Hi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function df(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Nu(e,t){df(e,t),(e=e.alternate)&&df(e,t)}function H0(){return null}var Zh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Au(e){this._internalRoot=e}ys.prototype.render=Au.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));vs(e,t,null,null)};ys.prototype.unmount=Au.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jn(function(){vs(null,e,null,null)}),t[Lt]=null}};function ys(e){this._internalRoot=e}ys.prototype.unstable_scheduleHydration=function(e){if(e){var t=Lp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ft.length&&t!==0&&t<Ft[n].priority;n++);Ft.splice(n,0,e),n===0&&Ap(e)}};function Mu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function pf(){}function G0(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Hi(s);i.call(u)}}var s=Yh(t,r,e,0,null,!1,!1,"",pf);return e._reactRootContainer=s,e[Lt]=s.current,co(e.nodeType===8?e.parentNode:e),jn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Hi(l);a.call(u)}}var l=Lu(e,0,!1,null,null,!1,!1,"",pf);return e._reactRootContainer=l,e[Lt]=l.current,co(e.nodeType===8?e.parentNode:e),jn(function(){vs(t,l,n,r)}),l}function ws(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Hi(s);a.call(l)}}vs(t,s,e,o)}else s=G0(n,t,e,o,r);return Hi(s)}kp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Fr(t.pendingLanes);n!==0&&(Yl(t,n|1),Ve(t,le()),!(U&6)&&(gr=le()+500,un()))}break;case 13:jn(function(){var r=Nt(e,1);if(r!==null){var o=Le();lt(r,e,1,o)}}),Nu(e,1)}};Zl=function(e){if(e.tag===13){var t=Nt(e,134217728);if(t!==null){var n=Le();lt(t,e,134217728,n)}Nu(e,134217728)}};Rp=function(e){if(e.tag===13){var t=Yt(e),n=Nt(e,t);if(n!==null){var r=Le();lt(n,e,t,r)}Nu(e,t)}};Lp=function(){return W};Np=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};ba=function(e,t,n){switch(t){case"input":if(Aa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=cs(r);if(!o)throw Error(R(90));sp(r),Aa(r,o)}}}break;case"textarea":lp(e,n);break;case"select":t=n.value,t!=null&&or(e,!!n.multiple,t,!1)}};mp=Eu;gp=jn;var K0={usingClientEntryPoint:!1,Events:[No,Kn,cs,pp,hp,Eu]},jr={findFiberByHostInstance:Sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Q0={bundleType:jr.bundleType,version:jr.version,rendererPackageName:jr.rendererPackageName,rendererConfig:jr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:jt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=xp(e),e===null?null:e.stateNode},findFiberByHostInstance:jr.findFiberByHostInstance||H0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ei=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ei.isDisabled&&ei.supportsFiber)try{ss=ei.inject(Q0),mt=ei}catch{}}He.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=K0;He.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Mu(t))throw Error(R(200));return W0(e,t,null,n)};He.createRoot=function(e,t){if(!Mu(e))throw Error(R(299));var n=!1,r="",o=Zh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Lu(e,1,!1,null,null,n,!1,r,o),e[Lt]=t.current,co(e.nodeType===8?e.parentNode:e),new Au(t)};He.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=xp(t),e=e===null?null:e.stateNode,e};He.flushSync=function(e){return jn(e)};He.hydrate=function(e,t,n){if(!xs(t))throw Error(R(200));return ws(null,e,t,!0,n)};He.hydrateRoot=function(e,t,n){if(!Mu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Zh;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Yh(t,null,e,1,n??null,o,!1,i,s),e[Lt]=t.current,co(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ys(t)};He.render=function(e,t,n){if(!xs(t))throw Error(R(200));return ws(null,e,t,!1,n)};He.unmountComponentAtNode=function(e){if(!xs(e))throw Error(R(40));return e._reactRootContainer?(jn(function(){ws(null,null,e,!1,function(){e._reactRootContainer=null,e[Lt]=null})}),!0):!1};He.unstable_batchedUpdates=Eu;He.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!xs(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return ws(e,t,n,!1,r)};He.version="18.3.1-next-f1338f8080-20240426";function qh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(qh)}catch(e){console.error(e)}}qh(),qd.exports=He;var Ss=qd.exports;const X0=Fd(Ss);var hf=Ss;Pa.createRoot=hf.createRoot,Pa.hydrateRoot=hf.hydrateRoot;const Y0="modulepreload",Z0=function(e){return"/"+e},mf={},ct=function(t,n,r){if(!n||n.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=Z0(i),i in mf)return;mf[i]=!0;const s=i.endsWith(".css"),a=s?'[rel="stylesheet"]':"";if(!!r)for(let c=o.length-1;c>=0;c--){const f=o[c];if(f.href===i&&(!s||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${a}`))return;const u=document.createElement("link");if(u.rel=s?"stylesheet":Y0,s||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),s)return new Promise((c,f)=>{u.addEventListener("load",c),u.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function wo(){return wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wo.apply(this,arguments)}var $t;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})($t||($t={}));const gf="popstate";function q0(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return gl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Gi(o)}return e1(t,n,null,e)}function se(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Jh(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function J0(){return Math.random().toString(36).substr(2,8)}function vf(e,t){return{usr:e.state,key:e.key,idx:t}}function gl(e,t,n,r){return n===void 0&&(n=null),wo({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Pr(t):t,{state:n,key:t&&t.key||r||J0()})}function Gi(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Pr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function e1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=$t.Pop,l=null,u=c();u==null&&(u=0,s.replaceState(wo({},s.state,{idx:u}),""));function c(){return(s.state||{idx:null}).idx}function f(){a=$t.Pop;let S=c(),g=S==null?null:S-u;u=S,l&&l({action:a,location:y.location,delta:g})}function d(S,g){a=$t.Push;let p=gl(y.location,S,g);n&&n(p,S),u=c()+1;let h=vf(p,u),w=y.createHref(p);try{s.pushState(h,"",w)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;o.location.assign(w)}i&&l&&l({action:a,location:y.location,delta:1})}function m(S,g){a=$t.Replace;let p=gl(y.location,S,g);n&&n(p,S),u=c();let h=vf(p,u),w=y.createHref(p);s.replaceState(h,"",w),i&&l&&l({action:a,location:y.location,delta:0})}function x(S){let g=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof S=="string"?S:Gi(S);return p=p.replace(/ $/,"%20"),se(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let y={get action(){return a},get location(){return e(o,s)},listen(S){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(gf,f),l=S,()=>{o.removeEventListener(gf,f),l=null}},createHref(S){return t(o,S)},createURL:x,encodeLocation(S){let g=x(S);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:d,replace:m,go(S){return s.go(S)}};return y}var yf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(yf||(yf={}));function t1(e,t,n){return n===void 0&&(n="/"),n1(e,t,n,!1)}function n1(e,t,n,r){let o=typeof t=="string"?Pr(t):t,i=vr(o.pathname||"/",n);if(i==null)return null;let s=em(e);r1(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let u=h1(i);a=d1(s[l],u,r)}return a}function em(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(se(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=qt([r,l.relativePath]),c=n.concat(l);i.children&&i.children.length>0&&(se(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),em(i.children,t,c,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:c1(u,i.index),routesMeta:c})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of tm(i.path))o(i,s,l)}),t}function tm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=tm(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function r1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:f1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const o1=/^:[\w-]+$/,i1=3,s1=2,a1=1,l1=10,u1=-2,xf=e=>e==="*";function c1(e,t){let n=e.split("/"),r=n.length;return n.some(xf)&&(r+=u1),t&&(r+=s1),n.filter(o=>!xf(o)).reduce((o,i)=>o+(o1.test(i)?i1:i===""?a1:l1),r)}function f1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function d1(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,o={},i="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=i==="/"?t:t.slice(i.length)||"/",f=Ki({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),d=l.route;if(!f&&u&&n&&!r[r.length-1].route.index&&(f=Ki({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!f)return null;Object.assign(o,f.params),s.push({params:o,pathname:qt([i,f.pathname]),pathnameBase:y1(qt([i,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(i=qt([i,f.pathnameBase]))}return s}function Ki(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=p1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:d,isOptional:m}=c;if(d==="*"){let y=a[f]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const x=a[f];return m&&!x?u[d]=void 0:u[d]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function p1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Jh(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function h1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Jh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function vr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function m1(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Pr(e):e;return{pathname:n?n.startsWith("/")?n:g1(n,t):t,search:x1(r),hash:w1(o)}}function g1(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function aa(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function v1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function nm(e,t){let n=v1(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function rm(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Pr(e):(o=wo({},e),se(!o.pathname||!o.pathname.includes("?"),aa("?","pathname","search",o)),se(!o.pathname||!o.pathname.includes("#"),aa("#","pathname","hash",o)),se(!o.search||!o.search.includes("#"),aa("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,a;if(s==null)a=n;else{let f=t.length-1;if(!r&&s.startsWith("..")){let d=s.split("/");for(;d[0]==="..";)d.shift(),f-=1;o.pathname=d.join("/")}a=f>=0?t[f]:"/"}let l=m1(o,a),u=s&&s!=="/"&&s.endsWith("/"),c=(i||s===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const qt=e=>e.join("/").replace(/\/\/+/g,"/"),y1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),x1=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,w1=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function S1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const om=["post","put","patch","delete"];new Set(om);const C1=["get",...om];new Set(C1);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function So(){return So=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},So.apply(this,arguments)}const Cs=v.createContext(null),im=v.createContext(null),cn=v.createContext(null),Ps=v.createContext(null),On=v.createContext({outlet:null,matches:[],isDataRoute:!1}),sm=v.createContext(null);function P1(e,t){let{relative:n}=t===void 0?{}:t;Mo()||se(!1);let{basename:r,navigator:o}=v.useContext(cn),{hash:i,pathname:s,search:a}=Es(e,{relative:n}),l=s;return r!=="/"&&(l=s==="/"?r:qt([r,s])),o.createHref({pathname:l,search:a,hash:i})}function Mo(){return v.useContext(Ps)!=null}function Er(){return Mo()||se(!1),v.useContext(Ps).location}function am(e){v.useContext(cn).static||v.useLayoutEffect(e)}function E1(){let{isDataRoute:e}=v.useContext(On);return e?b1():T1()}function T1(){Mo()||se(!1);let e=v.useContext(Cs),{basename:t,future:n,navigator:r}=v.useContext(cn),{matches:o}=v.useContext(On),{pathname:i}=Er(),s=JSON.stringify(nm(o,n.v7_relativeSplatPath)),a=v.useRef(!1);return am(()=>{a.current=!0}),v.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let f=rm(u,JSON.parse(s),i,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:qt([t,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[t,r,s,i,e])}function Es(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=v.useContext(cn),{matches:o}=v.useContext(On),{pathname:i}=Er(),s=JSON.stringify(nm(o,r.v7_relativeSplatPath));return v.useMemo(()=>rm(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function k1(e,t){return R1(e,t)}function R1(e,t,n,r){Mo()||se(!1);let{navigator:o}=v.useContext(cn),{matches:i}=v.useContext(On),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=Er(),c;if(t){var f;let S=typeof t=="string"?Pr(t):t;l==="/"||(f=S.pathname)!=null&&f.startsWith(l)||se(!1),c=S}else c=u;let d=c.pathname||"/",m=d;if(l!=="/"){let S=l.replace(/^\//,"").split("/");m="/"+d.replace(/^\//,"").split("/").slice(S.length).join("/")}let x=t1(e,{pathname:m}),y=j1(x&&x.map(S=>Object.assign({},S,{params:Object.assign({},a,S.params),pathname:qt([l,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?l:qt([l,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),i,n,r);return t&&y?v.createElement(Ps.Provider,{value:{location:So({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:$t.Pop}},y):y}function L1(){let e=O1(),t=S1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},t),n?v.createElement("pre",{style:o},n):null,i)}const N1=v.createElement(L1,null);class A1 extends v.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?v.createElement(On.Provider,{value:this.props.routeContext},v.createElement(sm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function M1(e){let{routeContext:t,match:n,children:r}=e,o=v.useContext(Cs);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),v.createElement(On.Provider,{value:t},r)}function j1(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let c=s.findIndex(f=>f.route.id&&(a==null?void 0:a[f.route.id])!==void 0);c>=0||se(!1),s=s.slice(0,Math.min(s.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<s.length;c++){let f=s[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:d,errors:m}=n,x=f.route.loader&&d[f.route.id]===void 0&&(!m||m[f.route.id]===void 0);if(f.route.lazy||x){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((c,f,d)=>{let m,x=!1,y=null,S=null;n&&(m=a&&f.route.id?a[f.route.id]:void 0,y=f.route.errorElement||N1,l&&(u<0&&d===0?(I1("route-fallback",!1),x=!0,S=null):u===d&&(x=!0,S=f.route.hydrateFallbackElement||null)));let g=t.concat(s.slice(0,d+1)),p=()=>{let h;return m?h=y:x?h=S:f.route.Component?h=v.createElement(f.route.Component,null):f.route.element?h=f.route.element:h=c,v.createElement(M1,{match:f,routeContext:{outlet:c,matches:g,isDataRoute:n!=null},children:h})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?v.createElement(A1,{location:n.location,revalidation:n.revalidation,component:y,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var lm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(lm||{}),Qi=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Qi||{});function _1(e){let t=v.useContext(Cs);return t||se(!1),t}function D1(e){let t=v.useContext(im);return t||se(!1),t}function V1(e){let t=v.useContext(On);return t||se(!1),t}function um(e){let t=V1(),n=t.matches[t.matches.length-1];return n.route.id||se(!1),n.route.id}function O1(){var e;let t=v.useContext(sm),n=D1(Qi.UseRouteError),r=um(Qi.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function b1(){let{router:e}=_1(lm.UseNavigateStable),t=um(Qi.UseNavigateStable),n=v.useRef(!1);return am(()=>{n.current=!0}),v.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,So({fromRouteId:t},i)))},[e,t])}const wf={};function I1(e,t,n){!t&&!wf[e]&&(wf[e]=!0)}function F1(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Qe(e){se(!1)}function z1(e){let{basename:t="/",children:n=null,location:r,navigationType:o=$t.Pop,navigator:i,static:s=!1,future:a}=e;Mo()&&se(!1);let l=t.replace(/^\/*/,"/"),u=v.useMemo(()=>({basename:l,navigator:i,static:s,future:So({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=Pr(r));let{pathname:c="/",search:f="",hash:d="",state:m=null,key:x="default"}=r,y=v.useMemo(()=>{let S=vr(c,l);return S==null?null:{location:{pathname:S,search:f,hash:d,state:m,key:x},navigationType:o}},[l,c,f,d,m,x,o]);return y==null?null:v.createElement(cn.Provider,{value:u},v.createElement(Ps.Provider,{children:n,value:y}))}function B1(e){let{children:t,location:n}=e;return k1(vl(t),n)}new Promise(()=>{});function vl(e,t){t===void 0&&(t=[]);let n=[];return v.Children.forEach(e,(r,o)=>{if(!v.isValidElement(r))return;let i=[...t,o];if(r.type===v.Fragment){n.push.apply(n,vl(r.props.children,i));return}r.type!==Qe&&se(!1),!r.props.index||!r.props.children||se(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=vl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xi.apply(this,arguments)}function cm(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function U1(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function $1(e,t){return e.button===0&&(!t||t==="_self")&&!U1(e)}const W1=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],H1=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],G1="6";try{window.__reactRouterVersion=G1}catch{}const K1=v.createContext({isTransitioning:!1}),Q1="startTransition",Sf=Yd[Q1];function X1(e){let{basename:t,children:n,future:r,window:o}=e,i=v.useRef();i.current==null&&(i.current=q0({window:o,v5Compat:!0}));let s=i.current,[a,l]=v.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},c=v.useCallback(f=>{u&&Sf?Sf(()=>l(f)):l(f)},[l,u]);return v.useLayoutEffect(()=>s.listen(c),[s,c]),v.useEffect(()=>F1(r),[r]),v.createElement(z1,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}const Y1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Z1=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,pt=v.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:a,target:l,to:u,preventScrollReset:c,viewTransition:f}=t,d=cm(t,W1),{basename:m}=v.useContext(cn),x,y=!1;if(typeof u=="string"&&Z1.test(u)&&(x=u,Y1))try{let h=new URL(window.location.href),w=u.startsWith("//")?new URL(h.protocol+u):new URL(u),P=vr(w.pathname,m);w.origin===h.origin&&P!=null?u=P+w.search+w.hash:y=!0}catch{}let S=P1(u,{relative:o}),g=J1(u,{replace:s,state:a,target:l,preventScrollReset:c,relative:o,viewTransition:f});function p(h){r&&r(h),h.defaultPrevented||g(h)}return v.createElement("a",Xi({},d,{href:x||S,onClick:y||i?r:p,ref:n,target:l}))}),Cf=v.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:o=!1,className:i="",end:s=!1,style:a,to:l,viewTransition:u,children:c}=t,f=cm(t,H1),d=Es(l,{relative:f.relative}),m=Er(),x=v.useContext(im),{navigator:y,basename:S}=v.useContext(cn),g=x!=null&&ex(d)&&u===!0,p=y.encodeLocation?y.encodeLocation(d).pathname:d.pathname,h=m.pathname,w=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;o||(h=h.toLowerCase(),w=w?w.toLowerCase():null,p=p.toLowerCase()),w&&S&&(w=vr(w,S)||w);const P=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let T=h===p||!s&&h.startsWith(p)&&h.charAt(P)==="/",E=w!=null&&(w===p||!s&&w.startsWith(p)&&w.charAt(p.length)==="/"),k={isActive:T,isPending:E,isTransitioning:g},N=T?r:void 0,M;typeof i=="function"?M=i(k):M=[i,T?"active":null,E?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let $=typeof a=="function"?a(k):a;return v.createElement(pt,Xi({},f,{"aria-current":N,className:M,ref:n,style:$,to:l,viewTransition:u}),typeof c=="function"?c(k):c)});var yl;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(yl||(yl={}));var Pf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Pf||(Pf={}));function q1(e){let t=v.useContext(Cs);return t||se(!1),t}function J1(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:a}=t===void 0?{}:t,l=E1(),u=Er(),c=Es(e,{relative:s});return v.useCallback(f=>{if($1(f,n)){f.preventDefault();let d=r!==void 0?r:Gi(u)===Gi(c);l(e,{replace:d,state:o,preventScrollReset:i,relative:s,viewTransition:a})}},[u,l,c,r,o,n,e,i,s,a])}function ex(e,t){t===void 0&&(t={});let n=v.useContext(K1);n==null&&se(!1);let{basename:r}=q1(yl.useViewTransitionState),o=Es(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=vr(n.currentLocation.pathname,r)||n.currentLocation.pathname,s=vr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Ki(o.pathname,s)!=null||Ki(o.pathname,i)!=null}function fm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=fm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function dm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=fm(e))&&(r&&(r+=" "),r+=t);return r}function tx(){for(var e=0,t,n,r="";e<arguments.length;)(t=arguments[e++])&&(n=pm(t))&&(r&&(r+=" "),r+=n);return r}function pm(e){if(typeof e=="string")return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=pm(e[r]))&&(n&&(n+=" "),n+=t);return n}var ju="-";function nx(e){var t=ox(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,o=r===void 0?{}:r;function i(a){var l=a.split(ju);return l[0]===""&&l.length!==1&&l.shift(),hm(l,t)||rx(a)}function s(a,l){var u=n[a]||[];return l&&o[a]?[].concat(u,o[a]):u}return{getClassGroupId:i,getConflictingClassGroupIds:s}}function hm(e,t){var s;if(e.length===0)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),o=r?hm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length!==0){var i=e.join(ju);return(s=t.validators.find(function(a){var l=a.validator;return l(i)}))==null?void 0:s.classGroupId}}var Ef=/^\[(.+)\]$/;function rx(e){if(Ef.test(e)){var t=Ef.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function ox(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},o=sx(Object.entries(e.classGroups),n);return o.forEach(function(i){var s=i[0],a=i[1];xl(a,r,s,t)}),r}function xl(e,t,n,r){e.forEach(function(o){if(typeof o=="string"){var i=o===""?t:Tf(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(ix(o)){xl(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(function(s){var a=s[0],l=s[1];xl(l,Tf(t,a),n,r)})})}function Tf(e,t){var n=e;return t.split(ju).forEach(function(r){n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function ix(e){return e.isThemeGetter}function sx(e,t){return t?e.map(function(n){var r=n[0],o=n[1],i=o.map(function(s){return typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(function(a){var l=a[0],u=a[1];return[t+l,u]})):s});return[r,i]}):e}function ax(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get:function(s){var a=n.get(s);if(a!==void 0)return a;if((a=r.get(s))!==void 0)return o(s,a),a},set:function(s,a){n.has(s)?n.set(s,a):o(s,a)}}}var mm="!";function lx(e){var t=e.separator||":",n=t.length===1,r=t[0],o=t.length;return function(s){for(var a=[],l=0,u=0,c,f=0;f<s.length;f++){var d=s[f];if(l===0){if(d===r&&(n||s.slice(f,f+o)===t)){a.push(s.slice(u,f)),u=f+o;continue}if(d==="/"){c=f;continue}}d==="["?l++:d==="]"&&l--}var m=a.length===0?s:s.substring(u),x=m.startsWith(mm),y=x?m.substring(1):m,S=c&&c>u?c-u:void 0;return{modifiers:a,hasImportantModifier:x,baseClassName:y,maybePostfixModifierPosition:S}}}function ux(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(r){var o=r[0]==="[";o?(t.push.apply(t,n.sort().concat([r])),n=[]):n.push(r)}),t.push.apply(t,n.sort()),t}function cx(e){return{cache:ax(e.cacheSize),splitModifiers:lx(e),...nx(e)}}var fx=/\s+/;function dx(e,t){var n=t.splitModifiers,r=t.getClassGroupId,o=t.getConflictingClassGroupIds,i=new Set;return e.trim().split(fx).map(function(s){var a=n(s),l=a.modifiers,u=a.hasImportantModifier,c=a.baseClassName,f=a.maybePostfixModifierPosition,d=r(f?c.substring(0,f):c),m=!!f;if(!d){if(!f)return{isTailwindClass:!1,originalClassName:s};if(d=r(c),!d)return{isTailwindClass:!1,originalClassName:s};m=!1}var x=ux(l).join(":"),y=u?x+mm:x;return{isTailwindClass:!0,modifierId:y,classGroupId:d,originalClassName:s,hasPostfixModifier:m}}).reverse().filter(function(s){if(!s.isTailwindClass)return!0;var a=s.modifierId,l=s.classGroupId,u=s.hasPostfixModifier,c=a+l;return i.has(c)?!1:(i.add(c),o(l,u).forEach(function(f){return i.add(a+f)}),!0)}).reverse().map(function(s){return s.originalClassName}).join(" ")}function px(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,o,i,s=a;function a(u){var c=t[0],f=t.slice(1),d=f.reduce(function(m,x){return x(m)},c());return r=cx(d),o=r.cache.get,i=r.cache.set,s=l,l(u)}function l(u){var c=o(u);if(c)return c;var f=dx(u,r);return i(u,f),f}return function(){return s(tx.apply(null,arguments))}}function Q(e){var t=function(r){return r[e]||[]};return t.isThemeGetter=!0,t}var gm=/^\[(?:([a-z-]+):)?(.+)\]$/i,hx=/^\d+\/\d+$/,mx=new Set(["px","full","screen"]),gx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,vx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,yx=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function rt(e){return En(e)||mx.has(e)||hx.test(e)||wl(e)}function wl(e){return bn(e,"length",Ex)}function xx(e){return bn(e,"size",vm)}function wx(e){return bn(e,"position",vm)}function Sx(e){return bn(e,"url",Tx)}function ti(e){return bn(e,"number",En)}function En(e){return!Number.isNaN(Number(e))}function Cx(e){return e.endsWith("%")&&En(e.slice(0,-1))}function _r(e){return kf(e)||bn(e,"number",kf)}function F(e){return gm.test(e)}function Dr(){return!0}function Dt(e){return gx.test(e)}function Px(e){return bn(e,"",kx)}function bn(e,t,n){var r=gm.exec(e);return r?r[1]?r[1]===t:n(r[2]):!1}function Ex(e){return vx.test(e)}function vm(){return!1}function Tx(e){return e.startsWith("url(")}function kf(e){return Number.isInteger(Number(e))}function kx(e){return yx.test(e)}function Rx(){var e=Q("colors"),t=Q("spacing"),n=Q("blur"),r=Q("brightness"),o=Q("borderColor"),i=Q("borderRadius"),s=Q("borderSpacing"),a=Q("borderWidth"),l=Q("contrast"),u=Q("grayscale"),c=Q("hueRotate"),f=Q("invert"),d=Q("gap"),m=Q("gradientColorStops"),x=Q("gradientColorStopPositions"),y=Q("inset"),S=Q("margin"),g=Q("opacity"),p=Q("padding"),h=Q("saturate"),w=Q("scale"),P=Q("sepia"),T=Q("skew"),E=Q("space"),k=Q("translate"),N=function(){return["auto","contain","none"]},M=function(){return["auto","hidden","clip","visible","scroll"]},$=function(){return["auto",F,t]},V=function(){return[F,t]},J=function(){return["",rt]},j=function(){return["auto",En,F]},Z=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},z=function(){return["solid","dashed","dotted","double","none"]},ae=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},L=function(){return["start","end","center","between","around","evenly","stretch"]},_=function(){return["","0",F]},b=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},D=function(){return[En,ti]},B=function(){return[En,F]};return{cacheSize:500,theme:{colors:[Dr],spacing:[rt],blur:["none","",Dt,F],brightness:D(),borderColor:[e],borderRadius:["none","","full",Dt,F],borderSpacing:V(),borderWidth:J(),contrast:D(),grayscale:_(),hueRotate:B(),invert:_(),gap:V(),gradientColorStops:[e],gradientColorStopPositions:[Cx,wl],inset:$(),margin:$(),opacity:D(),padding:V(),saturate:D(),scale:D(),sepia:_(),skew:B(),space:V(),translate:V()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[Dt]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(Z(),[F])}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",_r]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",_r]}],"grid-cols":[{"grid-cols":[Dr]}],"col-start-end":[{col:["auto",{span:["full",_r]},F]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Dr]}],"row-start-end":[{row:["auto",{span:[_r]},F]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal"].concat(L())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(L(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(L(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",F,t]}],"min-w":[{"min-w":["min","max","fit",F,rt]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[Dt]},Dt,F]}],h:[{h:[F,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",F,rt]}],"max-h":[{"max-h":[F,t,"min","max","fit"]}],"font-size":[{text:["base",Dt,wl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ti]}],"font-family":[{font:[Dr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",En,ti]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",F,rt]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(z(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",rt]}],"underline-offset":[{"underline-offset":["auto",F,rt]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(Z(),[wx])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",xx]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Sx]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[].concat(z(),["hidden"])}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(z())}],"outline-offset":[{"outline-offset":[F,rt]}],"outline-w":[{outline:[rt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:J()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[rt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Dt,Px]}],"shadow-color":[{shadow:[Dr]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":ae()}],"bg-blend":[{"bg-blend":ae()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Dt,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[f]}],saturate:[{saturate:[h]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[_r,F]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[rt,ti]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var Lx=px(Rx);function fn(...e){return Lx(dm(e))}function Rf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Nx(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Rf(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Rf(e[o],null)}}}}function rn(...e){return v.useCallback(Nx(...e),e)}function Yi(e){const t=Mx(e),n=v.forwardRef((r,o)=>{const{children:i,...s}=r,a=v.Children.toArray(i),l=a.find(_x);if(l){const u=l.props.children,c=a.map(f=>f===l?v.Children.count(u)>1?v.Children.only(null):v.isValidElement(u)?u.props.children:null:f);return C.jsx(t,{...s,ref:o,children:v.isValidElement(u)?v.cloneElement(u,void 0,c):null})}return C.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var Ax=Yi("Slot");function Mx(e){const t=v.forwardRef((n,r)=>{const{children:o,...i}=n,s=v.isValidElement(o)?Vx(o):void 0,a=rn(s,r);if(v.isValidElement(o)){const l=Dx(i,o.props);return o.type!==v.Fragment&&(l.ref=a),v.cloneElement(o,l)}return v.Children.count(o)>1?v.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var jx=Symbol("radix.slottable");function _x(e){return v.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===jx}function Dx(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{const l=i(...a);return o(...a),l}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Vx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const Lf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Nf=dm,ym=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Nf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],f=i==null?void 0:i[u];if(c===null)return null;const d=Lf(c)||Lf(f);return o[u][d]}),a=n&&Object.entries(n).reduce((u,c)=>{let[f,d]=c;return d===void 0||(u[f]=d),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:f,className:d,...m}=c;return Object.entries(m).every(x=>{let[y,S]=x;return Array.isArray(S)?S.includes({...i,...a}[y]):{...i,...a}[y]===S})?[...u,f,d]:u},[]);return Nf(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)},Ox=ym("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Zr=de.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?Ax:"button";return C.jsx(s,{className:fn(Ox({variant:t,size:n,className:e})),ref:i,...o})});Zr.displayName="Button";var bx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Ix=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ee=(e,t)=>{const n=v.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,children:a,...l},u)=>v.createElement("svg",{ref:u,...bx,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:`lucide lucide-${Ix(e)}`,...l},[...t.map(([c,f])=>v.createElement(c,f)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n},Fx=Ee("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]),zx=Ee("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),Bx=Ee("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Ux=Ee("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),$x=Ee("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),Wx=Ee("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),Hx=Ee("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),Gx=Ee("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),Kx=Ee("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),Qx=Ee("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),xm=Ee("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Xx=Ee("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),Yx=Ee("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),Af=Ee("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),wm=Ee("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Zx=Ee("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Sm=v.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ts=v.createContext({}),ks=v.createContext(null),Rs=typeof document<"u",_u=Rs?v.useLayoutEffect:v.useEffect,Cm=v.createContext({strict:!1}),Du=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),qx="framerAppearId",Pm="data-"+Du(qx);function Jx(e,t,n,r){const{visualElement:o}=v.useContext(Ts),i=v.useContext(Cm),s=v.useContext(ks),a=v.useContext(Sm).reducedMotion,l=v.useRef();r=r||i.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:o,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;v.useInsertionEffect(()=>{u&&u.update(n,s)});const c=v.useRef(!!(n[Pm]&&!window.HandoffComplete));return _u(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),v.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function er(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function ew(e,t,n){return v.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):er(n)&&(n.current=r))},[t])}function Co(e){return typeof e=="string"||Array.isArray(e)}function Ls(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Vu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ou=["initial",...Vu];function Ns(e){return Ls(e.animate)||Ou.some(t=>Co(e[t]))}function Em(e){return!!(Ns(e)||e.variants)}function tw(e,t){if(Ns(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Co(n)?n:void 0,animate:Co(r)?r:void 0}}return e.inherit!==!1?t:{}}function nw(e){const{initial:t,animate:n}=tw(e,v.useContext(Ts));return v.useMemo(()=>({initial:t,animate:n}),[Mf(t),Mf(n)])}function Mf(e){return Array.isArray(e)?e.join(" "):e}const jf={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Po={};for(const e in jf)Po[e]={isEnabled:t=>jf[e].some(n=>!!t[n])};function rw(e){for(const t in e)Po[t]={...Po[t],...e[t]}}const bu=v.createContext({}),Tm=v.createContext({}),ow=Symbol.for("motionComponentSymbol");function iw({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&rw(e);function i(a,l){let u;const c={...v.useContext(Sm),...a,layoutId:sw(a)},{isStatic:f}=c,d=nw(a),m=r(a,f);if(!f&&Rs){d.visualElement=Jx(o,m,c,t);const x=v.useContext(Tm),y=v.useContext(Cm).strict;d.visualElement&&(u=d.visualElement.loadFeatures(c,y,e,x))}return v.createElement(Ts.Provider,{value:d},u&&d.visualElement?v.createElement(u,{visualElement:d.visualElement,...c}):null,n(o,a,ew(m,d.visualElement,l),m,f,d.visualElement))}const s=v.forwardRef(i);return s[ow]=o,s}function sw({layoutId:e}){const t=v.useContext(bu).id;return t&&e!==void 0?t+"-"+e:e}function aw(e){function t(r,o={}){return iw(e(r,o))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,o)=>(n.has(o)||n.set(o,t(o)),n.get(o))})}const lw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Iu(e){return typeof e!="string"||e.includes("-")?!1:!!(lw.indexOf(e)>-1||/[A-Z]/.test(e))}const Zi={};function uw(e){Object.assign(Zi,e)}const jo=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],In=new Set(jo);function km(e,{layout:t,layoutId:n}){return In.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Zi[e]||e==="opacity")}const Oe=e=>!!(e&&e.getVelocity),cw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},fw=jo.length;function dw(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,o){let i="";for(let s=0;s<fw;s++){const a=jo[s];if(e[a]!==void 0){const l=cw[a]||a;i+=`${l}(${e[a]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),o?i=o(e,r?"":i):n&&r&&(i="none"),i}const Rm=e=>t=>typeof t=="string"&&t.startsWith(e),Lm=Rm("--"),Sl=Rm("var(--"),pw=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,hw=(e,t)=>t&&typeof e=="number"?t.transform(e):e,on=(e,t,n)=>Math.min(Math.max(n,e),t),Fn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},qr={...Fn,transform:e=>on(0,1,e)},ni={...Fn,default:1},Jr=e=>Math.round(e*1e5)/1e5,As=/(-)?([\d]*\.?[\d])+/g,Nm=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,mw=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function _o(e){return typeof e=="string"}const Do=e=>({test:t=>_o(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Vt=Do("deg"),vt=Do("%"),O=Do("px"),gw=Do("vh"),vw=Do("vw"),_f={...vt,parse:e=>vt.parse(e)/100,transform:e=>vt.transform(e*100)},Df={...Fn,transform:Math.round},Am={borderWidth:O,borderTopWidth:O,borderRightWidth:O,borderBottomWidth:O,borderLeftWidth:O,borderRadius:O,radius:O,borderTopLeftRadius:O,borderTopRightRadius:O,borderBottomRightRadius:O,borderBottomLeftRadius:O,width:O,maxWidth:O,height:O,maxHeight:O,size:O,top:O,right:O,bottom:O,left:O,padding:O,paddingTop:O,paddingRight:O,paddingBottom:O,paddingLeft:O,margin:O,marginTop:O,marginRight:O,marginBottom:O,marginLeft:O,rotate:Vt,rotateX:Vt,rotateY:Vt,rotateZ:Vt,scale:ni,scaleX:ni,scaleY:ni,scaleZ:ni,skew:Vt,skewX:Vt,skewY:Vt,distance:O,translateX:O,translateY:O,translateZ:O,x:O,y:O,z:O,perspective:O,transformPerspective:O,opacity:qr,originX:_f,originY:_f,originZ:O,zIndex:Df,fillOpacity:qr,strokeOpacity:qr,numOctaves:Df};function Fu(e,t,n,r){const{style:o,vars:i,transform:s,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const f in t){const d=t[f];if(Lm(f)){i[f]=d;continue}const m=Am[f],x=hw(d,m);if(In.has(f)){if(l=!0,s[f]=x,!c)continue;d!==(m.default||0)&&(c=!1)}else f.startsWith("origin")?(u=!0,a[f]=x):o[f]=x}if(t.transform||(l||r?o.transform=dw(e.transform,n,c,r):o.transform&&(o.transform="none")),u){const{originX:f="50%",originY:d="50%",originZ:m=0}=a;o.transformOrigin=`${f} ${d} ${m}`}}const zu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Mm(e,t,n){for(const r in t)!Oe(t[r])&&!km(r,n)&&(e[r]=t[r])}function yw({transformTemplate:e},t,n){return v.useMemo(()=>{const r=zu();return Fu(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function xw(e,t,n){const r=e.style||{},o={};return Mm(o,r,e),Object.assign(o,yw(e,t,n)),e.transformValues?e.transformValues(o):o}function ww(e,t,n){const r={},o=xw(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=o,r}const Sw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function qi(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Sw.has(e)}let jm=e=>!qi(e);function Cw(e){e&&(jm=t=>t.startsWith("on")?!qi(t):e(t))}try{Cw(require("@emotion/is-prop-valid").default)}catch{}function Pw(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(jm(o)||n===!0&&qi(o)||!t&&!qi(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function Vf(e,t,n){return typeof e=="string"?e:O.transform(t+n*e)}function Ew(e,t,n){const r=Vf(t,e.x,e.width),o=Vf(n,e.y,e.height);return`${r} ${o}`}const Tw={offset:"stroke-dashoffset",array:"stroke-dasharray"},kw={offset:"strokeDashoffset",array:"strokeDasharray"};function Rw(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?Tw:kw;e[i.offset]=O.transform(-r);const s=O.transform(t),a=O.transform(n);e[i.array]=`${s} ${a}`}function Bu(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,f,d){if(Fu(e,u,c,d),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:x,dimensions:y}=e;m.transform&&(y&&(x.transform=m.transform),delete m.transform),y&&(o!==void 0||i!==void 0||x.transform)&&(x.transformOrigin=Ew(y,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&Rw(m,s,a,l,!1)}const _m=()=>({...zu(),attrs:{}}),Uu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Lw(e,t,n,r){const o=v.useMemo(()=>{const i=_m();return Bu(i,t,{enableHardwareAcceleration:!1},Uu(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Mm(i,e.style,e),o.style={...i,...o.style}}return o}function Nw(e=!1){return(n,r,o,{latestValues:i},s)=>{const l=(Iu(n)?Lw:ww)(r,i,s,n),c={...Pw(r,typeof n=="string",e),...l,ref:o},{children:f}=r,d=v.useMemo(()=>Oe(f)?f.get():f,[f]);return v.createElement(n,{...c,children:d})}}function Dm(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Vm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Om(e,t,n,r){Dm(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Vm.has(o)?o:Du(o),t.attrs[o])}function $u(e,t){const{style:n}=e,r={};for(const o in n)(Oe(n[o])||t.style&&Oe(t.style[o])||km(o,e))&&(r[o]=n[o]);return r}function bm(e,t){const n=$u(e,t);for(const r in e)if(Oe(e[r])||Oe(t[r])){const o=jo.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=e[r]}return n}function Wu(e,t,n,r={},o={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),t}function Im(e){const t=v.useRef(null);return t.current===null&&(t.current=e()),t.current}const Ji=e=>Array.isArray(e),Aw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Mw=e=>Ji(e)?e[e.length-1]||0:e;function wi(e){const t=Oe(e)?e.get():e;return Aw(t)?t.toValue():t}function jw({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,i){const s={latestValues:_w(r,o,i,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const Fm=e=>(t,n)=>{const r=v.useContext(Ts),o=v.useContext(ks),i=()=>jw(e,t,r,o);return n?i():Im(i)};function _w(e,t,n,r){const o={},i=r(e,{});for(const d in i)o[d]=wi(i[d]);let{initial:s,animate:a}=e;const l=Ns(e),u=Em(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?a:s;return f&&typeof f!="boolean"&&!Ls(f)&&(Array.isArray(f)?f:[f]).forEach(m=>{const x=Wu(e,m);if(!x)return;const{transitionEnd:y,transition:S,...g}=x;for(const p in g){let h=g[p];if(Array.isArray(h)){const w=c?h.length-1:0;h=h[w]}h!==null&&(o[p]=h)}for(const p in y)o[p]=y[p]}),o}const ie=e=>e;class Of{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Dw(e){let t=new Of,n=new Of,r=0,o=!1,i=!1;const s=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const f=c&&o,d=f?t:n;return u&&s.add(l),d.add(l)&&f&&o&&(r=t.order.length),l},cancel:l=>{n.remove(l),s.delete(l)},process:l=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),s.has(c)&&(a.schedule(c),e())}o=!1,i&&(i=!1,a.process(l))}};return a}const ri=["prepare","read","update","preRender","render","postRender"],Vw=40;function Ow(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=ri.reduce((f,d)=>(f[d]=Dw(()=>n=!0),f),{}),s=f=>i[f].process(o),a=()=>{const f=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(f-o.timestamp,Vw),1),o.timestamp=f,o.isProcessing=!0,ri.forEach(s),o.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,o.isProcessing||e(a)};return{schedule:ri.reduce((f,d)=>{const m=i[d];return f[d]=(x,y=!1,S=!1)=>(n||l(),m.schedule(x,y,S)),f},{}),cancel:f=>ri.forEach(d=>i[d].cancel(f)),state:o,steps:i}}const{schedule:K,cancel:Mt,state:Se,steps:la}=Ow(typeof requestAnimationFrame<"u"?requestAnimationFrame:ie,!0),bw={useVisualState:Fm({scrapeMotionValuesFromProps:bm,createRenderState:_m,onMount:(e,t,{renderState:n,latestValues:r})=>{K.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),K.render(()=>{Bu(n,r,{enableHardwareAcceleration:!1},Uu(t.tagName),e.transformTemplate),Om(t,n)})}})},Iw={useVisualState:Fm({scrapeMotionValuesFromProps:$u,createRenderState:zu})};function Fw(e,{forwardMotionProps:t=!1},n,r){return{...Iu(e)?bw:Iw,preloadedFeatures:n,useRender:Nw(t),createVisualElement:r,Component:e}}function Pt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const zm=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Ms(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const zw=e=>t=>zm(t)&&e(t,Ms(t));function Tt(e,t,n,r){return Pt(e,t,zw(n),r)}const Bw=(e,t)=>n=>t(e(n)),Jt=(...e)=>e.reduce(Bw);function Bm(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const bf=Bm("dragHorizontal"),If=Bm("dragVertical");function Um(e){let t=!1;if(e==="y")t=If();else if(e==="x")t=bf();else{const n=bf(),r=If();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function $m(){const e=Um(!0);return e?(e(),!1):!0}class dn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Ff(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),o=(i,s)=>{if(i.pointerType==="touch"||$m())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&K.update(()=>a[r](i,s))};return Tt(e.current,n,o,{passive:!e.getProps()[r]})}class Uw extends dn{mount(){this.unmount=Jt(Ff(this.node,!0),Ff(this.node,!1))}unmount(){}}class $w extends dn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Jt(Pt(this.node.current,"focus",()=>this.onFocus()),Pt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Wm=(e,t)=>t?e===t?!0:Wm(e,t.parentElement):!1;function ua(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Ms(n))}class Ww extends dn{constructor(){super(...arguments),this.removeStartListeners=ie,this.removeEndListeners=ie,this.removeAccessibleListeners=ie,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=Tt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:f}=this.node.getProps();K.update(()=>{!f&&!Wm(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),s=Tt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Jt(i,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||ua("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&K.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Pt(this.node.current,"keyup",s),ua("down",(a,l)=>{this.startPress(a,l)})},n=Pt(this.node.current,"keydown",t),r=()=>{this.isPressing&&ua("cancel",(i,s)=>this.cancelPress(i,s))},o=Pt(this.node.current,"blur",r);this.removeAccessibleListeners=Jt(n,o)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:o}=this.node.getProps();o&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!$m()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&K.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Tt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Pt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Jt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Cl=new WeakMap,ca=new WeakMap,Hw=e=>{const t=Cl.get(e.target);t&&t(e)},Gw=e=>{e.forEach(Hw)};function Kw({root:e,...t}){const n=e||document;ca.has(n)||ca.set(n,{});const r=ca.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(Gw,{root:e,...t})),r[o]}function Qw(e,t,n){const r=Kw(t);return Cl.set(e,n),r.observe(e),()=>{Cl.delete(e),r.unobserve(e)}}const Xw={some:0,all:1};class Yw extends dn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:Xw[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(l)};return Qw(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Zw(t,n))&&this.startObserver()}unmount(){}}function Zw({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const qw={inView:{Feature:Yw},tap:{Feature:Ww},focus:{Feature:$w},hover:{Feature:Uw}};function Hm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Jw(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function eS(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function js(e,t,n){const r=e.getProps();return Wu(r,t,n!==void 0?n:r.custom,Jw(e),eS(e))}let tS=ie,Hu=ie;const en=e=>e*1e3,kt=e=>e/1e3,nS={current:!1},Gm=e=>Array.isArray(e)&&typeof e[0]=="number";function Km(e){return!!(!e||typeof e=="string"&&Qm[e]||Gm(e)||Array.isArray(e)&&e.every(Km))}const Br=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Qm={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Br([0,.65,.55,1]),circOut:Br([.55,0,1,.45]),backIn:Br([.31,.01,.66,-.59]),backOut:Br([.33,1.53,.69,.99])};function Xm(e){if(e)return Gm(e)?Br(e):Array.isArray(e)?e.map(Xm):Qm[e]}function rS(e,t,n,{delay:r=0,duration:o,repeat:i=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Xm(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}function oS(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Ym=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,iS=1e-7,sS=12;function aS(e,t,n,r,o){let i,s,a=0;do s=t+(n-t)/2,i=Ym(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>iS&&++a<sS);return s}function Vo(e,t,n,r){if(e===t&&n===r)return ie;const o=i=>aS(i,0,1,e,n);return i=>i===0||i===1?i:Ym(o(i),t,r)}const lS=Vo(.42,0,1,1),uS=Vo(0,0,.58,1),Zm=Vo(.42,0,.58,1),cS=e=>Array.isArray(e)&&typeof e[0]!="number",qm=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jm=e=>t=>1-e(1-t),Gu=e=>1-Math.sin(Math.acos(e)),eg=Jm(Gu),fS=qm(Gu),tg=Vo(.33,1.53,.69,.99),Ku=Jm(tg),dS=qm(Ku),pS=e=>(e*=2)<1?.5*Ku(e):.5*(2-Math.pow(2,-10*(e-1))),hS={linear:ie,easeIn:lS,easeInOut:Zm,easeOut:uS,circIn:Gu,circInOut:fS,circOut:eg,backIn:Ku,backInOut:dS,backOut:tg,anticipate:pS},zf=e=>{if(Array.isArray(e)){Hu(e.length===4);const[t,n,r,o]=e;return Vo(t,n,r,o)}else if(typeof e=="string")return hS[e];return e},Qu=(e,t)=>n=>!!(_o(n)&&mw.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),ng=(e,t,n)=>r=>{if(!_o(r))return r;const[o,i,s,a]=r.match(As);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},mS=e=>on(0,255,e),fa={...Fn,transform:e=>Math.round(mS(e))},Tn={test:Qu("rgb","red"),parse:ng("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+fa.transform(e)+", "+fa.transform(t)+", "+fa.transform(n)+", "+Jr(qr.transform(r))+")"};function gS(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const Pl={test:Qu("#"),parse:gS,transform:Tn.transform},tr={test:Qu("hsl","hue"),parse:ng("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+vt.transform(Jr(t))+", "+vt.transform(Jr(n))+", "+Jr(qr.transform(r))+")"},Te={test:e=>Tn.test(e)||Pl.test(e)||tr.test(e),parse:e=>Tn.test(e)?Tn.parse(e):tr.test(e)?tr.parse(e):Pl.parse(e),transform:e=>_o(e)?e:e.hasOwnProperty("red")?Tn.transform(e):tr.transform(e)},te=(e,t,n)=>-n*e+n*t+e;function da(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function vS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=da(l,a,e+1/3),i=da(l,a,e),s=da(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}const pa=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},yS=[Pl,Tn,tr],xS=e=>yS.find(t=>t.test(e));function Bf(e){const t=xS(e);let n=t.parse(e);return t===tr&&(n=vS(n)),n}const rg=(e,t)=>{const n=Bf(e),r=Bf(t),o={...n};return i=>(o.red=pa(n.red,r.red,i),o.green=pa(n.green,r.green,i),o.blue=pa(n.blue,r.blue,i),o.alpha=te(n.alpha,r.alpha,i),Tn.transform(o))};function wS(e){var t,n;return isNaN(e)&&_o(e)&&(((t=e.match(As))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Nm))===null||n===void 0?void 0:n.length)||0)>0}const og={regex:pw,countKey:"Vars",token:"${v}",parse:ie},ig={regex:Nm,countKey:"Colors",token:"${c}",parse:Te.parse},sg={regex:As,countKey:"Numbers",token:"${n}",parse:Fn.parse};function ha(e,{regex:t,countKey:n,token:r,parse:o}){const i=e.tokenised.match(t);i&&(e["num"+n]=i.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...i.map(o)))}function es(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ha(n,og),ha(n,ig),ha(n,sg),n}function ag(e){return es(e).values}function lg(e){const{values:t,numColors:n,numVars:r,tokenised:o}=es(e),i=t.length;return s=>{let a=o;for(let l=0;l<i;l++)l<r?a=a.replace(og.token,s[l]):l<r+n?a=a.replace(ig.token,Te.transform(s[l])):a=a.replace(sg.token,Jr(s[l]));return a}}const SS=e=>typeof e=="number"?0:e;function CS(e){const t=ag(e);return lg(e)(t.map(SS))}const sn={test:wS,parse:ag,createTransformer:lg,getAnimatableNone:CS},ug=(e,t)=>n=>`${n>0?t:e}`;function cg(e,t){return typeof e=="number"?n=>te(e,t,n):Te.test(e)?rg(e,t):e.startsWith("var(")?ug(e,t):dg(e,t)}const fg=(e,t)=>{const n=[...e],r=n.length,o=e.map((i,s)=>cg(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}},PS=(e,t)=>{const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=cg(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}},dg=(e,t)=>{const n=sn.createTransformer(t),r=es(e),o=es(t);return r.numVars===o.numVars&&r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?Jt(fg(r.values,o.values),n):ug(e,t)},Eo=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Uf=(e,t)=>n=>te(e,t,n);function ES(e){return typeof e=="number"?Uf:typeof e=="string"?Te.test(e)?rg:dg:Array.isArray(e)?fg:typeof e=="object"?PS:Uf}function TS(e,t,n){const r=[],o=n||ES(e[0]),i=e.length-1;for(let s=0;s<i;s++){let a=o(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||ie:t;a=Jt(l,a)}r.push(a)}return r}function pg(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Hu(i===t.length),i===1)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=TS(t,r,o),a=s.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=Eo(e[c],e[c+1],u);return s[c](f)};return n?u=>l(on(e[0],e[i-1],u)):l}function kS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=Eo(0,t,r);e.push(te(n,1,o))}}function RS(e){const t=[0];return kS(t,e.length-1),t}function LS(e,t){return e.map(n=>n*t)}function NS(e,t){return e.map(()=>t||Zm).splice(0,e.length-1)}function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=cS(r)?r.map(zf):zf(r),i={done:!1,value:t[0]},s=LS(n&&n.length===t.length?n:RS(t),e),a=pg(s,t,{ease:Array.isArray(o)?o:NS(t,o)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}function hg(e,t){return t?e*(1e3/t):0}const AS=5;function mg(e,t,n){const r=Math.max(t-AS,0);return hg(n-e(r),t-r)}const ma=.001,MS=.01,$f=10,jS=.05,_S=1;function DS({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i;tS(e<=en($f));let s=1-t;s=on(jS,_S,s),e=on(MS,$f,kt(e)),s<1?(o=u=>{const c=u*s,f=c*e,d=c-n,m=El(u,s),x=Math.exp(-f);return ma-d/m*x},i=u=>{const f=u*s*e,d=f*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,x=Math.exp(-f),y=El(Math.pow(u,2),s);return(-o(u)+ma>0?-1:1)*((d-m)*x)/y}):(o=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-ma+c*f},i=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const a=5/e,l=OS(o,i,a);if(e=en(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const VS=12;function OS(e,t,n){let r=n;for(let o=1;o<VS;o++)r=r-e(r)/t(r);return r}function El(e,t){return e*Math.sqrt(1-t*t)}const bS=["duration","bounce"],IS=["stiffness","damping","mass"];function Wf(e,t){return t.some(n=>e[n]!==void 0)}function FS(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Wf(e,IS)&&Wf(e,bS)){const n=DS(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function gg({keyframes:e,restDelta:t,restSpeed:n,...r}){const o=e[0],i=e[e.length-1],s={done:!1,value:o},{stiffness:a,damping:l,mass:u,duration:c,velocity:f,isResolvedFromDuration:d}=FS({...r,velocity:-kt(r.velocity||0)}),m=f||0,x=l/(2*Math.sqrt(a*u)),y=i-o,S=kt(Math.sqrt(a/u)),g=Math.abs(y)<5;n||(n=g?.01:2),t||(t=g?.005:.5);let p;if(x<1){const h=El(S,x);p=w=>{const P=Math.exp(-x*S*w);return i-P*((m+x*S*y)/h*Math.sin(h*w)+y*Math.cos(h*w))}}else if(x===1)p=h=>i-Math.exp(-S*h)*(y+(m+S*y)*h);else{const h=S*Math.sqrt(x*x-1);p=w=>{const P=Math.exp(-x*S*w),T=Math.min(h*w,300);return i-P*((m+x*S*y)*Math.sinh(T)+h*y*Math.cosh(T))/h}}return{calculatedDuration:d&&c||null,next:h=>{const w=p(h);if(d)s.done=h>=c;else{let P=m;h!==0&&(x<1?P=mg(p,h,w):P=0);const T=Math.abs(P)<=n,E=Math.abs(i-w)<=t;s.done=T&&E}return s.value=s.done?i:w,s}}}function Hf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},m=k=>a!==void 0&&k<a||l!==void 0&&k>l,x=k=>a===void 0?l:l===void 0||Math.abs(a-k)<Math.abs(l-k)?a:l;let y=n*t;const S=f+y,g=s===void 0?S:s(S);g!==S&&(y=g-f);const p=k=>-y*Math.exp(-k/r),h=k=>g+p(k),w=k=>{const N=p(k),M=h(k);d.done=Math.abs(N)<=u,d.value=d.done?g:M};let P,T;const E=k=>{m(d.value)&&(P=k,T=gg({keyframes:[d.value,x(d.value)],velocity:mg(h,k,d.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:k=>{let N=!1;return!T&&P===void 0&&(N=!0,w(k),E(k)),P!==void 0&&k>P?T.next(k-P):(!N&&w(k),d)}}}const zS=e=>{const t=({timestamp:n})=>e(n);return{start:()=>K.update(t,!0),stop:()=>Mt(t),now:()=>Se.isProcessing?Se.timestamp:performance.now()}},Gf=2e4;function Kf(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Gf;)t+=n,r=e.next(t);return t>=Gf?1/0:t}const BS={decay:Hf,inertia:Hf,tween:ts,keyframes:ts,spring:gg};function ns({autoplay:e=!0,delay:t=0,driver:n=zS,keyframes:r,type:o="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:f,...d}){let m=1,x=!1,y,S;const g=()=>{S=new Promise(D=>{y=D})};g();let p;const h=BS[o]||ts;let w;h!==ts&&typeof r[0]!="number"&&(w=pg([0,100],r,{clamp:!1}),r=[0,100]);const P=h({...d,keyframes:r});let T;a==="mirror"&&(T=h({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let E="idle",k=null,N=null,M=null;P.calculatedDuration===null&&i&&(P.calculatedDuration=Kf(P));const{calculatedDuration:$}=P;let V=1/0,J=1/0;$!==null&&(V=$+s,J=V*(i+1)-s);let j=0;const Z=D=>{if(N===null)return;m>0&&(N=Math.min(N,D)),m<0&&(N=Math.min(D-J/m,N)),k!==null?j=k:j=Math.round(D-N)*m;const B=j-t*(m>=0?1:-1),H=m>=0?B<0:B>J;j=Math.max(B,0),E==="finished"&&k===null&&(j=J);let be=j,zn=P;if(i){const Ds=Math.min(j,J)/V;let bo=Math.floor(Ds),hn=Ds%1;!hn&&Ds>=1&&(hn=1),hn===1&&bo--,bo=Math.min(bo,i+1),!!(bo%2)&&(a==="reverse"?(hn=1-hn,s&&(hn-=s/V)):a==="mirror"&&(zn=T)),be=on(0,1,hn)*V}const Ie=H?{done:!1,value:r[0]}:zn.next(be);w&&(Ie.value=w(Ie.value));let{done:pn}=Ie;!H&&$!==null&&(pn=m>=0?j>=J:j<=0);const Cv=k===null&&(E==="finished"||E==="running"&&pn);return f&&f(Ie.value),Cv&&L(),Ie},z=()=>{p&&p.stop(),p=void 0},ae=()=>{E="idle",z(),y(),g(),N=M=null},L=()=>{E="finished",c&&c(),z(),y()},_=()=>{if(x)return;p||(p=n(Z));const D=p.now();l&&l(),k!==null?N=D-k:(!N||E==="finished")&&(N=D),E==="finished"&&g(),M=N,k=null,E="running",p.start()};e&&_();const b={then(D,B){return S.then(D,B)},get time(){return kt(j)},set time(D){D=en(D),j=D,k!==null||!p||m===0?k=D:N=p.now()-D/m},get duration(){const D=P.calculatedDuration===null?Kf(P):P.calculatedDuration;return kt(D)},get speed(){return m},set speed(D){D===m||!p||(m=D,b.time=kt(j))},get state(){return E},play:_,pause:()=>{E="paused",k=j},stop:()=>{x=!0,E!=="idle"&&(E="idle",u&&u(),ae())},cancel:()=>{M!==null&&Z(M),ae()},complete:()=>{E="finished"},sample:D=>(N=0,Z(D))};return b}function US(e){let t;return()=>(t===void 0&&(t=e()),t)}const $S=US(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),WS=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),oi=10,HS=2e4,GS=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Km(t.ease);function KS(e,t,{onUpdate:n,onComplete:r,...o}){if(!($S()&&WS.has(t)&&!o.repeatDelay&&o.repeatType!=="mirror"&&o.damping!==0&&o.type!=="inertia"))return!1;let s=!1,a,l,u=!1;const c=()=>{l=new Promise(h=>{a=h})};c();let{keyframes:f,duration:d=300,ease:m,times:x}=o;if(GS(t,o)){const h=ns({...o,repeat:0,delay:0});let w={done:!1,value:f[0]};const P=[];let T=0;for(;!w.done&&T<HS;)w=h.sample(T),P.push(w.value),T+=oi;x=void 0,f=P,d=T-oi,m="linear"}const y=rS(e.owner.current,t,f,{...o,duration:d,ease:m,times:x}),S=()=>{u=!1,y.cancel()},g=()=>{u=!0,K.update(S),a(),c()};return y.onfinish=()=>{u||(e.set(oS(f,o)),r&&r(),g())},{then(h,w){return l.then(h,w)},attachTimeline(h){return y.timeline=h,y.onfinish=null,ie},get time(){return kt(y.currentTime||0)},set time(h){y.currentTime=en(h)},get speed(){return y.playbackRate},set speed(h){y.playbackRate=h},get duration(){return kt(d)},play:()=>{s||(y.play(),Mt(S))},pause:()=>y.pause(),stop:()=>{if(s=!0,y.playState==="idle")return;const{currentTime:h}=y;if(h){const w=ns({...o,autoplay:!1});e.setWithVelocity(w.sample(h-oi).value,w.sample(h).value,oi)}g()},complete:()=>{u||y.finish()},cancel:g}}function QS({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const o=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ie,pause:ie,stop:ie,then:i=>(i(),Promise.resolve()),cancel:ie,complete:ie});return t?ns({keyframes:[0,1],duration:0,delay:t,onComplete:o}):o()}const XS={type:"spring",stiffness:500,damping:25,restSpeed:10},YS=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),ZS={type:"keyframes",duration:.8},qS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},JS=(e,{keyframes:t})=>t.length>2?ZS:In.has(e)?e.startsWith("scale")?YS(t[1]):XS:qS,Tl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(sn.test(t)||t==="0")&&!t.startsWith("url(")),eC=new Set(["brightness","contrast","saturate","opacity"]);function tC(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(As)||[];if(!r)return e;const o=n.replace(r,"");let i=eC.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const nC=/([a-z-]*)\(.*?\)/g,kl={...sn,getAnimatableNone:e=>{const t=e.match(nC);return t?t.map(tC).join(" "):e}},rC={...Am,color:Te,backgroundColor:Te,outlineColor:Te,fill:Te,stroke:Te,borderColor:Te,borderTopColor:Te,borderRightColor:Te,borderBottomColor:Te,borderLeftColor:Te,filter:kl,WebkitFilter:kl},Xu=e=>rC[e];function vg(e,t){let n=Xu(e);return n!==kl&&(n=sn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yg=e=>/^0[^.\s]+$/.test(e);function oC(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||yg(e)}function iC(e,t,n,r){const o=Tl(t,n);let i;Array.isArray(n)?i=[...n]:i=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<i.length;u++)i[u]===null&&(i[u]=u===0?s:i[u-1]),oC(i[u])&&l.push(u),typeof i[u]=="string"&&i[u]!=="none"&&i[u]!=="0"&&(a=i[u]);if(o&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];i[c]=vg(t,a)}return i}function sC({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function Yu(e,t){return e[t]||e.default||e}const aC={skipAnimations:!1},Zu=(e,t,n,r={})=>o=>{const i=Yu(r,e)||{},s=i.delay||r.delay||0;let{elapsed:a=0}=r;a=a-en(s);const l=iC(t,e,n,i),u=l[0],c=l[l.length-1],f=Tl(e,u),d=Tl(e,c);let m={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-a,onUpdate:x=>{t.set(x),i.onUpdate&&i.onUpdate(x)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(sC(i)||(m={...m,...JS(e,m)}),m.duration&&(m.duration=en(m.duration)),m.repeatDelay&&(m.repeatDelay=en(m.repeatDelay)),!f||!d||nS.current||i.type===!1||aC.skipAnimations)return QS(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const x=KS(t,e,m);if(x)return x}return ns(m)};function rs(e){return!!(Oe(e)&&e.add)}const xg=e=>/^\-?\d*\.?\d+$/.test(e);function qu(e,t){e.indexOf(t)===-1&&e.push(t)}function Ju(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ec{constructor(){this.subscriptions=[]}add(t){return qu(this.subscriptions,t),()=>Ju(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const lC=e=>!isNaN(parseFloat(e));class uC{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,o=!0)=>{this.prev=this.current,this.current=r;const{delta:i,timestamp:s}=Se;this.lastUpdated!==s&&(this.timeDelta=i,this.lastUpdated=s,K.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>K.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=lC(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ec);const r=this.events[t].add(n);return t==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?hg(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function yr(e,t){return new uC(e,t)}const wg=e=>t=>t.test(e),cC={test:e=>e==="auto",parse:e=>e},Sg=[Fn,O,vt,Vt,vw,gw,cC],Vr=e=>Sg.find(wg(e)),fC=[...Sg,Te,sn],dC=e=>fC.find(wg(e));function pC(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,yr(n))}function hC(e,t){const n=js(e,t);let{transitionEnd:r={},transition:o={},...i}=n?e.makeTargetAnimatable(n,!1):{};i={...i,...r};for(const s in i){const a=Mw(i[s]);pC(e,s,a)}}function mC(e,t,n){var r,o;const i=Object.keys(t).filter(a=>!e.hasValue(a)),s=i.length;if(s)for(let a=0;a<s;a++){const l=i[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(o=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&o!==void 0?o:t[l]),c!=null&&(typeof c=="string"&&(xg(c)||yg(c))?c=parseFloat(c):!dC(c)&&sn.test(u)&&(c=vg(l,u)),e.addValue(l,yr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function gC(e,t){return t?(t[e]||t.default||t).from:void 0}function vC(e,t,n){const r={};for(const o in e){const i=gC(o,t);if(i!==void 0)r[o]=i;else{const s=n.getValue(o);s&&(r[o]=s.get())}}return r}function yC({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function xC(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Cg(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(i=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const f in a){const d=e.getValue(f),m=a[f];if(!d||m===void 0||c&&yC(c,f))continue;const x={delay:n,elapsed:0,...Yu(i||{},f)};if(window.HandoffAppearAnimations){const g=e.getProps()[Pm];if(g){const p=window.HandoffAppearAnimations(g,f,d,K);p!==null&&(x.elapsed=p,x.isHandoff=!0)}}let y=!x.isHandoff&&!xC(d,m);if(x.type==="spring"&&(d.getVelocity()||x.velocity)&&(y=!1),d.animation&&(y=!1),y)continue;d.start(Zu(f,d,m,e.shouldReduceMotion&&In.has(f)?{type:!1}:x));const S=d.animation;rs(l)&&(l.add(f),S.then(()=>l.remove(f))),u.push(S)}return s&&Promise.all(u).then(()=>{s&&hC(e,s)}),u}function Rl(e,t,n={}){const r=js(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const i=r?()=>Promise.all(Cg(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:f}=o;return wC(e,t,u+l,c,f,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[l,u]=a==="beforeChildren"?[i,s]:[s,i];return l().then(()=>u())}else return Promise.all([i(),s(n.delay)])}function wC(e,t,n=0,r=0,o=1,i){const s=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(SC).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Rl(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function SC(e,t){return e.sortNodePosition(t)}function CC(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>Rl(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=Rl(e,t,n);else{const o=typeof t=="function"?js(e,t,n.custom):t;r=Promise.all(Cg(e,o,n))}return r.then(()=>e.notify("AnimationComplete",t))}const PC=[...Vu].reverse(),EC=Vu.length;function TC(e){return t=>Promise.all(t.map(({animation:n,options:r})=>CC(e,n,r)))}function kC(e){let t=TC(e);const n=LC();let r=!0;const o=(l,u)=>{const c=js(e,u);if(c){const{transition:f,transitionEnd:d,...m}=c;l={...l,...m,...d}}return l};function i(l){t=l(e)}function s(l,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],m=new Set;let x={},y=1/0;for(let g=0;g<EC;g++){const p=PC[g],h=n[p],w=c[p]!==void 0?c[p]:f[p],P=Co(w),T=p===u?h.isActive:null;T===!1&&(y=g);let E=w===f[p]&&w!==c[p]&&P;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),h.protectedKeys={...x},!h.isActive&&T===null||!w&&!h.prevProp||Ls(w)||typeof w=="boolean")continue;let N=RC(h.prevProp,w)||p===u&&h.isActive&&!E&&P||g>y&&P,M=!1;const $=Array.isArray(w)?w:[w];let V=$.reduce(o,{});T===!1&&(V={});const{prevResolvedValues:J={}}=h,j={...J,...V},Z=z=>{N=!0,m.has(z)&&(M=!0,m.delete(z)),h.needsAnimating[z]=!0};for(const z in j){const ae=V[z],L=J[z];if(x.hasOwnProperty(z))continue;let _=!1;Ji(ae)&&Ji(L)?_=!Hm(ae,L):_=ae!==L,_?ae!==void 0?Z(z):m.add(z):ae!==void 0&&m.has(z)?Z(z):h.protectedKeys[z]=!0}h.prevProp=w,h.prevResolvedValues=V,h.isActive&&(x={...x,...V}),r&&e.blockInitialAnimation&&(N=!1),N&&(!E||M)&&d.push(...$.map(z=>({animation:z,options:{type:p,...l}})))}if(m.size){const g={};m.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(g[p]=h)}),d.push({animation:g})}let S=!!d.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(d):Promise.resolve()}function a(l,u,c){var f;if(n[l].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(m=>{var x;return(x=m.animationState)===null||x===void 0?void 0:x.setActive(l,u)}),n[l].isActive=u;const d=s(c,l);for(const m in n)n[m].protectedKeys={};return d}return{animateChanges:s,setActive:a,setAnimateFunction:i,getState:()=>n}}function RC(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hm(t,e):!1}function mn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function LC(){return{animate:mn(!0),whileInView:mn(),whileHover:mn(),whileTap:mn(),whileDrag:mn(),whileFocus:mn(),exit:mn()}}class NC extends dn{constructor(t){super(t),t.animationState||(t.animationState=kC(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Ls(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let AC=0;class MC extends dn{constructor(){super(...arguments),this.id=AC++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===o)return;const i=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const jC={animation:{Feature:NC},exit:{Feature:MC}},Qf=(e,t)=>Math.abs(e-t);function _C(e,t){const n=Qf(e.x,t.x),r=Qf(e.y,t.y);return Math.sqrt(n**2+r**2)}class Pg{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=va(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,m=_C(f.offset,{x:0,y:0})>=3;if(!d&&!m)return;const{point:x}=f,{timestamp:y}=Se;this.history.push({...x,timestamp:y});const{onStart:S,onMove:g}=this.handlers;d||(S&&S(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ga(d,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:m,onSessionEnd:x,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=va(f.type==="pointercancel"?this.lastMoveEventInfo:ga(d,this.transformPagePoint),this.history);this.startEvent&&m&&m(f,S),x&&x(f,S)},!zm(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=Ms(t),a=ga(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=Se;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,va(a,this.history)),this.removeListeners=Jt(Tt(this.contextWindow,"pointermove",this.handlePointerMove),Tt(this.contextWindow,"pointerup",this.handlePointerUp),Tt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Mt(this.updatePoint)}}function ga(e,t){return t?{point:t(e.point)}:e}function Xf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function va({point:e},t){return{point:e,delta:Xf(e,Eg(t)),offset:Xf(e,DC(t)),velocity:VC(t,.1)}}function DC(e){return e[0]}function Eg(e){return e[e.length-1]}function VC(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Eg(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>en(t)));)n--;if(!r)return{x:0,y:0};const i=kt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function We(e){return e.max-e.min}function Ll(e,t=0,n=.01){return Math.abs(e-t)<=n}function Yf(e,t,n,r=.5){e.origin=r,e.originPoint=te(t.min,t.max,e.origin),e.scale=We(n)/We(t),(Ll(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=te(n.min,n.max,e.origin)-e.originPoint,(Ll(e.translate)||isNaN(e.translate))&&(e.translate=0)}function eo(e,t,n,r){Yf(e.x,t.x,n.x,r?r.originX:void 0),Yf(e.y,t.y,n.y,r?r.originY:void 0)}function Zf(e,t,n){e.min=n.min+t.min,e.max=e.min+We(t)}function OC(e,t,n){Zf(e.x,t.x,n.x),Zf(e.y,t.y,n.y)}function qf(e,t,n){e.min=t.min-n.min,e.max=e.min+We(t)}function to(e,t,n){qf(e.x,t.x,n.x),qf(e.y,t.y,n.y)}function bC(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?te(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?te(n,e,r.max):Math.min(e,n)),e}function Jf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function IC(e,{top:t,left:n,bottom:r,right:o}){return{x:Jf(e.x,n,o),y:Jf(e.y,t,r)}}function ed(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function FC(e,t){return{x:ed(e.x,t.x),y:ed(e.y,t.y)}}function zC(e,t){let n=.5;const r=We(e),o=We(t);return o>r?n=Eo(t.min,t.max-r,e.min):r>o&&(n=Eo(e.min,e.max-o,t.min)),on(0,1,n)}function BC(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Nl=.35;function UC(e=Nl){return e===!1?e=0:e===!0&&(e=Nl),{x:td(e,"left","right"),y:td(e,"top","bottom")}}function td(e,t,n){return{min:nd(e,t),max:nd(e,n)}}function nd(e,t){return typeof e=="number"?e:e[t]||0}const rd=()=>({translate:0,scale:1,origin:0,originPoint:0}),nr=()=>({x:rd(),y:rd()}),od=()=>({min:0,max:0}),ue=()=>({x:od(),y:od()});function Xe(e){return[e("x"),e("y")]}function Tg({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function $C({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function WC(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ya(e){return e===void 0||e===1}function Al({scale:e,scaleX:t,scaleY:n}){return!ya(e)||!ya(t)||!ya(n)}function yn(e){return Al(e)||kg(e)||e.z||e.rotate||e.rotateX||e.rotateY}function kg(e){return id(e.x)||id(e.y)}function id(e){return e&&e!=="0%"}function os(e,t,n){const r=e-n,o=t*r;return n+o}function sd(e,t,n,r,o){return o!==void 0&&(e=os(e,o,r)),os(e,n,r)+t}function Ml(e,t=0,n=1,r,o){e.min=sd(e.min,t,n,r,o),e.max=sd(e.max,t,n,r,o)}function Rg(e,{x:t,y:n}){Ml(e.x,t.translate,t.scale,t.originPoint),Ml(e.y,n.translate,n.scale,n.originPoint)}function HC(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let a=0;a<o;a++){i=n[a],s=i.projectionDelta;const l=i.instance;l&&l.style&&l.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Rg(e,s)),r&&yn(i.latestValues)&&rr(e,i.latestValues))}t.x=ad(t.x),t.y=ad(t.y)}function ad(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function It(e,t){e.min=e.min+t,e.max=e.max+t}function ld(e,t,[n,r,o]){const i=t[o]!==void 0?t[o]:.5,s=te(e.min,e.max,i);Ml(e,t[n],t[r],s,t.scale)}const GC=["x","scaleX","originX"],KC=["y","scaleY","originY"];function rr(e,t){ld(e.x,t,GC),ld(e.y,t,KC)}function Lg(e,t){return Tg(WC(e.getBoundingClientRect(),t))}function QC(e,t,n){const r=Lg(e,n),{scroll:o}=t;return o&&(It(r.x,o.offset.x),It(r.y,o.offset.y)),r}const Ng=({current:e})=>e?e.ownerDocument.defaultView:null,XC=new WeakMap;class YC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ue(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Ms(c,"page").point)},i=(c,f)=>{const{drag:d,dragPropagation:m,onDragStart:x}=this.getProps();if(d&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Um(d),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xe(S=>{let g=this.getAxisMotionValue(S).get()||0;if(vt.test(g)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[S];h&&(g=We(h)*(parseFloat(g)/100))}}this.originPoint[S]=g}),x&&K.update(()=>x(c,f),!1,!0);const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:d,dragDirectionLock:m,onDirectionLock:x,onDrag:y}=this.getProps();if(!d&&!this.openGlobalLock)return;const{offset:S}=f;if(m&&this.currentDirection===null){this.currentDirection=ZC(S),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",f.point,S),this.updateAxis("y",f.point,S),this.visualElement.render(),y&&y(c,f)},a=(c,f)=>this.stop(c,f),l=()=>Xe(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Pg(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Ng(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&K.update(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!ii(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=bC(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&er(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=IC(o.layoutBox,n):this.constraints=!1,this.elastic=UC(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Xe(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=BC(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!er(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=QC(r,o.root,this.visualElement.getTransformPagePoint());let s=FC(o.layout.layoutBox,i);if(n){const a=n($C(s));this.hasMutatedConstraints=!!a,a&&(s=Tg(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Xe(c=>{if(!ii(c,n,this.currentDirection))return;let f=l&&l[c]||{};s&&(f={min:0,max:0});const d=o?200:1e6,m=o?40:1e7,x={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...f};return this.startAxisValueAnimation(c,x)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Zu(t,r,0,n))}stopAnimation(){Xe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Xe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Xe(n=>{const{drag:r}=this.getProps();if(!ii(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:a}=o.layout.layoutBox[n];i.set(t[n]-te(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!er(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Xe(s=>{const a=this.getAxisMotionValue(s);if(a){const l=a.get();o[s]=zC({min:l,max:l},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Xe(s=>{if(!ii(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(te(l,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;XC.set(this.visualElement,this);const t=this.visualElement.current,n=Tt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();er(l)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),r();const s=Pt(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Xe(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=l[c].translate,f.set(f.get()+l[c].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=Nl,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function ii(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function ZC(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class qC extends dn{constructor(t){super(t),this.removeGroupControls=ie,this.removeListeners=ie,this.controls=new YC(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ie}unmount(){this.removeGroupControls(),this.removeListeners()}}const ud=e=>(t,n)=>{e&&K.update(()=>e(t,n))};class JC extends dn{constructor(){super(...arguments),this.removePointerDownListener=ie}onPointerDown(t){this.session=new Pg(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ng(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:ud(t),onStart:ud(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&K.update(()=>o(i,s))}}}mount(){this.removePointerDownListener=Tt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function eP(){const e=v.useContext(ks);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=v.useId();return v.useEffect(()=>r(o),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}const Si={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function cd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Or={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(O.test(e))e=parseFloat(e);else return e;const n=cd(e,t.target.x),r=cd(e,t.target.y);return`${n}% ${r}%`}},tP={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=sn.parse(e);if(o.length>5)return r;const i=sn.createTransformer(e),s=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+s]/=a,o[1+s]/=l;const u=te(a,l,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}};class nP extends de.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;uw(rP),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Si.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||K.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Ag(e){const[t,n]=eP(),r=v.useContext(bu);return de.createElement(nP,{...e,layoutGroup:r,switchLayoutGroup:v.useContext(Tm),isPresent:t,safeToRemove:n})}const rP={borderRadius:{...Or,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Or,borderTopRightRadius:Or,borderBottomLeftRadius:Or,borderBottomRightRadius:Or,boxShadow:tP},Mg=["TopLeft","TopRight","BottomLeft","BottomRight"],oP=Mg.length,fd=e=>typeof e=="string"?parseFloat(e):e,dd=e=>typeof e=="number"||O.test(e);function iP(e,t,n,r,o,i){o?(e.opacity=te(0,n.opacity!==void 0?n.opacity:1,sP(r)),e.opacityExit=te(t.opacity!==void 0?t.opacity:1,0,aP(r))):i&&(e.opacity=te(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<oP;s++){const a=`border${Mg[s]}Radius`;let l=pd(t,a),u=pd(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||dd(l)===dd(u)?(e[a]=Math.max(te(fd(l),fd(u),r),0),(vt.test(u)||vt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=te(t.rotate||0,n.rotate||0,r))}function pd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const sP=jg(0,.5,eg),aP=jg(.5,.95,ie);function jg(e,t,n){return r=>r<e?0:r>t?1:n(Eo(e,t,r))}function hd(e,t){e.min=t.min,e.max=t.max}function Ke(e,t){hd(e.x,t.x),hd(e.y,t.y)}function md(e,t,n,r,o){return e-=t,e=os(e,1/n,r),o!==void 0&&(e=os(e,1/o,r)),e}function lP(e,t=0,n=1,r=.5,o,i=e,s=e){if(vt.test(t)&&(t=parseFloat(t),t=te(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=te(i.min,i.max,r);e===i&&(a-=t),e.min=md(e.min,t,n,a,o),e.max=md(e.max,t,n,a,o)}function gd(e,t,[n,r,o],i,s){lP(e,t[n],t[r],t[o],t.scale,i,s)}const uP=["x","scaleX","originX"],cP=["y","scaleY","originY"];function vd(e,t,n,r){gd(e.x,t,uP,n?n.x:void 0,r?r.x:void 0),gd(e.y,t,cP,n?n.y:void 0,r?r.y:void 0)}function yd(e){return e.translate===0&&e.scale===1}function _g(e){return yd(e.x)&&yd(e.y)}function fP(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Dg(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function xd(e){return We(e.x)/We(e.y)}class dP{constructor(){this.members=[]}add(t){qu(this.members,t),t.scheduleRender()}remove(t){if(Ju(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function wd(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(r=`translate3d(${o}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const pP=(e,t)=>e.depth-t.depth;class hP{constructor(){this.children=[],this.isDirty=!1}add(t){qu(this.children,t),this.isDirty=!0}remove(t){Ju(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(pP),this.isDirty=!1,this.children.forEach(t)}}function mP(e,t){const n=performance.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(Mt(r),e(i-t))};return K.read(r,!0),()=>Mt(r)}function gP(e){window.MotionDebug&&window.MotionDebug.record(e)}function vP(e){return e instanceof SVGElement&&e.tagName!=="svg"}function yP(e,t,n){const r=Oe(e)?e:yr(e);return r.start(Zu("",r,t,n)),r.animation}const Sd=["","X","Y","Z"],xP={visibility:"hidden"},Cd=1e3;let wP=0;const xn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Vg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},a=t==null?void 0:t()){this.id=wP++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,xn.totalNodes=xn.resolvedTargetDeltas=xn.recalculatedProjection=0,this.nodes.forEach(PP),this.nodes.forEach(LP),this.nodes.forEach(NP),this.nodes.forEach(EP),gP(xn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new hP)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new ec),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=vP(s),this.instance=s;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=mP(d,250),Si.hasAnimatedSinceResize&&(Si.hasAnimatedSinceResize=!1,this.nodes.forEach(Ed))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:m,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||c.getDefaultTransition()||DP,{onLayoutAnimationStart:S,onLayoutAnimationComplete:g}=c.getProps(),p=!this.targetLayout||!Dg(this.targetLayout,x)||m,h=!d&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,h);const w={...Yu(y,"layout"),onPlay:S,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else d||Ed(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Mt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(AP),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Pd);return}this.isUpdating||this.nodes.forEach(kP),this.isUpdating=!1,this.nodes.forEach(RP),this.nodes.forEach(SP),this.nodes.forEach(CP),this.clearAllSnapshots();const a=performance.now();Se.delta=on(0,1e3/60,a-Se.timestamp),Se.timestamp=a,Se.isProcessing=!0,la.update.process(Se),la.preRender.process(Se),la.render.process(Se),Se.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(TP),this.sharedNodes.forEach(MP)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ue(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!_g(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(a||yn(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),VP(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ue();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(It(a.x,l.offset.x),It(a.y,l.offset.y)),a}removeElementScroll(s){const a=ue();Ke(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){Ke(a,s);const{scroll:d}=this.root;d&&(It(a.x,-d.offset.x),It(a.y,-d.offset.y))}It(a.x,c.offset.x),It(a.y,c.offset.y)}}return a}applyTransform(s,a=!1){const l=ue();Ke(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&rr(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),yn(c.latestValues)&&rr(l,c.latestValues)}return yn(this.latestValues)&&rr(l,this.latestValues),l}removeTransform(s){const a=ue();Ke(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!yn(u.latestValues))continue;Al(u.latestValues)&&u.updateSnapshot();const c=ue(),f=u.measurePageBox();Ke(c,f),vd(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return yn(this.latestValues)&&vd(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Se.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=Se.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),to(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Ke(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ue(),this.targetWithTransforms=ue()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),OC(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ke(this.target,this.layout.layoutBox),Rg(this.target,this.targetDelta)):Ke(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),to(this.relativeTargetOrigin,this.target,m.target),Ke(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}xn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Al(this.parent.latestValues)||kg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Se.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;Ke(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,m=this.treeScale.y;HC(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:x}=a;if(!x){this.projectionTransform&&(this.projectionDelta=nr(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=nr(),this.projectionDeltaWithTransform=nr());const y=this.projectionTransform;eo(this.projectionDelta,this.layoutCorrected,x,this.latestValues),this.projectionTransform=wd(this.projectionDelta,this.treeScale),(this.projectionTransform!==y||this.treeScale.x!==d||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),xn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},f=nr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=ue(),m=l?l.source:void 0,x=this.layout?this.layout.source:void 0,y=m!==x,S=this.getStack(),g=!S||S.members.length<=1,p=!!(y&&!g&&this.options.crossfade===!0&&!this.path.some(_P));this.animationProgress=0;let h;this.mixTargetDelta=w=>{const P=w/1e3;Td(f.x,s.x,P),Td(f.y,s.y,P),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(to(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),jP(this.relativeTarget,this.relativeTargetOrigin,d,P),h&&fP(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=ue()),Ke(h,this.relativeTarget)),y&&(this.animationValues=c,iP(c,u,this.latestValues,P,p,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Mt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{Si.hasAnimatedSinceResize=!0,this.currentAnimation=yP(0,Cd,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Cd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Og(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ue();const f=We(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+f;const d=We(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+d}Ke(a,l),rr(a,c),eo(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new dP),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<Sd.length;c++){const f="rotate"+Sd[c];l[f]&&(u[f]=l[f],s.setStaticValue(f,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return xP;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=wi(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=wi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!yn(this.latestValues)&&(y.transform=c?c({},""):"none",this.hasProjected=!1),y}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=wd(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:m,y:x}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${x.origin*100}% 0`,f.animationValues?u.opacity=f===this?(l=(a=d.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const y in Zi){if(d[y]===void 0)continue;const{correct:S,applyTo:g}=Zi[y],p=u.transform==="none"?d[y]:S(d[y],f);if(g){const h=g.length;for(let w=0;w<h;w++)u[g[w]]=p}else u[y]=p}return this.options.layoutId&&(u.pointerEvents=f===this?wi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Pd),this.root.sharedNodes.clear()}}}function SP(e){e.updateLayout()}function CP(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?Xe(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],m=We(d);d.min=r[f].min,d.max=d.min+m}):Og(i,n.layoutBox,r)&&Xe(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],m=We(r[f]);d.max=d.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+m)});const a=nr();eo(a,r,n.layoutBox);const l=nr();s?eo(l,e.applyTransform(o,!0),n.measuredBox):eo(l,r,n.layoutBox);const u=!_g(a);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:m}=f;if(d&&m){const x=ue();to(x,n.layoutBox,d.layoutBox);const y=ue();to(y,r,m.layoutBox),Dg(x,y)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=x,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function PP(e){xn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function EP(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function TP(e){e.clearSnapshot()}function Pd(e){e.clearMeasurements()}function kP(e){e.isLayoutDirty=!1}function RP(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ed(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function LP(e){e.resolveTargetDelta()}function NP(e){e.calcProjection()}function AP(e){e.resetRotation()}function MP(e){e.removeLeadSnapshot()}function Td(e,t,n){e.translate=te(t.translate,0,n),e.scale=te(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function kd(e,t,n,r){e.min=te(t.min,n.min,r),e.max=te(t.max,n.max,r)}function jP(e,t,n,r){kd(e.x,t.x,n.x,r),kd(e.y,t.y,n.y,r)}function _P(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const DP={duration:.45,ease:[.4,0,.1,1]},Rd=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Ld=Rd("applewebkit/")&&!Rd("chrome/")?Math.round:ie;function Nd(e){e.min=Ld(e.min),e.max=Ld(e.max)}function VP(e){Nd(e.x),Nd(e.y)}function Og(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ll(xd(t),xd(n),.2)}const OP=Vg({attachResizeListener:(e,t)=>Pt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),xa={current:void 0},bg=Vg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!xa.current){const e=new OP({});e.mount(window),e.setOptions({layoutScroll:!0}),xa.current=e}return xa.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),bP={pan:{Feature:JC},drag:{Feature:qC,ProjectionNode:bg,MeasureLayout:Ag}},IP=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function FP(e){const t=IP.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function jl(e,t,n=1){const[r,o]=FP(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return xg(s)?parseFloat(s):s}else return Sl(o)?jl(o,t,n+1):o}function zP(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(o=>{const i=o.get();if(!Sl(i))return;const s=jl(i,r);s&&o.set(s)});for(const o in t){const i=t[o];if(!Sl(i))continue;const s=jl(i,r);s&&(t[o]=s,n||(n={}),n[o]===void 0&&(n[o]=i))}return{target:t,transitionEnd:n}}const BP=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Ig=e=>BP.has(e),UP=e=>Object.keys(e).some(Ig),Ad=e=>e===Fn||e===O,Md=(e,t)=>parseFloat(e.split(", ")[t]),jd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/);if(o)return Md(o[1],t);{const i=r.match(/^matrix\((.+)\)$/);return i?Md(i[1],e):0}},$P=new Set(["x","y","z"]),WP=jo.filter(e=>!$P.has(e));function HP(e){const t=[];return WP.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const xr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:jd(4,13),y:jd(5,14)};xr.translateX=xr.x;xr.translateY=xr.y;const GP=(e,t,n)=>{const r=t.measureViewportBox(),o=t.current,i=getComputedStyle(o),{display:s}=i,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=xr[u](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=xr[u](l,i)}),e},KP=(e,t,n={},r={})=>{t={...t},r={...r};const o=Object.keys(t).filter(Ig);let i=[],s=!1;const a=[];if(o.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],f=Vr(c);const d=t[l];let m;if(Ji(d)){const x=d.length,y=d[0]===null?1:0;c=d[y],f=Vr(c);for(let S=y;S<x&&d[S]!==null;S++)m?Hu(Vr(d[S])===m):m=Vr(d[S])}else m=Vr(d);if(f!==m)if(Ad(f)&&Ad(m)){const x=u.get();typeof x=="string"&&u.set(parseFloat(x)),typeof d=="string"?t[l]=parseFloat(d):Array.isArray(d)&&m===O&&(t[l]=d.map(parseFloat))}else f!=null&&f.transform&&(m!=null&&m.transform)&&(c===0||d===0)?c===0?u.set(m.transform(c)):t[l]=f.transform(d):(s||(i=HP(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(d))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=GP(t,e,a);return i.length&&i.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),Rs&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function QP(e,t,n,r){return UP(t)?KP(e,t,n,r):{target:t,transitionEnd:r}}const XP=(e,t,n,r)=>{const o=zP(e,t,r);return t=o.target,r=o.transitionEnd,QP(e,t,n,r)},_l={current:null},Fg={current:!1};function YP(){if(Fg.current=!0,!!Rs)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>_l.current=e.matches;e.addListener(t),t()}else _l.current=!1}function ZP(e,t,n){const{willChange:r}=t;for(const o in t){const i=t[o],s=n[o];if(Oe(i))e.addValue(o,i),rs(r)&&r.add(o);else if(Oe(s))e.addValue(o,yr(i,{owner:e})),rs(r)&&r.remove(o);else if(s!==i)if(e.hasValue(o)){const a=e.getValue(o);!a.hasAnimated&&a.set(i)}else{const a=e.getStaticValue(o);e.addValue(o,yr(a!==void 0?a:i,{owner:e}))}}for(const o in n)t[o]===void 0&&e.removeValue(o);return t}const _d=new WeakMap,zg=Object.keys(Po),qP=zg.length,Dd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],JP=Ou.length;class eE{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,visualState:i},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>K.render(this.render,!1,!0);const{latestValues:a,renderState:l}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=s,this.isControllingVariants=Ns(n),this.isVariantNode=Em(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const f in c){const d=c[f];a[f]!==void 0&&Oe(d)&&(d.set(a[f],!1),rs(u)&&u.add(f))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,_d.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Fg.current||YP(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:_l.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){_d.delete(this.current),this.projection&&this.projection.unmount(),Mt(this.notifyUpdate),Mt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=In.has(t),o=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&K.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{o(),i()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,o,i){let s,a;for(let l=0;l<qP;l++){const u=zg[l],{isEnabled:c,Feature:f,ProjectionNode:d,MeasureLayout:m}=Po[u];d&&(s=d),c(n)&&(!this.features[u]&&f&&(this.features[u]=new f(this)),m&&(a=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:f,layoutScroll:d,layoutRoot:m}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||f&&er(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:i,layoutScroll:d,layoutRoot:m})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ue()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Dd.length;r++){const o=Dd[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i=t["on"+o];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=ZP(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<JP;r++){const o=Ou[r],i=this.props[o];(Co(i)||i===!1)&&(n[o]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=yr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,o=typeof r=="string"||typeof r=="object"?(n=Wu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Oe(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ec),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Bg extends eE{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:o},i){let s=vC(r,t||{},this);if(o&&(n&&(n=o(n)),r&&(r=o(r)),s&&(s=o(s))),i){mC(this,r,s);const a=XP(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function tE(e){return window.getComputedStyle(e)}class nE extends Bg{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(In.has(n)){const r=Xu(n);return r&&r.default||0}else{const r=tE(t),o=(Lm(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Lg(t,n)}build(t,n,r,o){Fu(t,n,r,o.transformTemplate)}scrapeMotionValuesFromProps(t,n){return $u(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Oe(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,o){Dm(t,n,r,o)}}class rE extends Bg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(In.has(n)){const r=Xu(n);return r&&r.default||0}return n=Vm.has(n)?n:Du(n),t.getAttribute(n)}measureInstanceViewportBox(){return ue()}scrapeMotionValuesFromProps(t,n){return bm(t,n)}build(t,n,r,o){Bu(t,n,r,this.isSVGTag,o.transformTemplate)}renderInstance(t,n,r,o){Om(t,n,r,o)}mount(t){this.isSVGTag=Uu(t.tagName),super.mount(t)}}const oE=(e,t)=>Iu(e)?new rE(t,{enableHardwareAcceleration:!1}):new nE(t,{enableHardwareAcceleration:!0}),iE={layout:{ProjectionNode:bg,MeasureLayout:Ag}},sE={...jC,...qw,...bP,...iE},aE=aw((e,t)=>Fw(e,t,sE,oE));function Ug(){const e=v.useRef(!1);return _u(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function lE(){const e=Ug(),[t,n]=v.useState(0),r=v.useCallback(()=>{e.current&&n(t+1)},[t]);return[v.useCallback(()=>K.postRender(r),[r]),t]}class uE extends v.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function cE({children:e,isPresent:t}){const n=v.useId(),r=v.useRef(null),o=v.useRef({width:0,height:0,top:0,left:0});return v.useInsertionEffect(()=>{const{width:i,height:s,top:a,left:l}=o.current;if(t||!r.current||!i||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${s}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),v.createElement(uE,{isPresent:t,childRef:r,sizeRef:o},v.cloneElement(e,{ref:r}))}const wa=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const a=Im(fE),l=v.useId(),u=v.useMemo(()=>({id:l,initial:t,isPresent:n,custom:o,onExitComplete:c=>{a.set(c,!0);for(const f of a.values())if(!f)return;r&&r()},register:c=>(a.set(c,!1),()=>a.delete(c))}),i?void 0:[n]);return v.useMemo(()=>{a.forEach((c,f)=>a.set(f,!1))},[n]),v.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=v.createElement(cE,{isPresent:n},e)),v.createElement(ks.Provider,{value:u},e)};function fE(){return new Map}function dE(e){return v.useEffect(()=>()=>e(),[])}const wn=e=>e.key||"";function pE(e,t){e.forEach(n=>{const r=wn(n);t.set(r,n)})}function hE(e){const t=[];return v.Children.forEach(e,n=>{v.isValidElement(n)&&t.push(n)}),t}const mE=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:o,presenceAffectsLayout:i=!0,mode:s="sync"})=>{const a=v.useContext(bu).forceRender||lE()[0],l=Ug(),u=hE(e);let c=u;const f=v.useRef(new Map).current,d=v.useRef(c),m=v.useRef(new Map).current,x=v.useRef(!0);if(_u(()=>{x.current=!1,pE(u,m),d.current=c}),dE(()=>{x.current=!0,m.clear(),f.clear()}),x.current)return v.createElement(v.Fragment,null,c.map(p=>v.createElement(wa,{key:wn(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:i,mode:s},p)));c=[...c];const y=d.current.map(wn),S=u.map(wn),g=y.length;for(let p=0;p<g;p++){const h=y[p];S.indexOf(h)===-1&&!f.has(h)&&f.set(h,void 0)}return s==="wait"&&f.size&&(c=[]),f.forEach((p,h)=>{if(S.indexOf(h)!==-1)return;const w=m.get(h);if(!w)return;const P=y.indexOf(h);let T=p;if(!T){const E=()=>{f.delete(h);const k=Array.from(m.keys()).filter(N=>!S.includes(N));if(k.forEach(N=>m.delete(N)),d.current=u.filter(N=>{const M=wn(N);return M===h||k.includes(M)}),!f.size){if(l.current===!1)return;a(),r&&r()}};T=v.createElement(wa,{key:wn(w),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:i,mode:s},w),f.set(h,T)}c.splice(P,0,T)}),c=c.map(p=>{const h=p.key;return f.has(h)?p:v.createElement(wa,{key:wn(p),isPresent:!0,presenceAffectsLayout:i,mode:s},p)}),v.createElement(v.Fragment,null,f.size?c:c.map(p=>v.cloneElement(p)))},Vd=[{name:"Home",path:"/",icon:Ux},{name:"About",path:"/about",icon:Af},{name:"Services",path:"/services",icon:Wx},{name:"Projects",path:"/projects",icon:Fx},{name:"Team",path:"/team",icon:Af},{name:"Blog",path:"/blog",icon:Bx},{name:"Testimonials",path:"/testimonials",icon:Xx},{name:"Features",path:"/features",icon:Zx},{name:"Contact",path:"/contact",icon:xm}],gE=()=>{const[e,t]=v.useState(!1),n=()=>t(!e),r="text-primary font-semibold border-b-2 border-primary",o="text-foreground hover:text-primary transition-colors duration-300";return C.jsxs("nav",{className:"bg-background/80 backdrop-blur-md shadow-sm sticky top-0 z-50",children:[C.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:C.jsxs("div",{className:"flex items-center justify-between h-20",children:[C.jsxs(pt,{to:"/",className:"flex items-center",children:[C.jsx("img",{src:"/geostrat-logo.svg",className:"h-10 w-auto mr-2",alt:"GEOSTRATDRC Logo"}),C.jsxs("span",{className:"font-jost text-2xl font-bold text-primary",children:["GEOSTRAT",C.jsx("span",{className:"text-secondary",children:"DRC"})]})]}),C.jsx("div",{className:"hidden md:flex items-center space-x-6",children:Vd.slice(0,5).map(i=>C.jsx(Cf,{to:i.path,className:({isActive:s})=>`${s?r:o} font-medium text-sm`,children:i.name},i.name))}),C.jsx("div",{className:"hidden md:flex items-center",children:C.jsx(Zr,{asChild:!0,variant:"default",className:"font-jost",children:C.jsx(pt,{to:"/quote",children:"Get a Quote"})})}),C.jsx("div",{className:"md:hidden flex items-center",children:C.jsx(Zr,{variant:"ghost",onClick:n,"aria-label":"Toggle menu",children:e?C.jsx(wm,{className:"h-6 w-6 text-primary"}):C.jsx(Qx,{className:"h-6 w-6 text-primary"})})})]})}),C.jsx(mE,{children:e&&C.jsx(aE.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"md:hidden bg-background shadow-lg absolute w-full",children:C.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center",children:[Vd.map(i=>C.jsx(Cf,{to:i.path,onClick:n,className:({isActive:s})=>`block px-3 py-2 rounded-md text-base font-medium w-full text-center ${s?"bg-primary/10 text-primary":"text-foreground hover:bg-gray-100"}`,children:C.jsxs("div",{className:"flex items-center justify-center",children:[C.jsx(i.icon,{className:"h-5 w-5 mr-2"}),i.name]})},i.name)),C.jsx(Zr,{asChild:!0,variant:"default",className:"w-full mt-4 font-jost",onClick:n,children:C.jsx(pt,{to:"/quote",children:"Get a Quote"})})]})})})]})},$g=de.forwardRef(({className:e,type:t,...n},r)=>C.jsx("input",{type:t,className:fn("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));$g.displayName="Input";const vE=()=>{const e=new Date().getFullYear(),t=[{name:"About Us",path:"/about"},{name:"Services",path:"/services"},{name:"Projects",path:"/projects"},{name:"Contact Us",path:"/contact"},{name:"Blog",path:"/blog"}],n=[{name:"Climate-Smart Agriculture",path:"/services#climate-smart-agriculture"},{name:"Natural Risk Assessments",path:"/services#natural-risk-assessments"},{name:"Spatial Data Analytics",path:"/services#spatial-data-analytics"},{name:"Urban Planning",path:"/services#urban-planning"},{name:"Environmental Monitoring",path:"/services#environmental-monitoring"},{name:"Natural Resources Management",path:"/services#natural-resources-management"}],r=o=>{o.preventDefault(),console.log("Newsletter submitted:",o.target.email.value),o.target.reset()};return C.jsx("footer",{className:"bg-gradient-to-r from-primary via-teal-600 to-secondary text-primary-foreground pt-16 pb-8 font-sans",children:C.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[C.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[C.jsxs("div",{children:[C.jsxs(pt,{to:"/",className:"flex items-center mb-4",children:[C.jsx("img",{src:"/geostrat-logo.svg",className:"h-10 w-auto mr-2 filter brightness-0 invert",alt:"GEOSTRATDRC Logo White"}),C.jsxs("span",{className:"font-jost text-2xl font-bold",children:["GEOSTRAT",C.jsx("span",{className:"opacity-80",children:"DRC"})]})]}),C.jsx("p",{className:"text-sm opacity-90 mb-4",children:"Leading geospatial innovation in Central Africa. We provide cutting-edge solutions for a sustainable future."}),C.jsxs("div",{className:"flex space-x-4",children:[C.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:C.jsx(zx,{size:20})}),C.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:C.jsx(Yx,{size:20})}),C.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:C.jsx(Hx,{size:20})}),C.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-75 transition-opacity",children:C.jsx($x,{size:20})})]})]}),C.jsxs("div",{children:[C.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Quick Links"}),C.jsx("ul",{className:"space-y-2",children:t.map(o=>C.jsx("li",{children:C.jsx(pt,{to:o.path,className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity",children:o.name})},o.name))})]}),C.jsxs("div",{children:[C.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Our Services"}),C.jsxs("ul",{className:"space-y-2",children:[n.map(o=>C.jsx("li",{children:C.jsx(pt,{to:o.path,className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity",children:o.name})},o.name)),C.jsx("li",{children:C.jsx(pt,{to:"/services",className:"text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity font-semibold",children:"View All Services..."})})]})]}),C.jsxs("div",{children:[C.jsx("p",{className:"font-jost text-lg font-semibold mb-4",children:"Stay Updated"}),C.jsx("p",{className:"text-sm opacity-90 mb-3",children:"Subscribe to our newsletter for the latest updates and insights."}),C.jsxs("form",{onSubmit:r,className:"flex flex-col sm:flex-row gap-2",children:[C.jsx($g,{type:"email",name:"email",placeholder:"Enter your email",className:"bg-white/20 border-white/30 placeholder-white/70 text-white focus:bg-white/30 focus:ring-white/50 flex-grow",required:!0}),C.jsx(Zr,{type:"submit",variant:"outline",className:"bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary font-jost",children:"Subscribe"})]})]})]}),C.jsxs("div",{className:"border-t border-white/20 pt-8 mt-8 text-center md:text-left",children:[C.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 text-sm",children:[C.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[C.jsx(Kx,{size:18,className:"mr-2 opacity-80"}),C.jsx("span",{className:"opacity-90",children:"Kinshasa, Democratic Republic of Congo"})]}),C.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[C.jsx(xm,{size:18,className:"mr-2 opacity-80"}),C.jsx("span",{className:"opacity-90",children:"+243 XXX XXX XXX"})]}),C.jsxs("div",{className:"flex items-center justify-center md:justify-start",children:[C.jsx(Gx,{size:18,className:"mr-2 opacity-80"}),C.jsx("span",{className:"opacity-90",children:"<EMAIL>"})]})]}),C.jsxs("p",{className:"text-sm opacity-80",children:["© ",e," GEOSTRATDRC. All Rights Reserved.",C.jsx(pt,{to:"/privacy-policy",className:"hover:underline ml-2",children:"Privacy Policy"})," |",C.jsx(pt,{to:"/terms-of-service",className:"hover:underline ml-1",children:"Terms of Service"})]})]})]})})};function yE(){const{pathname:e}=Er();return v.useEffect(()=>{window.scrollTo(0,0)},[e]),null}const xE=({children:e})=>C.jsxs("div",{className:"flex flex-col min-h-screen",children:[C.jsx(yE,{}),C.jsx(gE,{}),C.jsx("main",{className:"flex-grow",children:e}),C.jsx(vE,{})]});function ze(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function MT(e,t){const n=v.createContext(t),r=i=>{const{children:s,...a}=i,l=v.useMemo(()=>a,Object.values(a));return C.jsx(n.Provider,{value:l,children:s})};r.displayName=e+"Provider";function o(i){const s=v.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function Wg(e,t=[]){let n=[];function r(i,s){const a=v.createContext(s),l=n.length;n=[...n,s];const u=f=>{var g;const{scope:d,children:m,...x}=f,y=((g=d==null?void 0:d[e])==null?void 0:g[l])||a,S=v.useMemo(()=>x,Object.values(x));return C.jsx(y.Provider,{value:S,children:m})};u.displayName=i+"Provider";function c(f,d){var y;const m=((y=d==null?void 0:d[e])==null?void 0:y[l])||a,x=v.useContext(m);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>v.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return v.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,wE(o,...t)]}function wE(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(i)[`__scope${u}`];return{...a,...f}},{});return v.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function SE(e){const t=e+"CollectionProvider",[n,r]=Wg(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=y=>{const{scope:S,children:g}=y,p=de.useRef(null),h=de.useRef(new Map).current;return C.jsx(o,{scope:S,itemMap:h,collectionRef:p,children:g})};s.displayName=t;const a=e+"CollectionSlot",l=Yi(a),u=de.forwardRef((y,S)=>{const{scope:g,children:p}=y,h=i(a,g),w=rn(S,h.collectionRef);return C.jsx(l,{ref:w,children:p})});u.displayName=a;const c=e+"CollectionItemSlot",f="data-radix-collection-item",d=Yi(c),m=de.forwardRef((y,S)=>{const{scope:g,children:p,...h}=y,w=de.useRef(null),P=rn(S,w),T=i(c,g);return de.useEffect(()=>(T.itemMap.set(w,{ref:w,...h}),()=>void T.itemMap.delete(w))),C.jsx(d,{[f]:"",ref:P,children:p})});m.displayName=c;function x(y){const S=i(e+"CollectionConsumer",y);return de.useCallback(()=>{const p=S.collectionRef.current;if(!p)return[];const h=Array.from(p.querySelectorAll(`[${f}]`));return Array.from(S.itemMap.values()).sort((T,E)=>h.indexOf(T.ref.current)-h.indexOf(E.ref.current))},[S.collectionRef,S.itemMap])}return[{Provider:s,Slot:u,ItemSlot:m},x,r]}var CE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],yt=CE.reduce((e,t)=>{const n=Yi(`Primitive.${t}`),r=v.forwardRef((o,i)=>{const{asChild:s,...a}=o,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Hg(e,t){e&&Ss.flushSync(()=>e.dispatchEvent(t))}function _n(e){const t=v.useRef(e);return v.useEffect(()=>{t.current=e}),v.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function PE(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e);v.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var EE="DismissableLayer",Dl="dismissableLayer.update",TE="dismissableLayer.pointerDownOutside",kE="dismissableLayer.focusOutside",Od,Gg=v.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Kg=v.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=v.useContext(Gg),[c,f]=v.useState(null),d=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=v.useState({}),x=rn(t,E=>f(E)),y=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=y.indexOf(S),p=c?y.indexOf(c):-1,h=u.layersWithOutsidePointerEventsDisabled.size>0,w=p>=g,P=LE(E=>{const k=E.target,N=[...u.branches].some(M=>M.contains(k));!w||N||(o==null||o(E),s==null||s(E),E.defaultPrevented||a==null||a())},d),T=NE(E=>{const k=E.target;[...u.branches].some(M=>M.contains(k))||(i==null||i(E),s==null||s(E),E.defaultPrevented||a==null||a())},d);return PE(E=>{p===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&a&&(E.preventDefault(),a()))},d),v.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Od=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),bd(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=Od)}},[c,d,n,u]),v.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),bd())},[c,u]),v.useEffect(()=>{const E=()=>m({});return document.addEventListener(Dl,E),()=>document.removeEventListener(Dl,E)},[]),C.jsx(yt.div,{...l,ref:x,style:{pointerEvents:h?w?"auto":"none":void 0,...e.style},onFocusCapture:ze(e.onFocusCapture,T.onFocusCapture),onBlurCapture:ze(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:ze(e.onPointerDownCapture,P.onPointerDownCapture)})});Kg.displayName=EE;var RE="DismissableLayerBranch",Qg=v.forwardRef((e,t)=>{const n=v.useContext(Gg),r=v.useRef(null),o=rn(t,r);return v.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),C.jsx(yt.div,{...e,ref:o})});Qg.displayName=RE;function LE(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e),r=v.useRef(!1),o=v.useRef(()=>{});return v.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){Xg(TE,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function NE(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e),r=v.useRef(!1);return v.useEffect(()=>{const o=i=>{i.target&&!r.current&&Xg(kE,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function bd(){const e=new CustomEvent(Dl);document.dispatchEvent(e)}function Xg(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Hg(o,i):o.dispatchEvent(i)}var AE=Kg,ME=Qg,To=globalThis!=null&&globalThis.document?v.useLayoutEffect:()=>{},jE="Portal",Yg=v.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=v.useState(!1);To(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?X0.createPortal(C.jsx(yt.div,{...r,ref:t}),s):null});Yg.displayName=jE;function _E(e,t){return v.useReducer((n,r)=>t[n][r]??n,e)}var Zg=e=>{const{present:t,children:n}=e,r=DE(t),o=typeof n=="function"?n({present:r.isPresent}):v.Children.only(n),i=rn(r.ref,VE(o));return typeof n=="function"||r.isPresent?v.cloneElement(o,{ref:i}):null};Zg.displayName="Presence";function DE(e){const[t,n]=v.useState(),r=v.useRef(null),o=v.useRef(e),i=v.useRef("none"),s=e?"mounted":"unmounted",[a,l]=_E(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return v.useEffect(()=>{const u=si(r.current);i.current=a==="mounted"?u:"none"},[a]),To(()=>{const u=r.current,c=o.current;if(c!==e){const d=i.current,m=si(u);e?l("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(c&&d!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),To(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,f=m=>{const y=si(r.current).includes(m.animationName);if(m.target===t&&y&&(l("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},d=m=>{m.target===t&&(i.current=si(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:v.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function si(e){return(e==null?void 0:e.animationName)||"none"}function VE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var OE=Yd[" useInsertionEffect ".trim().toString()]||To;function bE({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=IE({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:o;{const c=v.useRef(e!==void 0);v.useEffect(()=>{const f=c.current;f!==a&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=a},[a,r])}const u=v.useCallback(c=>{var f;if(a){const d=FE(c)?c(e):c;d!==e&&((f=s.current)==null||f.call(s,d))}else i(c)},[a,e,i,s]);return[l,u]}function IE({defaultProp:e,onChange:t}){const[n,r]=v.useState(e),o=v.useRef(n),i=v.useRef(t);return OE(()=>{i.current=t},[t]),v.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function FE(e){return typeof e=="function"}var zE=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),BE="VisuallyHidden",tc=v.forwardRef((e,t)=>C.jsx(yt.span,{...e,ref:t,style:{...zE,...e.style}}));tc.displayName=BE;var nc="ToastProvider",[rc,UE,$E]=SE("Toast"),[qg,jT]=Wg("Toast",[$E]),[WE,_s]=qg(nc),Jg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=v.useState(null),[u,c]=v.useState(0),f=v.useRef(!1),d=v.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${nc}\`. Expected non-empty \`string\`.`),C.jsx(rc.Provider,{scope:t,children:C.jsx(WE,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:v.useCallback(()=>c(m=>m+1),[]),onToastRemove:v.useCallback(()=>c(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:d,children:s})})};Jg.displayName=nc;var ev="ToastViewport",HE=["F8"],Vl="toast.viewportPause",Ol="toast.viewportResume",tv=v.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=HE,label:o="Notifications ({hotkey})",...i}=e,s=_s(ev,n),a=UE(n),l=v.useRef(null),u=v.useRef(null),c=v.useRef(null),f=v.useRef(null),d=rn(t,f,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;v.useEffect(()=>{const S=g=>{var h;r.length!==0&&r.every(w=>g[w]||g.code===w)&&((h=f.current)==null||h.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),v.useEffect(()=>{const S=l.current,g=f.current;if(x&&S&&g){const p=()=>{if(!s.isClosePausedRef.current){const T=new CustomEvent(Vl);g.dispatchEvent(T),s.isClosePausedRef.current=!0}},h=()=>{if(s.isClosePausedRef.current){const T=new CustomEvent(Ol);g.dispatchEvent(T),s.isClosePausedRef.current=!1}},w=T=>{!S.contains(T.relatedTarget)&&h()},P=()=>{S.contains(document.activeElement)||h()};return S.addEventListener("focusin",p),S.addEventListener("focusout",w),S.addEventListener("pointermove",p),S.addEventListener("pointerleave",P),window.addEventListener("blur",p),window.addEventListener("focus",h),()=>{S.removeEventListener("focusin",p),S.removeEventListener("focusout",w),S.removeEventListener("pointermove",p),S.removeEventListener("pointerleave",P),window.removeEventListener("blur",p),window.removeEventListener("focus",h)}}},[x,s.isClosePausedRef]);const y=v.useCallback(({tabbingDirection:S})=>{const p=a().map(h=>{const w=h.ref.current,P=[w,...oT(w)];return S==="forwards"?P:P.reverse()});return(S==="forwards"?p.reverse():p).flat()},[a]);return v.useEffect(()=>{const S=f.current;if(S){const g=p=>{var P,T,E;const h=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!h){const k=document.activeElement,N=p.shiftKey;if(p.target===S&&N){(P=u.current)==null||P.focus();return}const V=y({tabbingDirection:N?"backwards":"forwards"}),J=V.findIndex(j=>j===k);Sa(V.slice(J+1))?p.preventDefault():N?(T=u.current)==null||T.focus():(E=c.current)==null||E.focus()}};return S.addEventListener("keydown",g),()=>S.removeEventListener("keydown",g)}},[a,y]),C.jsxs(ME,{ref:l,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&C.jsx(bl,{ref:u,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"forwards"});Sa(S)}}),C.jsx(rc.Slot,{scope:n,children:C.jsx(yt.ol,{tabIndex:-1,...i,ref:d})}),x&&C.jsx(bl,{ref:c,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"backwards"});Sa(S)}})]})});tv.displayName=ev;var nv="ToastFocusProxy",bl=v.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=_s(nv,n);return C.jsx(tc,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});bl.displayName=nv;var Oo="Toast",GE="toast.swipeStart",KE="toast.swipeMove",QE="toast.swipeCancel",XE="toast.swipeEnd",rv=v.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a,l]=bE({prop:r,defaultProp:o??!0,onChange:i,caller:Oo});return C.jsx(Zg,{present:n||a,children:C.jsx(qE,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:_n(e.onPause),onResume:_n(e.onResume),onSwipeStart:ze(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ze(e.onSwipeMove,u=>{const{x:c,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:ze(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ze(e.onSwipeEnd,u=>{const{x:c,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),l(!1)})})})});rv.displayName=Oo;var[YE,ZE]=qg(Oo,{onClose(){}}),qE=v.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:c,onSwipeMove:f,onSwipeCancel:d,onSwipeEnd:m,...x}=e,y=_s(Oo,n),[S,g]=v.useState(null),p=rn(t,j=>g(j)),h=v.useRef(null),w=v.useRef(null),P=o||y.duration,T=v.useRef(0),E=v.useRef(P),k=v.useRef(0),{onToastAdd:N,onToastRemove:M}=y,$=_n(()=>{var Z;(S==null?void 0:S.contains(document.activeElement))&&((Z=y.viewport)==null||Z.focus()),s()}),V=v.useCallback(j=>{!j||j===1/0||(window.clearTimeout(k.current),T.current=new Date().getTime(),k.current=window.setTimeout($,j))},[$]);v.useEffect(()=>{const j=y.viewport;if(j){const Z=()=>{V(E.current),u==null||u()},z=()=>{const ae=new Date().getTime()-T.current;E.current=E.current-ae,window.clearTimeout(k.current),l==null||l()};return j.addEventListener(Vl,z),j.addEventListener(Ol,Z),()=>{j.removeEventListener(Vl,z),j.removeEventListener(Ol,Z)}}},[y.viewport,P,l,u,V]),v.useEffect(()=>{i&&!y.isClosePausedRef.current&&V(P)},[i,P,y.isClosePausedRef,V]),v.useEffect(()=>(N(),()=>M()),[N,M]);const J=v.useMemo(()=>S?cv(S):null,[S]);return y.viewport?C.jsxs(C.Fragment,{children:[J&&C.jsx(JE,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:J}),C.jsx(YE,{scope:n,onClose:$,children:Ss.createPortal(C.jsx(rc.ItemSlot,{scope:n,children:C.jsx(AE,{asChild:!0,onEscapeKeyDown:ze(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||$(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:C.jsx(yt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...x,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ze(e.onKeyDown,j=>{j.key==="Escape"&&(a==null||a(j.nativeEvent),j.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:ze(e.onPointerDown,j=>{j.button===0&&(h.current={x:j.clientX,y:j.clientY})}),onPointerMove:ze(e.onPointerMove,j=>{if(!h.current)return;const Z=j.clientX-h.current.x,z=j.clientY-h.current.y,ae=!!w.current,L=["left","right"].includes(y.swipeDirection),_=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,b=L?_(0,Z):0,D=L?0:_(0,z),B=j.pointerType==="touch"?10:2,H={x:b,y:D},be={originalEvent:j,delta:H};ae?(w.current=H,ai(KE,f,be,{discrete:!1})):Id(H,y.swipeDirection,B)?(w.current=H,ai(GE,c,be,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(Z)>B||Math.abs(z)>B)&&(h.current=null)}),onPointerUp:ze(e.onPointerUp,j=>{const Z=w.current,z=j.target;if(z.hasPointerCapture(j.pointerId)&&z.releasePointerCapture(j.pointerId),w.current=null,h.current=null,Z){const ae=j.currentTarget,L={originalEvent:j,delta:Z};Id(Z,y.swipeDirection,y.swipeThreshold)?ai(XE,m,L,{discrete:!0}):ai(QE,d,L,{discrete:!0}),ae.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),JE=e=>{const{__scopeToast:t,children:n,...r}=e,o=_s(Oo,t),[i,s]=v.useState(!1),[a,l]=v.useState(!1);return nT(()=>s(!0)),v.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:C.jsx(Yg,{asChild:!0,children:C.jsx(tc,{...r,children:i&&C.jsxs(C.Fragment,{children:[o.label," ",n]})})})},eT="ToastTitle",ov=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C.jsx(yt.div,{...r,ref:t})});ov.displayName=eT;var tT="ToastDescription",iv=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C.jsx(yt.div,{...r,ref:t})});iv.displayName=tT;var sv="ToastAction",av=v.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?C.jsx(uv,{altText:n,asChild:!0,children:C.jsx(oc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${sv}\`. Expected non-empty \`string\`.`),null)});av.displayName=sv;var lv="ToastClose",oc=v.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=ZE(lv,n);return C.jsx(uv,{asChild:!0,children:C.jsx(yt.button,{type:"button",...r,ref:t,onClick:ze(e.onClick,o.onClose)})})});oc.displayName=lv;var uv=v.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return C.jsx(yt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function cv(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),rT(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...cv(r))}}),t}function ai(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Hg(o,i):o.dispatchEvent(i)}var Id=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function nT(e=()=>{}){const t=_n(e);To(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function rT(e){return e.nodeType===e.ELEMENT_NODE}function oT(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Sa(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var iT=Jg,fv=tv,dv=rv,pv=ov,hv=iv,mv=av,gv=oc;const sT=iT,vv=de.forwardRef(({className:e,...t},n)=>C.jsx(fv,{ref:n,className:fn("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));vv.displayName=fv.displayName;const aT=ym("data-[swipe=move]:transition-none group relative pointer-events-auto flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full data-[state=closed]:slide-out-to-right-full",{variants:{variant:{default:"bg-background border",destructive:"group destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),yv=de.forwardRef(({className:e,variant:t,...n},r)=>C.jsx(dv,{ref:r,className:fn(aT({variant:t}),e),...n}));yv.displayName=dv.displayName;const lT=de.forwardRef(({className:e,...t},n)=>C.jsx(mv,{ref:n,className:fn("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-destructive/30 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));lT.displayName=mv.displayName;const xv=de.forwardRef(({className:e,...t},n)=>C.jsx(gv,{ref:n,className:fn("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:C.jsx(wm,{className:"h-4 w-4"})}));xv.displayName=gv.displayName;const wv=de.forwardRef(({className:e,...t},n)=>C.jsx(pv,{ref:n,className:fn("text-sm font-semibold",e),...t}));wv.displayName=pv.displayName;const Sv=de.forwardRef(({className:e,...t},n)=>C.jsx(hv,{ref:n,className:fn("text-sm opacity-90",e),...t}));Sv.displayName=hv.displayName;const uT=1;let Ca=0;function cT(){return Ca=(Ca+1)%Number.MAX_VALUE,Ca.toString()}const ke={state:{toasts:[]},listeners:[],getState:()=>ke.state,setState:e=>{typeof e=="function"?ke.state=e(ke.state):ke.state={...ke.state,...e},ke.listeners.forEach(t=>t(ke.state))},subscribe:e=>(ke.listeners.push(e),()=>{ke.listeners=ke.listeners.filter(t=>t!==e)})},fT=({...e})=>{const t=cT(),n=o=>ke.setState(i=>({...i,toasts:i.toasts.map(s=>s.id===t?{...s,...o}:s)})),r=()=>ke.setState(o=>({...o,toasts:o.toasts.filter(i=>i.id!==t)}));return ke.setState(o=>({...o,toasts:[{...e,id:t,dismiss:r},...o.toasts].slice(0,uT)})),{id:t,dismiss:r,update:n}};function dT(){const[e,t]=v.useState(ke.getState());return v.useEffect(()=>ke.subscribe(r=>{t(r)}),[]),v.useEffect(()=>{const n=[];return e.toasts.forEach(r=>{if(r.duration===1/0)return;const o=setTimeout(()=>{r.dismiss()},r.duration||5e3);n.push(o)}),()=>{n.forEach(r=>clearTimeout(r))}},[e.toasts]),{toast:fT,toasts:e.toasts}}function pT(){const{toasts:e}=dT();return C.jsxs(sT,{children:[e.map(({id:t,title:n,description:r,action:o,...i})=>C.jsxs(yv,{...i,children:[C.jsxs("div",{className:"grid gap-1",children:[n&&C.jsx(wv,{children:n}),r&&C.jsx(Sv,{children:r})]}),o,C.jsx(xv,{})]},t)),C.jsx(vv,{})]})}const hT={theme:"system",setTheme:()=>null},mT=v.createContext(hT);function gT({children:e,defaultTheme:t="system",storageKey:n="vite-ui-theme",...r}){const[o,i]=v.useState(()=>localStorage.getItem(n)||t);v.useEffect(()=>{const a=window.document.documentElement;if(a.classList.remove("light","dark"),o==="system"){const l=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";a.classList.add(l);return}a.classList.add(o)},[o]);const s={theme:o,setTheme:a=>{localStorage.setItem(n,a),i(a)}};return C.jsx(mT.Provider,{...r,value:s,children:e})}const vT=v.lazy(()=>ct(()=>import("./HomePage-6eb144d0.js"),["assets/HomePage-6eb144d0.js","assets/card-4ea953f9.js","assets/label-e002f61b.js","assets/dialog-cccff784.js","assets/chevron-left-80490873.js","assets/chevron-right-119c9c04.js","assets/award-27c7cfeb.js","assets/shield-alert-0ea8318b.js","assets/alert-triangle-949c3288.js"])),yT=v.lazy(()=>ct(()=>import("./AboutPage-7ab4c867.js"),["assets/AboutPage-7ab4c867.js","assets/card-4ea953f9.js","assets/award-27c7cfeb.js"])),xT=v.lazy(()=>ct(()=>import("./ServicesPage-e1419ce4.js"),["assets/ServicesPage-e1419ce4.js","assets/card-4ea953f9.js","assets/chevron-right-119c9c04.js","assets/shield-alert-0ea8318b.js","assets/alert-triangle-949c3288.js"])),wT=v.lazy(()=>ct(()=>import("./ProjectsPage-183fcf7a.js"),["assets/ProjectsPage-183fcf7a.js","assets/card-4ea953f9.js","assets/dialog-cccff784.js"])),ST=v.lazy(()=>ct(()=>import("./TeamPage-e9d929fb.js"),["assets/TeamPage-e9d929fb.js","assets/card-4ea953f9.js","assets/avatar-59c20f5e.js","assets/award-27c7cfeb.js"])),CT=v.lazy(()=>ct(()=>import("./BlogPage-f63d9599.js"),["assets/BlogPage-f63d9599.js","assets/card-4ea953f9.js","assets/user-12f5ae94.js","assets/chevron-left-80490873.js","assets/chevron-right-119c9c04.js"])),PT=v.lazy(()=>ct(()=>import("./ContactPage-40f4dad7.js"),["assets/ContactPage-40f4dad7.js","assets/label-e002f61b.js","assets/card-4ea953f9.js"])),ET=v.lazy(()=>ct(()=>import("./TestimonialsPage-df86e980.js"),["assets/TestimonialsPage-df86e980.js","assets/card-4ea953f9.js","assets/avatar-59c20f5e.js","assets/chevron-left-80490873.js","assets/chevron-right-119c9c04.js"])),TT=v.lazy(()=>ct(()=>import("./FeaturesPage-dc3a9026.js"),["assets/FeaturesPage-dc3a9026.js","assets/card-4ea953f9.js"])),kT=v.lazy(()=>ct(()=>import("./QuotePage-8686f039.js"),["assets/QuotePage-8686f039.js","assets/label-e002f61b.js","assets/card-4ea953f9.js","assets/user-12f5ae94.js"])),RT=v.lazy(()=>ct(()=>import("./NotFoundPage-ffb224df.js"),["assets/NotFoundPage-ffb224df.js","assets/alert-triangle-949c3288.js"]));function LT(){return C.jsx(gT,{defaultTheme:"light",storageKey:"vite-ui-theme",children:C.jsxs(X1,{children:[C.jsx(xE,{children:C.jsx(v.Suspense,{fallback:C.jsx("div",{className:"flex justify-center items-center h-screen w-screen text-2xl font-jost",children:"Loading GEOSTRATDRC..."}),children:C.jsxs(B1,{children:[C.jsx(Qe,{path:"/",element:C.jsx(vT,{})}),C.jsx(Qe,{path:"/about",element:C.jsx(yT,{})}),C.jsx(Qe,{path:"/services",element:C.jsx(xT,{})}),C.jsx(Qe,{path:"/projects",element:C.jsx(wT,{})}),C.jsx(Qe,{path:"/team",element:C.jsx(ST,{})}),C.jsx(Qe,{path:"/blog",element:C.jsx(CT,{})}),C.jsx(Qe,{path:"/contact",element:C.jsx(PT,{})}),C.jsx(Qe,{path:"/testimonials",element:C.jsx(ET,{})}),C.jsx(Qe,{path:"/features",element:C.jsx(TT,{})}),C.jsx(Qe,{path:"/quote",element:C.jsx(kT,{})}),C.jsx(Qe,{path:"*",element:C.jsx(RT,{})})]})})}),C.jsx(pT,{})]})})}Pa.createRoot(document.getElementById("root")).render(C.jsx(de.StrictMode,{children:C.jsx(LT,{})}));export{Zr as B,Kg as D,zx as F,Ux as H,$g as I,pt as L,Kx as M,yt as P,de as R,Xx as S,Yx as T,Af as U,wm as X,Zx as Z,Fx as a,To as b,Ee as c,Yd as d,_n as e,rn as f,Wg as g,ze as h,Zg as i,C as j,MT as k,bE as l,aE as m,Yg as n,Yi as o,fn as p,Hx as q,v as r,Gx as s,xm as t,dT as u,$x as v,Wx as w,Er as x,Bx as y,ym as z};
