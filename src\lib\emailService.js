import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  serviceId: 'service_geostrat', // You'll get this from EmailJS
  templateId: 'template_contact', // You'll create this template
  publicKey: 'YOUR_PUBLIC_KEY', // You'll get this from EmailJS
  recipientEmail: '<EMAIL>' // Your email address
};

// Initialize EmailJS
export const initEmailJS = () => {
  emailjs.init(EMAILJS_CONFIG.publicKey);
};

// Send Contact Form Email
export const sendContactEmail = async (formData) => {
  try {
    const templateParams = {
      to_email: EMAILJS_CONFIG.recipientEmail,
      from_name: formData.name,
      from_email: formData.email,
      subject: formData.subject || 'New Contact Form Submission',
      message: formData.message,
      phone: formData.phone || 'Not provided',
      company: formData.company || 'Not provided',
      submission_date: new Date().toLocaleString(),
      website: 'GEOSTRATDRC Website'
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      EMAILJS_CONFIG.templateId,
      templateParams
    );

    console.log('Email sent successfully:', response);
    return { success: true, response };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
};

// Send Quote Request Email
export const sendQuoteEmail = async (quoteData) => {
  try {
    const templateParams = {
      to_email: EMAILJS_CONFIG.recipientEmail,
      from_name: quoteData.name,
      from_email: quoteData.email,
      subject: `Quote Request: ${quoteData.projectTitle}`,
      message: `
PROJECT DETAILS:
- Title: ${quoteData.projectTitle}
- Description: ${quoteData.projectDescription}
- Services: ${quoteData.services.join(', ')}
- Budget: ${quoteData.estimatedBudget}
- Timeline: ${quoteData.desiredTimeline}
- Additional Info: ${quoteData.additionalInfo || 'None'}

CONTACT DETAILS:
- Name: ${quoteData.name}
- Email: ${quoteData.email}
- Phone: ${quoteData.phone || 'Not provided'}
- Company: ${quoteData.company || 'Not provided'}
      `,
      phone: quoteData.phone || 'Not provided',
      company: quoteData.company || 'Not provided',
      services: quoteData.services.join(', '),
      project_title: quoteData.projectTitle,
      project_description: quoteData.projectDescription,
      budget: quoteData.estimatedBudget,
      timeline: quoteData.desiredTimeline,
      submission_date: new Date().toLocaleString(),
      website: 'GEOSTRATDRC Website'
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      'template_quote', // Separate template for quotes
      templateParams
    );

    console.log('Quote email sent successfully:', response);
    return { success: true, response };
  } catch (error) {
    console.error('Quote email sending failed:', error);
    return { success: false, error };
  }
};

// Send Newsletter Subscription Email
export const sendNewsletterEmail = async (email) => {
  try {
    const templateParams = {
      to_email: EMAILJS_CONFIG.recipientEmail,
      from_email: email,
      subject: 'New Newsletter Subscription',
      message: `New newsletter subscription from: ${email}`,
      submission_date: new Date().toLocaleString(),
      website: 'GEOSTRATDRC Website'
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      'template_newsletter', // Template for newsletter subscriptions
      templateParams
    );

    console.log('Newsletter email sent successfully:', response);
    return { success: true, response };
  } catch (error) {
    console.error('Newsletter email sending failed:', error);
    return { success: false, error };
  }
};

export default {
  initEmailJS,
  sendContactEmail,
  sendQuoteEmail,
  sendNewsletterEmail
};
