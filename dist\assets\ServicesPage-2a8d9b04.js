import{j as e,m as s,R as r,B as t,L as n,M as o}from"./index-6df975e7.js";import"./card-23440328.js";import{C as l}from"./chevron-right-99c22a31.js";import{L as m,S as c,B as d,a as p,G as g,C as u,D as h}from"./shield-alert-5902ebf5.js";import{A as x}from"./alert-triangle-baff16b9.js";const f=[{id:"climate-smart-agriculture",name:"Climate-Smart Agriculture",icon:e.jsx(m,{className:"h-10 w-10"}),description:"Enhancing agricultural resilience and productivity through advanced geospatial data analysis, precision farming techniques, and climate modeling. We help optimize resource use, improve crop yields, and promote sustainable farming practices in the face of climate change.",image:"climate_smart_agriculture_detailed.jpg",alt:"Drone flying over a lush farm field for data collection"},{id:"natural-risk-assessments",name:"Natural Risk Assessments",icon:e.jsx(c,{className:"h-10 w-10"}),description:"Identifying, analyzing, and mitigating natural hazard risks such as floods, landslides, and droughts. Our assessments provide crucial information for disaster preparedness, land-use planning, and infrastructure development to build safer, more resilient communities.",image:"natural_risk_assessment_map.jpg",alt:"Geospatial map highlighting areas of natural risk"},{id:"spatial-data-analytics",name:"Spatial Data Analytics",icon:e.jsx(d,{className:"h-10 w-10"}),description:"Transforming complex geographic data into actionable intelligence. We employ sophisticated analytical methods, machine learning, and AI to uncover patterns, trends, and insights that support strategic decision-making across various sectors.",image:"spatial_data_analytics_dashboard.jpg",alt:"Computer screen showing complex spatial data visualizations"},{id:"urban-planning",name:"Urban Planning",icon:e.jsx(p,{className:"h-10 w-10"}),description:"Supporting the development of sustainable, efficient, and livable urban environments. Our services include land suitability analysis, infrastructure planning, population growth modeling, and smart city solutions to guide urban development.",image:"urban_planning_blueprint.jpg",alt:"Digital blueprint of a modern city plan"},{id:"environmental-monitoring",name:"Environmental Monitoring",icon:e.jsx(g,{className:"h-10 w-10"}),description:"Utilizing satellite imagery, remote sensing, and GIS to track environmental changes, monitor deforestation, assess biodiversity, and manage protected areas. We provide data-driven insights for effective environmental conservation and policy-making.",image:"satellite_image_environmental_change.jpg",alt:"Satellite image showing changes in land cover over time"},{id:"natural-resources-management",name:"Natural Resources Management",icon:e.jsx(o,{className:"h-10 w-10"}),description:"Optimizing the sustainable use and conservation of natural resources like forests, minerals, and fisheries. Our solutions help balance economic development with environmental protection through detailed resource mapping and monitoring.",image:"natural_resources_map_layers.jpg",alt:"Layered map showing different natural resources in a region"},{id:"climate-change-adaptation",name:"Climate Change Adaptation",icon:e.jsx(u,{className:"h-10 w-10"}),description:"Developing and implementing strategies to help communities and ecosystems adapt to the impacts of climate change. This includes vulnerability assessments, adaptation planning, and monitoring the effectiveness of adaptation measures.",image:"climate_adaptation_strategy_infographic.jpg",alt:"Infographic illustrating climate change adaptation strategies"},{id:"water-resources-management",name:"Water Resources Management",icon:e.jsx(h,{className:"h-10 w-10"}),description:"Ensuring the sustainable management and equitable distribution of water resources. We offer services in watershed analysis, groundwater mapping, irrigation optimization, and flood management to address water scarcity and quality issues.",image:"water_resources_system_diagram.jpg",alt:"Diagram showing a comprehensive water resource management system"},{id:"disaster-risk-management",name:"Disaster Risk Management",icon:e.jsx(x,{className:"h-10 w-10"}),description:"Strengthening preparedness, response, and recovery capabilities for disaster events. Our services include early warning systems, damage assessment, evacuation planning, and post-disaster needs analysis using geospatial technologies.",image:"disaster_response_command_center.jpg",alt:"Emergency response team using maps in a command center"}],w=()=>e.jsxs("div",{className:"animate-fade-in font-sans",children:[e.jsxs("section",{className:"relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground",children:[e.jsx("div",{className:"absolute inset-0 bg-black/30"}),e.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[e.jsx(s.h1,{className:"text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:"Our Geospatial Services"}),e.jsx(s.p,{className:"text-lg md:text-xl max-w-3xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"Delivering comprehensive solutions to address complex environmental and developmental challenges."})]})]}),e.jsx("section",{className:"py-16 lg:py-24",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsx("div",{className:"space-y-16",children:f.map((a,i)=>e.jsxs(s.div,{id:a.id,className:"grid md:grid-cols-2 gap-8 md:gap-12 items-center",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:i*.1},viewport:{once:!0},children:[e.jsx("div",{className:`md:order-${i%2===0?1:2}`,children:e.jsx("img",{src:a.image,alt:a.alt,className:"rounded-lg shadow-xl w-full h-auto object-cover max-h-[400px]",src:"https://images.unsplash.com/photo-1599472696777-95cab5e0f891"})}),e.jsxs("div",{className:`md:order-${i%2===0?2:1}`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("span",{className:"p-3 rounded-full bg-primary/10 text-primary mr-4",children:r.cloneElement(a.icon,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-2xl lg:text-3xl font-jost font-semibold text-primary",children:a.name})]}),e.jsx("p",{className:"text-foreground/80 mb-6 leading-relaxed",children:a.description}),e.jsx(t,{asChild:!0,variant:"default",className:"font-jost",children:e.jsxs(n,{to:`/quote?service=${encodeURIComponent(a.name)}`,children:["Request Quote for ",a.name," ",e.jsx(l,{className:"ml-2 h-4 w-4"})]})})]})]},a.id))})})}),e.jsx("section",{className:"py-16 lg:py-24 bg-muted",children:e.jsxs("div",{className:"container mx-auto px-4 text-center",children:[e.jsx("h2",{className:"text-3xl lg:text-4xl font-jost font-bold text-primary mb-6",children:"Have a Specific Challenge?"}),e.jsx("p",{className:"text-lg text-foreground/80 max-w-xl mx-auto mb-8",children:"Our team is ready to discuss your unique requirements and develop a tailored geospatial solution."}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[e.jsx(t,{size:"lg",asChild:!0,className:"font-jost",children:e.jsx(n,{to:"/contact",children:"Contact Us"})}),e.jsx(t,{size:"lg",variant:"outline",asChild:!0,className:"font-jost border-primary text-primary hover:bg-primary/10",children:e.jsx(n,{to:"/projects",children:"See Our Work"})})]})]})})]});export{w as default};
