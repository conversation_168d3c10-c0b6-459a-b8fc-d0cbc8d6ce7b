import{j as t,m as e,B as s,L as a,H as o}from"./index-4f70b8a6.js";import{A as r}from"./alert-triangle-7ba801eb.js";const n=()=>t.jsxs("div",{className:"min-h-[calc(100vh-10rem)] flex flex-col items-center justify-center text-center px-4 font-sans bg-gradient-to-br from-background via-muted to-background",children:[t.jsxs(e.div,{initial:{opacity:0,scale:.8,y:-50},animate:{opacity:1,scale:1,y:0},transition:{duration:.6,type:"spring",stiffness:100},className:"p-8 md:p-12 rounded-xl shadow-2xl bg-background/80 backdrop-blur-md max-w-lg w-full",children:[t.jsx(r,{className:"h-24 w-24 text-destructive mx-auto mb-6"}),t.jsx("h1",{className:"text-6xl md:text-8xl font-jost font-extrabold text-primary mb-4",children:"404"}),t.jsx("h2",{className:"text-2xl md:text-3xl font-jost font-semibold text-foreground mb-3",children:"Page Not Found"}),t.jsx("p",{className:"text-md md:text-lg text-foreground/70 mb-8",children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or perhaps you mistyped the URL."}),t.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[t.jsx(s,{asChild:!0,size:"lg",className:"font-jost",children:t.jsxs(a,{to:"/",children:[t.jsx(o,{className:"mr-2 h-5 w-5"})," Go to Homepage"]})}),t.jsx(s,{asChild:!0,variant:"outline",size:"lg",className:"font-jost border-primary text-primary hover:bg-primary/10",children:t.jsx(a,{to:"/contact",children:"Contact Support"})})]})]}),t.jsx(e.div,{className:"mt-12",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5,duration:.5},children:t.jsx("img",{src:"lost_explorer_illustration.svg",alt:"Illustration of a lost explorer looking at a map",className:"h-48 md:h-64 opacity-70",src:"https://images.unsplash.com/photo-1689773132527-bcabdc88a395"})})]});export{n as default};
