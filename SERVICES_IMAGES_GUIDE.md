# GEOSTRATDRC Services Images Setup Guide

## 📁 Image Directory Structure

Your custom service images should be placed in:
```
public/services/
```

## 🖼️ Required Image Files

You need to add **9 custom images** with these exact filenames:

### 1. **climate-smart-agriculture.jpg**
- **Service:** Climate-Smart Agriculture
- **Suggested content:** Your team's agricultural projects, drone surveys, crop monitoring, smart farming technology
- **Examples:** Field work photos, agricultural data collection, precision farming

### 2. **natural-risk-assessments.jpg**
- **Service:** Natural Risk Assessments
- **Suggested content:** Risk mapping projects, hazard assessment work, geological surveys
- **Examples:** Risk maps, field assessment work, geological data collection

### 3. **spatial-data-analytics.jpg**
- **Service:** Spatial Data Analytics
- **Suggested content:** Your GIS workstations, data analysis screens, mapping software in use
- **Examples:** Team working with GIS software, data visualization screens, analytical workflows

### 4. **urban-planning.jpg**
- **Service:** Urban Planning
- **Suggested content:** Urban development projects, city planning work, infrastructure mapping
- **Examples:** Urban maps, planning meetings, city development projects

### 5. **environmental-monitoring.jpg**
- **Service:** Environmental Monitoring
- **Suggested content:** Environmental monitoring equipment, satellite data analysis, field monitoring
- **Examples:** Environmental sensors, monitoring stations, satellite imagery analysis

### 6. **natural-resources-management.jpg**
- **Service:** Natural Resources Management
- **Suggested content:** Natural resource mapping, conservation projects, resource assessment
- **Examples:** Forest mapping, resource inventory, conservation area planning

### 7. **climate-change-adaptation.jpg**
- **Service:** Climate Change Adaptation
- **Suggested content:** Climate adaptation projects, resilience planning, climate data analysis
- **Examples:** Climate modeling, adaptation planning sessions, resilience projects

### 8. **water-resources-management.jpg**
- **Service:** Water Resources Management
- **Suggested content:** Water resource mapping, watershed analysis, water system planning
- **Examples:** Water body mapping, hydrological analysis, water infrastructure planning

### 9. **disaster-risk-management.jpg**
- **Service:** Disaster Risk Management
- **Suggested content:** Disaster response planning, emergency mapping, risk management systems
- **Examples:** Emergency response maps, disaster planning meetings, risk assessment tools

## 📐 Image Specifications

### **Recommended Dimensions:**
- **Width:** 800-1200 pixels
- **Height:** 600-900 pixels
- **Aspect Ratio:** 4:3 or 16:10 (landscape orientation)

### **File Requirements:**
- **Format:** JPG or PNG
- **File Size:** Under 2MB each for fast loading
- **Quality:** High resolution but web-optimized

### **Content Guidelines:**
- **Professional appearance** - High-quality, well-lit photos
- **GEOSTRATDRC branding** - Include your logo if possible
- **Relevant content** - Directly related to each service
- **Team in action** - Show your experts at work
- **Real projects** - Use actual project photos when possible

## 🚀 How to Add Your Images

### **Step 1: Prepare Your Images**
1. **Collect** photos from your actual projects
2. **Resize** to recommended dimensions
3. **Optimize** file sizes for web
4. **Rename** files to match the exact names above

### **Step 2: Upload to Website**
1. **Copy** all 9 images to: `public/services/`
2. **Ensure** filenames match exactly (case-sensitive)
3. **Test** that images load correctly

### **Step 3: Verify Setup**
1. **Navigate** to `/services` page
2. **Check** that each service shows your custom image
3. **Verify** images load quickly and look professional

## 🔄 Fallback System

If an image fails to load, the website will:
1. **Show** a placeholder or default image
2. **Log** an error in browser console
3. **Continue** functioning normally

## 📱 Responsive Design

Your images will automatically:
- **Scale** to fit different screen sizes
- **Maintain** aspect ratio
- **Load** optimized versions on mobile

## ✅ Quality Checklist

Before uploading, ensure each image:
- [ ] **High quality** and professional appearance
- [ ] **Relevant** to the specific service
- [ ] **Proper dimensions** (800x600 minimum)
- [ ] **Optimized file size** (under 2MB)
- [ ] **Correct filename** (exact match required)
- [ ] **Shows GEOSTRATDRC work** when possible

## 🎯 Benefits of Custom Images

Using your own images will:
- ✅ **Showcase** actual GEOSTRATDRC projects
- ✅ **Build credibility** with real work examples
- ✅ **Improve branding** with consistent visual identity
- ✅ **Engage visitors** with authentic content
- ✅ **Differentiate** from competitors using stock photos

## 📞 Need Help?

If you need assistance with:
- **Image optimization**
- **File naming**
- **Upload process**
- **Technical issues**

Just let me know and I'll help you get your custom service images set up perfectly!

---

**Next Steps:**
1. Gather your 9 service images
2. Rename them according to this guide
3. Place them in `public/services/` folder
4. Test the services page to see your custom images in action!
