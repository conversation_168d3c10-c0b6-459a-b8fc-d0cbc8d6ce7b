
import React from 'react';
import { motion } from 'framer-motion';
import { Linkedin, Twitter, Mail, UserCheck, Award, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'; // Assuming avatar is created

const teamMembersList = [
  { id: 'jean-du<PERSON>', name: 'Dr. <PERSON>', role: 'Founder & CEO', image: 'team_member_1_large.jpg', alt: 'Dr. <PERSON>, Founder & CEO', expertise: ['Geospatial Strategy', 'Climate Adaptation', 'Sustainable Development'], bio: 'Dr<PERSON> is a visionary leader with over 20 years of experience in applying geospatial technologies to solve complex environmental and developmental challenges in Central Africa. He holds a Ph.D. in Remote Sensing and GIS.', social: { linkedin: '#', twitter: '#', email: 'jean.du<PERSON>@geostratdrc.com' } },
  { id: 'aisha-b<PERSON><PERSON>', name: '<PERSON><PERSON>', role: 'Lead GIS Analyst', image: 'team_member_2_large.jpg', alt: '<PERSON>sha Bakari, Lead GIS Analyst', expertise: ['Spatial Data Analysis', 'GIS Modeling', 'Database Management'], bio: 'Aisha is a highly skilled GIS professional with a Master\'s degree in Geoinformatics. She excels in transforming raw data into actionable insights and developing sophisticated spatial models.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'kwame-nkosi', name: 'Kwame Nkosi', role: 'Head of Climate Solutions', image: 'team_member_3_large.jpg', alt: 'Kwame Nkosi, Head of Climate Solutions', expertise: ['Climate Change Impacts', 'Renewable Energy Siting', 'Risk Assessment'], bio: 'Kwame leads our climate solutions division, focusing on developing innovative strategies for climate change adaptation and mitigation. He has extensive field experience across the Congo Basin.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'fatima-zahra', name: 'Fatima Zahra', role: 'Urban Planning Specialist', image: 'team_member_4_large.jpg', alt: 'Fatima Zahra, Urban Planning Specialist', expertise: ['Smart Cities', 'Sustainable Urban Development', 'Infrastructure Planning'], bio: 'Fatima is an expert in urban and regional planning, dedicated to creating resilient and sustainable urban environments. Her work integrates geospatial data with urban design principles.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'david-okoro', name: 'David Okoro', role: 'Natural Resources Manager', image: 'team_member_5_large.jpg', alt: 'David Okoro, Natural Resources Manager', expertise: ['Forestry Management', 'Biodiversity Conservation', 'Resource Mapping'], bio: 'David specializes in the sustainable management of natural resources. He uses geospatial tools to monitor ecosystems, assess resource availability, and support conservation initiatives.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'sophie-mbiya', name: 'Sophie Mbiya', role: 'Project Manager', image: 'team_member_6_large.jpg', alt: 'Sophie Mbiya, Project Manager', expertise: ['Agile Project Management', 'Client Relations', 'Quality Assurance'], bio: 'Sophie ensures our projects are delivered on time, within budget, and to the highest quality standards. She is a certified Project Management Professional (PMP).', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
];

const TeamPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Meet Our Expert Team
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            The driving force behind GEOSTRATDRC's success: a collective of passionate, skilled, and dedicated professionals.
          </motion.p>
        </div>
      </section>

      {/* Team Introduction Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <Users className="h-16 w-16 text-primary mx-auto mb-4" />
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Collective Expertise</h2>
            <p className="text-lg text-foreground/80">
              At GEOSTRATDRC, we believe that our strength lies in our people. Our diverse team brings together a wealth of experience from various geospatial disciplines, united by a common goal: to deliver innovative solutions that make a real-world impact. We foster a culture of collaboration, continuous learning, and excellence.
            </p>
          </div>

          {/* Team Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembersList.map((member, index) => (
              <motion.div
                key={member.id}
                id={member.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full flex flex-col text-center hover:shadow-xl transition-shadow duration-300">
                  <CardHeader className="items-center pt-6">
                    <Avatar className="w-32 h-32 mb-4 border-4 border-primary/20">
                      <AvatarImage src={member.image} alt={member.alt} />
                      <AvatarFallback className="font-jost text-4xl bg-muted">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <CardTitle className="font-jost text-2xl text-primary">{member.name}</CardTitle>
                    <CardDescription className="text-secondary font-semibold">{member.role}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow flex flex-col justify-between">
                    <div>
                      <p className="text-sm text-foreground/70 mb-4 px-2">{member.bio.substring(0,100)}...</p>
                      <div className="mb-4">
                        <h4 className="font-jost text-sm font-semibold text-foreground mb-1">Key Expertise:</h4>
                        <div className="flex flex-wrap justify-center gap-1">
                          {member.expertise.map(skill => (
                            <span key={skill} className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">{skill}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="mt-auto">
                      <div className="flex justify-center space-x-3 mb-4">
                        {member.social.linkedin && <a href={member.social.linkedin} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary"><Linkedin size={20} /></a>}
                        {member.social.twitter && <a href={member.social.twitter} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary"><Twitter size={20} /></a>}
                        {member.social.email && <a href={`mailto:${member.social.email}`} className="text-muted-foreground hover:text-primary"><Mail size={20} /></a>}
                      </div>
                       {/* <Button variant="outline" size="sm" className="font-jost w-full">View Full Profile</Button> */}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Our Team Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4 text-center">
          <Award className="h-16 w-16 text-primary mx-auto mb-4" />
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Interested in Joining Our Mission?
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            We are always looking for talented and passionate individuals to join our growing team. If you share our vision for a sustainable future empowered by geospatial technology, we'd love to hear from you.
          </p>
          <Button size="lg" variant="default" className="font-jost">
            View Current Openings (Coming Soon)
          </Button>
        </div>
      </section>
    </div>
  );
};

export default TeamPage;
