
import React from 'react';
import { motion } from 'framer-motion';
import { Linkedin, Twitter, Mail, UserCheck, Award, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'; // Assuming avatar is created

const teamMembersList = [
  { id: 'padona-matabaro-jason', name: 'PADON<PERSON> MATABARO Jason', role: 'CEO and Senior Geospatial Data Analyst', image: '/team/padona-matabaro-jason.jpg', alt: 'PADONA MATABARO Jason, CEO and Senior Geospatial Data Analyst', expertise: ['Leadership', 'Strategic Management', 'Advanced Geospatial Data Analysis'], bio: '<PERSON> is a visionary leader with expertise in strategic management and advanced geospatial data analysis. He leads GEOSTRATDRC with a focus on innovative solutions for complex environmental and developmental challenges.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'amani-ines-maguru', name: '<PERSON><PERSON>ru', role: 'Research Assistant', image: '/team/amani-ines-maguru.jpg', alt: 'Amani Inès Maguru, Research Assistant', expertise: ['Research Support', 'Data Collection', 'Preliminary Analysis'], bio: 'Amani provides essential research support to our team, specializing in data collection and preliminary analysis. Her attention to detail and analytical skills contribute significantly to our research initiatives.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'ushindi-gabriel', name: 'Ushindi Gabriel', role: 'Chief Technology Officer', image: '/team/ushindi-gabriel.jpg', alt: 'Ushindi Gabriel, Chief Technology Officer', expertise: ['Technology Strategy', 'Systems Development', 'IT Infrastructure'], bio: 'Gabriel leads our technology strategy and oversees systems development and IT infrastructure. His expertise ensures that GEOSTRATDRC stays at the forefront of technological innovation in geospatial solutions.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'ashuza-mpuranyi-judith', name: 'Ashuza Mpuranyi Judith', role: 'GIS Specialist', image: '/team/ashuza-mpuranyi-judith.jpg', alt: 'Ashuza Mpuranyi Judith, GIS Specialist', expertise: ['Geographic Information Systems', 'Spatial Data Management', 'Mapping Solutions'], bio: 'Judith is our expert in Geographic Information Systems, specializing in spatial data management and mapping solutions. Her technical skills in GIS enable us to deliver precise and comprehensive spatial analysis.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'jordan-birali', name: 'Jordan Birali', role: 'Architect', image: '/team/jordan-birali.jpg', alt: 'Jordan Birali, Architect', expertise: ['Architectural Design', 'Spatial Planning', 'Built Environment Integration'], bio: 'Jordan brings architectural expertise to our team, focusing on architectural design, spatial planning, and built environment integration. His work bridges the gap between geospatial data and practical architectural applications.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'mushagalusa-namegabe-alain', name: 'Mushagalusa Namegabe Alain', role: 'Environmental Data Scientist', image: '/team/mushagalusa-namegabe-alain.jpg', alt: 'Mushagalusa Namegabe Alain, Environmental Data Scientist', expertise: ['Environmental Data Analysis', 'Sustainability Metrics', 'Eco-informatics'], bio: 'Alain specializes in environmental data analysis and sustainability metrics. His expertise in eco-informatics helps us develop solutions that promote environmental sustainability and conservation.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'cubaka-bahozi-rigobert', name: 'Cubaka Bahozi Rigobert', role: 'Remote Sensing Analyst', image: '/team/cubaka-bahozi-rigobert.jpg', alt: 'Cubaka Bahozi Rigobert, Remote Sensing Analyst', expertise: ['Satellite Imagery Analysis', 'Land Use/Land Cover Mapping', 'Remote Sensing Technologies'], bio: 'Rigobert is our expert in remote sensing technologies, specializing in satellite imagery analysis and land use/land cover mapping. His skills enable us to monitor and analyze environmental changes from space.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'gaelle-baraka', name: 'Gaelle Baraka', role: 'Human Resource Manager', image: '/team/gaelle-baraka.jpg', alt: 'Gaelle Baraka, Human Resource Manager', expertise: ['Human Resources Management', 'Team Development', 'Organizational Leadership'], bio: 'Gaelle leads our human resources initiatives, focusing on talent acquisition, employee development, and organizational culture. Her expertise ensures that GEOSTRATDRC attracts and retains top talent while fostering a positive work environment.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
  { id: 'moise-raphael', name: 'Moise Raphael', role: 'Senior Risk Assessment Expert', image: '/team/moise-raphael.jpg', alt: 'Moise Raphael, Senior Risk Assessment Expert', expertise: ['Risk Analysis', 'Environmental Impact Assessments', 'Disaster Risk Management'], bio: 'Moise is our senior expert in risk assessment, specializing in risk analysis, environmental impact assessments, and disaster risk management. His expertise helps organizations prepare for and mitigate environmental risks.', social: { linkedin: '#', twitter: '#', email: '<EMAIL>' } },
];

const TeamPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Meet Our Expert Team
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            The driving force behind GEOSTRATDRC's success: a collective of passionate, skilled, and dedicated professionals.
          </motion.p>
        </div>
      </section>

      {/* Team Introduction Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <Users className="h-16 w-16 text-primary mx-auto mb-4" />
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Collective Expertise</h2>
            <p className="text-lg text-foreground/80">
              At GEOSTRATDRC, we believe that our strength lies in our people. Our diverse team brings together a wealth of experience from various geospatial disciplines, united by a common goal: to deliver innovative solutions that make a real-world impact. We foster a culture of collaboration, continuous learning, and excellence.
            </p>
          </div>

          {/* Team Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembersList.map((member, index) => (
              <motion.div
                key={member.id}
                id={member.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full flex flex-col text-center hover:shadow-xl transition-shadow duration-300">
                  <CardHeader className="items-center pt-6">
                    <Avatar className="w-32 h-32 mb-4 border-4 border-primary/20">
                      <AvatarImage
                        src={member.image}
                        alt={member.alt}
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                      <AvatarFallback className="font-jost text-4xl bg-gradient-to-br from-primary/20 to-secondary/20 text-primary">
                        {member.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <CardTitle className="font-jost text-2xl text-primary">{member.name}</CardTitle>
                    <CardDescription className="text-secondary font-semibold">{member.role}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow flex flex-col justify-between">
                    <div>
                      <p className="text-sm text-foreground/70 mb-4 px-2">{member.bio.substring(0,100)}...</p>
                      <div className="mb-4">
                        <h4 className="font-jost text-sm font-semibold text-foreground mb-1">Key Expertise:</h4>
                        <div className="flex flex-wrap justify-center gap-1">
                          {member.expertise.map(skill => (
                            <span key={skill} className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">{skill}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="mt-auto">
                      <div className="flex justify-center space-x-3 mb-4">
                        {member.social.linkedin && <a href={member.social.linkedin} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary"><Linkedin size={20} /></a>}
                        {member.social.twitter && <a href={member.social.twitter} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary"><Twitter size={20} /></a>}
                        {member.social.email && <a href={`mailto:${member.social.email}`} className="text-muted-foreground hover:text-primary"><Mail size={20} /></a>}
                      </div>
                       {/* <Button variant="outline" size="sm" className="font-jost w-full">View Full Profile</Button> */}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Our Team Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4 text-center">
          <Award className="h-16 w-16 text-primary mx-auto mb-4" />
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Interested in Joining Our Mission?
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            We are always looking for talented and passionate individuals to join our growing team. If you share our vision for a sustainable future empowered by geospatial technology, we'd love to hear from you.
          </p>
          <Button size="lg" variant="default" className="font-jost">
            View Current Openings (Coming Soon)
          </Button>
        </div>
      </section>
    </div>
  );
};

export default TeamPage;
