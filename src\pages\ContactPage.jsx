
import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Phone, Mail, MapPin, Send, Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { initEmailJS, sendContactEmail } from '@/lib/emailService';

const ContactPage = () => {
  const { toast } = useToast();

  // Initialize EmailJS when component mounts
  useEffect(() => {
    initEmailJS();
  }, []);

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    console.log("Contact Form Data:", data);

    try {
      // Save to localStorage (backup)
      const existingMessages = JSON.parse(localStorage.getItem('contactMessages')) || [];
      localStorage.setItem('contactMessages', JSON.stringify([...existingMessages, { ...data, date: new Date().toISOString() }]));

      // Send email notification
      const emailResult = await sendContactEmail(data);

      if (emailResult.success) {
        toast({
          title: "Message Sent Successfully!",
          description: "Thank you for contacting us. We'll be in touch <NAME_EMAIL>.",
          variant: "default",
        });
        e.target.reset();
      } else {
        throw new Error('Email sending failed');
      }
    } catch (error) {
      console.error("Failed to send contact message:", error);
      toast({
        title: "Message Saved Locally",
        description: "Your message was saved but email notification failed. We'll still review your message.",
        variant: "default",
      });
      e.target.reset();
    }
  };

  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Get in Touch
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            We're here to answer your questions and explore how our geospatial solutions can help you.
          </motion.p>
        </div>
      </section>

      {/* Contact Form and Info Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="p-6 sm:p-8 shadow-xl bg-background">
                <CardHeader className="p-0 mb-6">
                  <CardTitle className="font-jost text-2xl text-primary flex items-center">
                    <Send className="h-6 w-6 mr-2" /> Send Us a Message
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <form onSubmit={handleContactSubmit} className="space-y-6">
                    <div className="grid sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="contact-name">Full Name</Label>
                        <Input id="contact-name" name="name" type="text" placeholder="Your Name" required className="mt-1" />
                      </div>
                      <div>
                        <Label htmlFor="contact-email">Email Address</Label>
                        <Input id="contact-email" name="email" type="email" placeholder="<EMAIL>" required className="mt-1" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="contact-subject">Subject</Label>
                      <Input id="contact-subject" name="subject" type="text" placeholder="Inquiry about..." required className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="contact-message">Message</Label>
                      <Textarea id="contact-message" name="message" placeholder="Your message here..." required rows={5} className="mt-1" />
                    </div>
                    <Button type="submit" size="lg" className="w-full font-jost">
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl font-jost font-semibold text-primary mb-4">Contact Information</h2>
                <div className="space-y-4 text-foreground/80">
                  <div className="flex items-start">
                    <MapPin className="h-6 w-6 mr-3 mt-1 text-primary flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-foreground">GEOSTRATDRC Headquarters</p>
                      <p>Bukavu/Ibanda/Quartier Nyalukemba/avenue du lac</p>
                      <p>Bukavu, Democratic Republic of Congo</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-5 w-5 mr-3 text-primary" />
                    <a href="tel:+243973583690" className="hover:text-primary">+243 973 583 690</a>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-primary" />
                    <a href="mailto:<EMAIL>" className="hover:text-primary"><EMAIL></a>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-jost font-semibold text-primary mb-3">Business Hours</h3>
                <p className="text-foreground/80">Monday - Friday: 9:00 AM - 5:00 PM (WAT)</p>
                <p className="text-foreground/80">Saturday - Sunday: Closed</p>
              </div>
              
              <div>
                <h3 className="text-xl font-jost font-semibold text-primary mb-3">Connect With Us</h3>
                <div className="flex space-x-4">
                  <Button variant="outline" size="icon" asChild className="text-primary border-primary hover:bg-primary/10">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Facebook"><Facebook /></a>
                  </Button>
                  <Button variant="outline" size="icon" asChild className="text-primary border-primary hover:bg-primary/10">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Twitter"><Twitter /></a>
                  </Button>
                  <Button variant="outline" size="icon" asChild className="text-primary border-primary hover:bg-primary/10">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn"><Linkedin /></a>
                  </Button>
                  <Button variant="outline" size="icon" asChild className="text-primary border-primary hover:bg-primary/10">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Instagram"><Instagram /></a>
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="h-[400px] md:h-[500px] bg-muted">
        {/* OpenStreetMap Embed Placeholder */}
        {/* In a real app, you'd use a library like Leaflet or React-Leaflet for OpenStreetMap */}
        <div className="w-full h-full flex items-center justify-center text-foreground/50">
          <div className="text-center p-4">
            <MapPin size={48} className="mx-auto mb-2" />
            <p className="font-jost text-xl">Our Office Location</p>
            <p className="text-sm">(Interactive Map Will Be Displayed Here)</p>
            <p className="text-xs mt-2">Using OpenStreetMap</p>
          </div>
        </div>
        {/* Example of an iframe for OpenStreetMap - adjust coordinates and zoom */}
        {/* <iframe 
          width="100%" 
          height="100%" 
          frameBorder="0" 
          scrolling="no" 
          marginHeight="0" 
          marginWidth="0" 
          src="https://www.openstreetmap.org/export/embed.html?bbox=15.25%2C-4.35%2C15.35%2C-4.25&layer=mapnik&marker=-4.3000%2C15.3000"
          title="GEOSTRATDRC Office Location"
          style={{ border: 0 }}
          allowFullScreen
        ></iframe> */}
      </section>
    </div>
  );
};

export default ContactPage;
