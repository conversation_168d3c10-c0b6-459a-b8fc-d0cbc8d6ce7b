
import React, { useState } from 'react';
import { Link, NavLink } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Menu, X, Briefcase, Users, Home, Layers, MessageSquare, FileText, Star, Zap, Phone } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const navLinks = [
  { name: 'Home', path: '/', icon: Home },
  { name: 'About', path: '/about', icon: Users },
  { name: 'Services', path: '/services', icon: Layers },
  { name: 'Projects', path: '/projects', icon: Briefcase },
  { name: 'Team', path: '/team', icon: Users },
  { name: 'Blog', path: '/blog', icon: FileText },
  { name: 'Testimonials', path: '/testimonials', icon: Star },
  { name: 'Features', path: '/features', icon: Zap },
  { name: 'Contact', path: '/contact', icon: Phone },
];

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => setIsOpen(!isOpen);

  const activeLinkClass = "text-primary font-semibold border-b-2 border-primary";
  const inactiveLinkClass = "text-foreground hover:text-primary transition-colors duration-300";

  return (
    <nav className="bg-background/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          <Link to="/" className="flex items-center">
            <img  className="h-10 w-auto mr-2" alt="GEOSTRATDRC Logo" src="https://images.unsplash.com/photo-1689773132527-bcabdc88a395" />
            <span className="font-jost text-2xl font-bold text-primary">GEOSTRAT<span className="text-secondary">DRC</span></span>
          </Link>

          <div className="hidden md:flex items-center space-x-6">
            {navLinks.slice(0, 5).map((link) => ( // Show first 5 links directly
              <NavLink
                key={link.name}
                to={link.path}
                className={({ isActive }) => 
                  `${isActive ? activeLinkClass : inactiveLinkClass} font-medium text-sm`
                }
              >
                {link.name}
              </NavLink>
            ))}
            {/* Add a "More" dropdown if needed for other links, or list them all if space permits */}
          </div>
          
          <div className="hidden md:flex items-center">
            <Button asChild variant="default" className="font-jost">
              <Link to="/quote">Get a Quote</Link>
            </Button>
          </div>

          <div className="md:hidden flex items-center">
            <Button variant="ghost" onClick={toggleMenu} aria-label="Toggle menu">
              {isOpen ? <X className="h-6 w-6 text-primary" /> : <Menu className="h-6 w-6 text-primary" />}
            </Button>
          </div>
        </div>
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="md:hidden bg-background shadow-lg absolute w-full"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center">
              {navLinks.map((link) => (
                <NavLink
                  key={link.name}
                  to={link.path}
                  onClick={toggleMenu}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium w-full text-center ${isActive ? 'bg-primary/10 text-primary' : 'text-foreground hover:bg-gray-100'}`
                  }
                >
                  <div className="flex items-center justify-center">
                    <link.icon className="h-5 w-5 mr-2" />
                    {link.name}
                  </div>
                </NavLink>
              ))}
              <Button asChild variant="default" className="w-full mt-4 font-jost" onClick={toggleMenu}>
                <Link to="/quote">Get a Quote</Link>
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;
