
import React from 'react';
import { motion } from 'framer-motion';
import { Users, Target, Award, CheckCircle, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const teamMembers = [
  { name: 'Dr. <PERSON>', role: 'Founder & CEO', image: 'founder_ceo_profile.jpg', alt: 'Dr. <PERSON>, Founder & CEO', bio: 'Visionary leader with 20+ years in geospatial tech.' },
  { name: '<PERSON><PERSON>', role: 'Lead GIS Analyst', image: 'lead_gis_analyst_profile.jpg', alt: '<PERSON><PERSON>, Lead GIS Analyst', bio: 'Expert in spatial data analysis and modeling.' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Head of Climate Solutions', image: 'climate_solutions_head_profile.jpg', alt: '<PERSON><PERSON><PERSON>, Head of Climate Solutions', bio: 'Passionate about sustainable climate adaptation.' },
];

const coreValues = [
  { title: 'Innovation', icon: <Target className="h-8 w-8 text-primary" />, description: 'Continuously seeking novel solutions to complex challenges.' },
  { title: 'Integrity', icon: <CheckCircle className="h-8 w-8 text-primary" />, description: 'Upholding the highest ethical standards in all our endeavors.' },
  { title: 'Impact', icon: <Eye className="h-8 w-8 text-primary" />, description: 'Dedicated to making a tangible, positive difference in the communities we serve.' },
  { title: 'Collaboration', icon: <Users className="h-8 w-8 text-primary" />, description: 'Working closely with partners and clients to achieve shared goals.' },
];

const AboutPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            About GEOSTRATDRC
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Pioneering Geospatial Innovation for a Sustainable Central Africa.
          </motion.p>
        </div>
      </section>

      {/* Company History and Mission */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">Our Journey & Vision</h2>
              <p className="text-lg text-foreground/90 mb-4">
                Founded with a passion for leveraging technology for societal good, GEOSTRATDRC has rapidly grown into a leading geospatial solutions provider in the Democratic Republic of Congo and Central Africa.
              </p>
              <p className="text-foreground/80 mb-4">
                Our mission is to empower organizations and communities with cutting-edge geospatial intelligence, fostering sustainable development, environmental stewardship, and resilience against climate change and natural hazards.
              </p>
              <p className="text-foreground/80">
                We envision a future where data-driven decisions lead to a more prosperous, equitable, and environmentally sound Central Africa.
              </p>
            </motion.div>
            <motion.div
              className="relative group"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <img  
                src="company_timeline_infographic.jpg" 
                alt="Infographic showing company milestones and growth" 
                className="rounded-lg shadow-xl w-full h-auto object-cover"
               src="https://images.unsplash.com/photo-1691405167344-c3bbc9710ad2" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Core Values</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              These principles guide every aspect of our work and define who we are as a company.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300">
                  <div className="flex justify-center mb-4">{value.icon}</div>
                  <h3 className="text-xl font-jost font-semibold mb-2 text-primary">{value.title}</h3>
                  <p className="text-foreground/80 text-sm">{value.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Profiles Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Meet Our Leadership</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              Our dedicated team of experts brings a wealth of experience and passion to every project.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center overflow-hidden group">
                  <img  
                    src={member.image} 
                    alt={member.alt} 
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                   src="https://images.unsplash.com/photo-1599472696777-95cab5e0f891" />
                  <CardContent className="p-6">
                    <h3 className="text-xl font-jost font-semibold text-primary mb-1">{member.name}</h3>
                    <p className="text-secondary font-medium mb-2">{member.role}</p>
                    <p className="text-sm text-foreground/70 mb-4">{member.bio}</p>
                    <Button variant="link" asChild className="text-primary font-semibold">
                      <Link to={`/team#${member.name.toLowerCase().replace(/\s+/g, '-')}`}>View Full Profile</Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" asChild className="font-jost">
              <Link to="/team">See Our Entire Team</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-r from-primary/10 via-teal-500/10 to-secondary/10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Why Partner with GEOSTRATDRC?</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              We offer unparalleled expertise, local knowledge, and a commitment to delivering impactful results.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { title: 'Local Expertise, Global Standards', description: 'Deep understanding of the Central African context combined with international best practices.', icon: <Award className="h-10 w-10 text-primary" /> },
              { title: 'Cutting-Edge Technology', description: 'Utilizing the latest geospatial tools and AI for superior data analysis and insights.', icon: <Target className="h-10 w-10 text-primary" /> },
              { title: 'Client-Centric Approach', description: 'Tailored solutions and dedicated support to ensure your project objectives are met.', icon: <Users className="h-10 w-10 text-primary" /> },
            ].map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.15 }}
                viewport={{ once: true }}
              >
                <Card className="p-6 text-center h-full">
                  <div className="flex justify-center mb-4">{item.icon}</div>
                  <h3 className="text-xl font-jost font-semibold text-primary mb-2">{item.title}</h3>
                  <p className="text-foreground/80">{item.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Let's Build a Sustainable Future Together
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            Contact us today to learn how GEOSTRATDRC can support your vision.
          </p>
          <Button size="lg" asChild className="font-jost">
            <Link to="/contact">Get in Touch</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
