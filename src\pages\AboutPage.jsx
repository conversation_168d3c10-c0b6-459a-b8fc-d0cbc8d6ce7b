
import React from 'react';
import { motion } from 'framer-motion';
import { Users, Target, Award, CheckCircle, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const teamMembers = [
  { name: 'PADONA MATABARO Jason', role: 'CEO and Senior Geospatial Data Analyst', image: '/team/padona-matabaro-jason.jpg', alt: 'PADON<PERSON> MATABARO Jason, CEO and Senior Geospatial Data Analyst', bio: 'Visionary leader with expertise in strategic management and advanced geospatial data analysis.' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Chief Technology Officer', image: '/team/ushindi-gabriel.jpg', alt: '<PERSON><PERSON><PERSON>, Chief Technology Officer', bio: 'Technology strategist overseeing systems development and IT infrastructure.' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Human Resource Manager', image: '/team/gaelle-baraka.jpg', alt: '<PERSON><PERSON><PERSON>, Human Resource Manager', bio: 'Leading human resources initiatives and organizational development.' },
];

const coreValues = [
  { title: 'Innovation', icon: <Target className="h-8 w-8 text-primary" />, description: 'Continuously seeking novel solutions to complex challenges.' },
  { title: 'Integrity', icon: <CheckCircle className="h-8 w-8 text-primary" />, description: 'Upholding the highest ethical standards in all our endeavors.' },
  { title: 'Impact', icon: <Eye className="h-8 w-8 text-primary" />, description: 'Dedicated to making a tangible, positive difference in the communities we serve.' },
  { title: 'Collaboration', icon: <Users className="h-8 w-8 text-primary" />, description: 'Working closely with partners and clients to achieve shared goals.' },
];

const AboutPage = () => {
  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            About GEOSTRATDRC
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Pioneering Geospatial Innovation for a Sustainable Central Africa.
          </motion.p>
        </div>
      </section>

      {/* Company History and Mission */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">Our Journey & Vision</h2>
              <p className="text-lg text-foreground/90 mb-4">
                Founded with a passion for leveraging technology for societal good, GEOSTRATDRC has rapidly grown into a leading geospatial solutions provider in the Democratic Republic of Congo and Central Africa.
              </p>
              <p className="text-foreground/80 mb-4">
                Our mission is to empower organizations and communities with cutting-edge geospatial intelligence, fostering sustainable development, environmental stewardship, and resilience against climate change and natural hazards.
              </p>
              <p className="text-foreground/80">
                We envision a future where data-driven decisions lead to a more prosperous, equitable, and environmentally sound Central Africa.
              </p>
            </motion.div>
            <motion.div
              className="relative group"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <img  
                src="company_timeline_infographic.jpg" 
                alt="Infographic showing company milestones and growth" 
                className="rounded-lg shadow-xl w-full h-auto object-cover"
               src="https://images.unsplash.com/photo-1691405167344-c3bbc9710ad2" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Core Values</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              These principles guide every aspect of our work and define who we are as a company.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300">
                  <div className="flex justify-center mb-4">{value.icon}</div>
                  <h3 className="text-xl font-jost font-semibold mb-2 text-primary">{value.title}</h3>
                  <p className="text-foreground/80 text-sm">{value.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission, Vision & Objectives Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-primary/5 via-teal-500/5 to-secondary/5">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Purpose & Direction</h2>
            <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
              Driving sustainable development through innovative geospatial solutions in Central Africa and beyond.
            </p>
          </div>

          {/* Mission Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <Card className="p-8 lg:p-12 bg-gradient-to-r from-primary/10 to-teal-600/10 border-primary/20">
              <div className="flex flex-col lg:flex-row items-center gap-8">
                <div className="flex-shrink-0">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                    <Target className="h-10 w-10 text-primary-foreground" />
                  </div>
                </div>
                <div className="text-center lg:text-left">
                  <h3 className="text-2xl lg:text-3xl font-jost font-bold text-primary mb-4">Our Mission</h3>
                  <p className="text-lg text-foreground/80 leading-relaxed">
                    To empower communities, organizations, and governments across Central Africa with cutting-edge geospatial
                    technologies and data-driven insights that promote sustainable development, environmental conservation,
                    and informed decision-making for a resilient future.
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Vision Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <Card className="p-8 lg:p-12 bg-gradient-to-r from-secondary/10 to-teal-500/10 border-secondary/20">
              <div className="flex flex-col lg:flex-row-reverse items-center gap-8">
                <div className="flex-shrink-0">
                  <div className="w-20 h-20 bg-secondary rounded-full flex items-center justify-center">
                    <Globe className="h-10 w-10 text-primary-foreground" />
                  </div>
                </div>
                <div className="text-center lg:text-left">
                  <h3 className="text-2xl lg:text-3xl font-jost font-bold text-primary mb-4">Our Vision</h3>
                  <p className="text-lg text-foreground/80 leading-relaxed">
                    To be the leading geospatial solutions provider in Central Africa, recognized for our innovation,
                    expertise, and commitment to sustainable development. We envision a future where geospatial
                    intelligence drives positive change, environmental stewardship, and economic prosperity across the region.
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Objectives Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-12">
              <h3 className="text-2xl lg:text-3xl font-jost font-bold text-primary mb-4">Our Strategic Objectives</h3>
              <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
                Key goals that guide our operations and drive our commitment to excellence.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  icon: <Award className="h-8 w-8" />,
                  title: "Excellence in Service",
                  description: "Deliver world-class geospatial solutions that exceed client expectations and industry standards."
                },
                {
                  icon: <Users className="h-8 w-8" />,
                  title: "Capacity Building",
                  description: "Develop local expertise and knowledge transfer to strengthen regional geospatial capabilities."
                },
                {
                  icon: <Leaf className="h-8 w-8" />,
                  title: "Environmental Stewardship",
                  description: "Promote sustainable practices and environmental conservation through data-driven insights."
                },
                {
                  icon: <BarChart3 className="h-8 w-8" />,
                  title: "Innovation Leadership",
                  description: "Pioneer cutting-edge technologies and methodologies in geospatial analysis and applications."
                },
                {
                  icon: <MapPin className="h-8 w-8" />,
                  title: "Regional Impact",
                  description: "Create positive socio-economic impact across Central Africa through strategic partnerships."
                },
                {
                  icon: <Shield className="h-8 w-8" />,
                  title: "Data Integrity",
                  description: "Maintain the highest standards of data quality, security, and ethical practices in all operations."
                }
              ].map((objective, index) => (
                <motion.div
                  key={objective.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="p-6 h-full text-center hover:shadow-lg transition-shadow duration-300">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <div className="text-primary">{objective.icon}</div>
                    </div>
                    <h4 className="text-lg font-jost font-semibold text-primary mb-3">{objective.title}</h4>
                    <p className="text-sm text-foreground/70 leading-relaxed">{objective.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-r from-primary/10 via-teal-500/10 to-secondary/10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Why Partner with GEOSTRATDRC?</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              We offer unparalleled expertise, local knowledge, and a commitment to delivering impactful results.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { title: 'Local Expertise, Global Standards', description: 'Deep understanding of the Central African context combined with international best practices.', icon: <Award className="h-10 w-10 text-primary" /> },
              { title: 'Cutting-Edge Technology', description: 'Utilizing the latest geospatial tools and AI for superior data analysis and insights.', icon: <Target className="h-10 w-10 text-primary" /> },
              { title: 'Client-Centric Approach', description: 'Tailored solutions and dedicated support to ensure your project objectives are met.', icon: <Users className="h-10 w-10 text-primary" /> },
            ].map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.15 }}
                viewport={{ once: true }}
              >
                <Card className="p-6 text-center h-full">
                  <div className="flex justify-center mb-4">{item.icon}</div>
                  <h3 className="text-xl font-jost font-semibold text-primary mb-2">{item.title}</h3>
                  <p className="text-foreground/80">{item.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Let's Build a Sustainable Future Together
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            Contact us today to learn how GEOSTRATDRC can support your vision.
          </p>
          <Button size="lg" asChild className="font-jost">
            <Link to="/contact">Get in Touch</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
