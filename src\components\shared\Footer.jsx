
import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Twitter, Linkedin, Instagram, MapPin, Phone, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input'; // Assuming input is created

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'About Us', path: '/about' },
    { name: 'Services', path: '/services' },
    { name: 'Projects', path: '/projects' },
    { name: 'Contact Us', path: '/contact' },
    { name: 'Blog', path: '/blog' },
  ];

  const servicesLinks = [
    { name: 'Climate-Smart Agriculture', path: '/services#climate-smart-agriculture' },
    { name: 'Natural Risk Assessments', path: '/services#natural-risk-assessments' },
    { name: 'Spatial Data Analytics', path: '/services#spatial-data-analytics' },
    { name: 'Urban Planning', path: '/services#urban-planning' },
    { name: 'Environmental Monitoring', path: '/services#environmental-monitoring' },
    { name: 'Natural Resources Management', path: '/services#natural-resources-management' },
  ];

  const handleNewsletterSubmit = (e) => {
    e.preventDefault();
    // Handle newsletter submission logic here
    // For now, just log to console or use toast
    console.log('Newsletter submitted:', e.target.email.value);
    // toast({ title: "Subscribed!", description: "Thanks for joining our newsletter." });
    e.target.reset();
  };

  return (
    <footer className="bg-gradient-to-r from-primary via-teal-600 to-secondary text-primary-foreground pt-16 pb-8 font-sans">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div>
            <Link to="/" className="flex items-center mb-4">
              <img
                src="/geostrat-logo.svg"
                className="h-10 w-auto mr-2 filter brightness-0 invert"
                alt="GEOSTRATDRC Logo White"
                onError={(e) => {
                  console.log('Footer logo failed to load:', e.target.src);
                  e.target.style.display = 'none';
                }}
                onLoad={() => console.log('Footer logo loaded successfully')}
              />
              <span className="font-jost text-2xl font-bold">GEOSTRAT<span className="opacity-80">DRC</span></span>
            </Link>
            <p className="text-sm opacity-90 mb-4">
              Leading geospatial innovation in Central Africa. We provide cutting-edge solutions for a sustainable future.
            </p>
            <div className="flex space-x-4">
              <a href="#" target="_blank" rel="noopener noreferrer" className="hover:opacity-75 transition-opacity"><Facebook size={20} /></a>
              <a href="#" target="_blank" rel="noopener noreferrer" className="hover:opacity-75 transition-opacity"><Twitter size={20} /></a>
              <a href="#" target="_blank" rel="noopener noreferrer" className="hover:opacity-75 transition-opacity"><Linkedin size={20} /></a>
              <a href="#" target="_blank" rel="noopener noreferrer" className="hover:opacity-75 transition-opacity"><Instagram size={20} /></a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <p className="font-jost text-lg font-semibold mb-4">Quick Links</p>
            <ul className="space-y-2">
              {quickLinks.map(link => (
                <li key={link.name}>
                  <Link to={link.path} className="text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity">{link.name}</Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services Highlights */}
          <div>
            <p className="font-jost text-lg font-semibold mb-4">Our Services</p>
            <ul className="space-y-2">
              {servicesLinks.map(link => (
                <li key={link.name}>
                  <Link to={link.path} className="text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity">{link.name}</Link>
                </li>
              ))}
              <li><Link to="/services" className="text-sm hover:underline opacity-90 hover:opacity-100 transition-opacity font-semibold">View All Services...</Link></li>
            </ul>
          </div>
          
          {/* Newsletter Signup */}
          <div>
            <p className="font-jost text-lg font-semibold mb-4">Stay Updated</p>
            <p className="text-sm opacity-90 mb-3">Subscribe to our newsletter for the latest updates and insights.</p>
            <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-2">
              <Input 
                type="email" 
                name="email"
                placeholder="Enter your email" 
                className="bg-white/20 border-white/30 placeholder-white/70 text-white focus:bg-white/30 focus:ring-white/50 flex-grow" 
                required 
              />
              <Button type="submit" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary font-jost">
                Subscribe
              </Button>
            </form>
          </div>
        </div>

        {/* Contact Info & Copyright */}
        <div className="border-t border-white/20 pt-8 mt-8 text-center md:text-left">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 text-sm">
            <div className="flex items-center justify-center md:justify-start">
              <MapPin size={18} className="mr-2 opacity-80" />
              <span className="opacity-90">Kinshasa, Democratic Republic of Congo</span>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <Phone size={18} className="mr-2 opacity-80" />
              <span className="opacity-90">+243 XXX XXX XXX</span>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <Mail size={18} className="mr-2 opacity-80" />
              <span className="opacity-90"><EMAIL></span>
            </div>
          </div>
          <p className="text-sm opacity-80">
            &copy; {currentYear} GEOSTRATDRC. All Rights Reserved. 
            <Link to="/privacy-policy" className="hover:underline ml-2">Privacy Policy</Link> | 
            <Link to="/terms-of-service" className="hover:underline ml-1">Terms of Service</Link>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
