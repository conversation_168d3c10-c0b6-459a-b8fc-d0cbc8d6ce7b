
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { AlertTriangle, Home } from 'lucide-react';

const NotFoundPage = () => {
  return (
    <div className="min-h-[calc(100vh-10rem)] flex flex-col items-center justify-center text-center px-4 font-sans bg-gradient-to-br from-background via-muted to-background">
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: -50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.6, type: 'spring', stiffness: 100 }}
        className="p-8 md:p-12 rounded-xl shadow-2xl bg-background/80 backdrop-blur-md max-w-lg w-full"
      >
        <AlertTriangle className="h-24 w-24 text-destructive mx-auto mb-6" />
        <h1 className="text-6xl md:text-8xl font-jost font-extrabold text-primary mb-4">404</h1>
        <h2 className="text-2xl md:text-3xl font-jost font-semibold text-foreground mb-3">Page Not Found</h2>
        <p className="text-md md:text-lg text-foreground/70 mb-8">
          Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or perhaps you mistyped the URL.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="font-jost">
            <Link to="/">
              <Home className="mr-2 h-5 w-5" /> Go to Homepage
            </Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="font-jost border-primary text-primary hover:bg-primary/10">
            <Link to="/contact">Contact Support</Link>
          </Button>
        </div>
      </motion.div>
      <motion.div 
        className="mt-12"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <img  
          src="lost_explorer_illustration.svg" 
          alt="Illustration of a lost explorer looking at a map" 
          className="h-48 md:h-64 opacity-70"
         src="https://images.unsplash.com/photo-1689773132527-bcabdc88a395" />
      </motion.div>
    </div>
  );
};

export default NotFoundPage;
