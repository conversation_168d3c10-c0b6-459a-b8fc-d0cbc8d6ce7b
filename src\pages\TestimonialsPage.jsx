
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, ChevronLeft, ChevronRight, MessageSquare } from 'lucide-react';
import { Link } from 'react-router-dom';

const testimonialsList = [
  { id: 1, name: "<PERSON><PERSON><PERSON>", company: "AgriCoop Congo", role: "CEO", image: "client_avatar_1.jpg", alt: "<PERSON><PERSON><PERSON>", logo: "client_logo_agricoop.png", altLogo: "AgriCoop Congo Logo", quote: "GEOSTRATDRC's climate-smart agriculture solutions have revolutionized our farming practices. We've seen a significant increase in yield and resource efficiency. Their team is knowledgeable and truly dedicated.", rating: 5 },
  { id: 2, name: "<PERSON>", company: "UrbanDev Kinshasa", role: "Director of Planning", image: "client_avatar_2.jpg", alt: "<PERSON>", logo: "client_logo_urbandev.png", altLogo: "UrbanDev Kinshasa Logo", quote: "The spatial data analytics provided by GEOSTRATDRC were instrumental in our urban expansion project. Their insights helped us make informed decisions for sustainable development. Highly professional and reliable.", rating: 5 },
  { id: 3, name: "Dr. Emmanuel Okoye", company: "Congo Basin Conservation Initiative", role: "Lead Researcher", image: "client_avatar_3.jpg", alt: "Dr. Emmanuel Okoye", logo: "client_logo_cbci.png", altLogo: "Congo Basin Conservation Initiative Logo", quote: "Working with GEOSTRATDRC on environmental monitoring has been a game-changer. Their advanced remote sensing capabilities and detailed reports are invaluable for our conservation efforts.", rating: 4 },
  { id: 4, name: "Fatou Diop", company: "Safer Communities NGO", role: "Program Manager", image: "client_avatar_4.jpg", alt: "Fatou Diop", logo: "client_logo_safercomm.png", altLogo: "Safer Communities NGO Logo", quote: "The natural risk assessment maps developed by GEOSTRATDRC have greatly improved our disaster preparedness programs. Their work is making a real difference in protecting vulnerable communities.", rating: 5 },
  { id: 5, name: "Ahmed Al-Fassi", company: "ResourceMin DRC", role: "Operations Manager", image: "client_avatar_5.jpg", alt: "Ahmed Al-Fassi", logo: "client_logo_resourcemin.png", altLogo: "ResourceMin DRC Logo", quote: "GEOSTRATDRC's expertise in natural resource management helped us optimize our operations while adhering to environmental best practices. Their team is responsive and delivers high-quality results.", rating: 4 },
];

const TestimonialsPage = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonialsList.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonialsList.length) % testimonialsList.length);
  };
  
  useEffect(() => {
    const timer = setTimeout(nextTestimonial, 8000); // Auto-scroll every 8 seconds
    return () => clearTimeout(timer);
  }, [currentTestimonial]);


  const { name, company, role, image, alt, logo, altLogo, quote, rating } = testimonialsList[currentTestimonial];

  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Client Success Stories
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Hear from organizations that have partnered with GEOSTRATDRC to achieve their goals.
          </motion.p>
        </div>
      </section>

      {/* Testimonials Carousel Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto relative">
            <Card className="shadow-xl overflow-hidden bg-background">
              <CardContent className="p-8 md:p-12 text-center">
                <motion.div
                  key={currentTestimonial} // Re-trigger animation on change
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  <img  
                    src={logo} 
                    alt={altLogo} 
                    className="h-12 md:h-16 mx-auto mb-6 object-contain filter grayscale opacity-70" 
                   src="https://images.unsplash.com/photo-1485531865381-286666aa80a9" />
                  <MessageSquare className="h-10 w-10 text-primary/30 mx-auto mb-4" />
                  <p className="text-lg md:text-xl italic text-foreground/90 mb-6 leading-relaxed">"{quote}"</p>
                  <div className="flex justify-center mb-2">
                    {Array(rating).fill(0).map((_, i) => <Star key={i} className="h-5 w-5 text-yellow-400 fill-yellow-400" />)}
                    {Array(5 - rating).fill(0).map((_, i) => <Star key={i+rating} className="h-5 w-5 text-yellow-400" />)}
                  </div>
                  <Avatar className="w-16 h-16 mx-auto mb-2 border-2 border-primary/50">
                    <AvatarImage src={image} alt={alt} />
                    <AvatarFallback className="font-jost bg-muted">{name.split(' ').map(n=>n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <p className="font-jost font-semibold text-primary text-lg">{name}</p>
                  <p className="text-sm text-muted-foreground">{role}, {company}</p>
                </motion.div>
              </CardContent>
            </Card>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={prevTestimonial} 
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 md:-translate-x-full bg-background hover:bg-muted rounded-full shadow-md"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-6 w-6 text-primary" />
            </Button>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={nextTestimonial} 
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 md:translate-x-full bg-background hover:bg-muted rounded-full shadow-md"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-6 w-6 text-primary" />
            </Button>
          </div>
          <div className="flex justify-center mt-8 space-x-2">
            {testimonialsList.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`h-2.5 w-2.5 rounded-full transition-all duration-300 ${currentTestimonial === index ? 'bg-primary w-6' : 'bg-muted hover:bg-primary/50'}`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </section>

      {/* All Testimonials Grid (Optional - if many testimonials) */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">More Success Stories</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              Read what other clients have to say about their experience with GEOSTRATDRC.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonialsList.map((testimonial) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Card className="h-full flex flex-col text-left p-6 hover:shadow-lg transition-shadow duration-300">
                  <div className="flex items-center mb-4">
                    <Avatar className="w-12 h-12 mr-4 border-2 border-primary/30">
                      <AvatarImage src={testimonial.image} alt={testimonial.alt} />
                      <AvatarFallback className="font-jost bg-muted">{testimonial.name.split(' ').map(n=>n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-jost font-semibold text-primary">{testimonial.name}</p>
                      <p className="text-xs text-muted-foreground">{testimonial.role}, {testimonial.company}</p>
                    </div>
                  </div>
                  <p className="text-sm text-foreground/80 italic mb-4 flex-grow">"{testimonial.quote.substring(0, 150)}..."</p>
                  <div className="flex">
                    {Array(testimonial.rating).fill(0).map((_, i) => <Star key={i} className="h-4 w-4 text-yellow-400 fill-yellow-400" />)}
                    {Array(5 - testimonial.rating).fill(0).map((_, i) => <Star key={i+testimonial.rating} className="h-4 w-4 text-yellow-400" />)}
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Ready to Be Our Next Success Story?
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            Partner with GEOSTRATDRC and let us help you achieve your project goals with our expert geospatial solutions.
          </p>
          <Button size="lg" asChild className="font-jost">
            <Link to="/contact">Contact Us Today</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default TestimonialsPage;
