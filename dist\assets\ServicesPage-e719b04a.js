import{j as a,m as t,R as o,B as i,L as n,M as r}from"./index-2bfcaeaf.js";import"./card-3197beef.js";import{C as l}from"./chevron-right-98f26ede.js";import{L as m,B as c,G as d}from"./leaf-2cf97dcf.js";import{S as p,B as h,C as g,D as u}from"./shield-alert-c18be38e.js";import{A as x}from"./alert-triangle-47cd5b39.js";const f=[{id:"climate-smart-agriculture",name:"Climate-Smart Agriculture",icon:a.jsx(m,{className:"h-10 w-10"}),description:"Revolutionizing agricultural practices with innovative technologies and sustainable approaches for improved productivity.",image:"https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=800&h=600&fit=crop",alt:"Drone flying over a lush farm field for data collection"},{id:"natural-risk-assessments",name:"Natural Risk Assessments",icon:a.jsx(p,{className:"h-10 w-10"}),description:"Comprehensive risk analysis and management strategies for natural hazards and environmental challenges.",image:"https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=800&h=600&fit=crop",alt:"Geospatial map highlighting areas of natural risk"},{id:"spatial-data-analytics",name:"Spatial Data Analytics",icon:a.jsx(c,{className:"h-10 w-10"}),description:"Advanced geospatial analysis and data interpretation for informed decision-making processes.",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop",alt:"Computer screen showing complex spatial data visualizations"},{id:"urban-planning",name:"Urban Planning",icon:a.jsx(h,{className:"h-10 w-10"}),description:"Sustainable urban development solutions with focus on smart city planning and infrastructure optimization.",image:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop",alt:"Digital blueprint of a modern city plan"},{id:"environmental-monitoring",name:"Environmental Monitoring",icon:a.jsx(d,{className:"h-10 w-10"}),description:"Comprehensive environmental assessment and monitoring systems for sustainable resource management.",image:"https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=800&h=600&fit=crop",alt:"Satellite image showing changes in land cover over time"},{id:"natural-resources-management",name:"Natural Resources Management",icon:a.jsx(r,{className:"h-10 w-10"}),description:"Strategic planning and sustainable management of natural resources for long-term conservation.",image:"https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop",alt:"Layered map showing different natural resources in a region"},{id:"climate-change-adaptation",name:"Climate Change Adaptation",icon:a.jsx(g,{className:"h-10 w-10"}),description:"Innovative strategies and solutions for climate change mitigation and adaptation planning.",image:"https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=600&fit=crop",alt:"Infographic illustrating climate change adaptation strategies"},{id:"water-resources-management",name:"Water Resources Management",icon:a.jsx(u,{className:"h-10 w-10"}),description:"Integrated water resource management solutions for sustainable water utilization and conservation.",image:"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800&h=600&fit=crop",alt:"Diagram showing a comprehensive water resource management system"},{id:"disaster-risk-management",name:"Disaster Risk Management",icon:a.jsx(x,{className:"h-10 w-10"}),description:"Comprehensive disaster risk assessment and management strategies for community resilience.",image:"https://images.unsplash.com/photo-1586348943529-beaae6c28db9?w=800&h=600&fit=crop",alt:"Emergency response team using maps in a command center"}],C=()=>a.jsxs("div",{className:"animate-fade-in font-sans",children:[a.jsxs("section",{className:"relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground",children:[a.jsx("div",{className:"absolute inset-0 bg-black/30"}),a.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[a.jsx(t.h1,{className:"text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:"Our Geospatial Services"}),a.jsx(t.p,{className:"text-lg md:text-xl max-w-3xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"Delivering comprehensive solutions to address complex environmental and developmental challenges."})]})]}),a.jsx("section",{className:"py-16 lg:py-24",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsx("div",{className:"space-y-16",children:f.map((e,s)=>a.jsxs(t.div,{id:e.id,className:"grid md:grid-cols-2 gap-8 md:gap-12 items-center",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:s*.1},viewport:{once:!0},children:[a.jsx("div",{className:`md:order-${s%2===0?1:2}`,children:a.jsx("img",{src:e.image,alt:e.alt,className:"rounded-lg shadow-xl w-full h-auto object-cover max-h-[400px]",src:"https://images.unsplash.com/photo-1599472696777-95cab5e0f891"})}),a.jsxs("div",{className:`md:order-${s%2===0?2:1}`,children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"p-3 rounded-full bg-primary/10 text-primary mr-4",children:o.cloneElement(e.icon,{className:"h-8 w-8"})}),a.jsx("h2",{className:"text-2xl lg:text-3xl font-jost font-semibold text-primary",children:e.name})]}),a.jsx("p",{className:"text-foreground/80 mb-6 leading-relaxed",children:e.description}),a.jsx(i,{asChild:!0,variant:"default",className:"font-jost",children:a.jsxs(n,{to:`/quote?service=${encodeURIComponent(e.name)}`,children:["Request Quote for ",e.name," ",a.jsx(l,{className:"ml-2 h-4 w-4"})]})})]})]},e.id))})})}),a.jsx("section",{className:"py-16 lg:py-24 bg-muted",children:a.jsxs("div",{className:"container mx-auto px-4 text-center",children:[a.jsx("h2",{className:"text-3xl lg:text-4xl font-jost font-bold text-primary mb-6",children:"Have a Specific Challenge?"}),a.jsx("p",{className:"text-lg text-foreground/80 max-w-xl mx-auto mb-8",children:"Our team is ready to discuss your unique requirements and develop a tailored geospatial solution."}),a.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[a.jsx(i,{size:"lg",asChild:!0,className:"font-jost",children:a.jsx(n,{to:"/contact",children:"Contact Us"})}),a.jsx(i,{size:"lg",variant:"outline",asChild:!0,className:"font-jost border-primary text-primary hover:bg-primary/10",children:a.jsx(n,{to:"/projects",children:"See Our Work"})})]})]})})]});export{C as default};
