
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tag, Calendar, User, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const mockBlogPosts = [
  { id: 1, title: "The Future of Climate-Smart Agriculture in DRC", date: "2025-05-15", author: "<PERSON><PERSON> <PERSON>", category: "Agriculture", tags: ["Climate Change", "Farming", "Technology"], excerpt: "Exploring how geospatial data is revolutionizing farming practices to enhance resilience and productivity in the face of climate change...", image: "blog_post_agriculture.jpg", alt: "Modern farm with data overlays" },
  { id: 2, title: "Urban Sprawl in Kinshasa: A Geospatial Perspective", date: "2025-04-28", author: "<PERSON><PERSON>", category: "Urban Planning", tags: ["City Growth", "Infrastructure", "Sustainability"], excerpt: "Analyzing the rapid urban expansion of Kinshasa using satellite imagery and GIS, and its implications for sustainable development.", image: "blog_post_kinshasa.jpg", alt: "Satellite view of Kinshasa urban area" },
  { id: 3, title: "Mitigating Natural Risks with Advanced Data Analytics", date: "2025-04-10", author: "Kwame Nkosi", category: "Risk Assessment", tags: ["Disaster Management", "Data Science", "Safety"], excerpt: "How advanced data analytics and geospatial modeling are key to identifying, assessing, and mitigating natural hazard risks.", image: "blog_post_risk.jpg", alt: "Map showing risk zones" },
  { id: 4, title: "Conservation Tech: Protecting DRC's Biodiversity", date: "2025-03-22", author: "David Okoro", category: "Environment", tags: ["Conservation", "Biodiversity", "Remote Sensing"], excerpt: "Leveraging remote sensing and GIS for effective monitoring and protection of the Democratic Republic of Congo's rich biodiversity.", image: "blog_post_conservation.jpg", alt: "Lush rainforest canopy" },
  { id: 5, title: "The Role of GIS in Water Resource Management", date: "2025-03-05", author: "Aisha Bakari", category: "Water Management", tags: ["Water Scarcity", "GIS", "Sustainability"], excerpt: "A deep dive into how Geographic Information Systems are crucial for sustainable water resource management and equitable access.", image: "blog_post_water.jpg", alt: "Clear river flowing through a green valley" },
];

const POSTS_PER_PAGE = 3;

const BlogPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);

  const categories = ['All', ...new Set(mockBlogPosts.map(post => post.category))];
  
  const filteredPosts = mockBlogPosts
    .filter(post => post.title.toLowerCase().includes(searchTerm.toLowerCase()) || post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()))
    .filter(post => selectedCategory === 'All' || post.category === selectedCategory);

  const totalPages = Math.ceil(filteredPosts.length / POSTS_PER_PAGE);
  const paginatedPosts = filteredPosts.slice((currentPage - 1) * POSTS_PER_PAGE, currentPage * POSTS_PER_PAGE);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); 
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };
  
  const goToPage = (pageNumber) => {
    setCurrentPage(Math.max(1, Math.min(pageNumber, totalPages)));
  };

  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            GEOSTRATDRC Insights
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Stay informed with our latest articles, research, and perspectives on geospatial technology and its applications.
          </motion.p>
        </div>
      </section>

      {/* Blog Content Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-12 gap-8">
            {/* Blog Posts */}
            <div className="lg:col-span-8">
              {paginatedPosts.length > 0 ? paginatedPosts.map((post, index) => (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="mb-12"
                >
                  <Card className="overflow-hidden group hover:shadow-xl transition-shadow duration-300">
                    <Link to={`/blog/${post.id}`}> {/* Assuming dynamic route for single post */}
                      <img  
                        src={post.image} 
                        alt={post.alt} 
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                       src="https://images.unsplash.com/photo-1578470155518-c5697b07e280" />
                    </Link>
                    <CardContent className="p-6">
                      <div className="flex items-center text-xs text-muted-foreground mb-2 space-x-4">
                        <span className="flex items-center"><Calendar className="h-4 w-4 mr-1" /> {post.date}</span>
                        <span className="flex items-center"><User className="h-4 w-4 mr-1" /> {post.author}</span>
                        <span className="flex items-center"><Tag className="h-4 w-4 mr-1" /> {post.category}</span>
                      </div>
                      <Link to={`/blog/${post.id}`}>
                        <CardTitle className="font-jost text-2xl text-primary mb-2 group-hover:text-primary/80 transition-colors">
                          {post.title}
                        </CardTitle>
                      </Link>
                      <CardDescription className="text-foreground/80 mb-4">{post.excerpt}</CardDescription>
                      <div className="mb-4">
                        {post.tags.map(tag => (
                          <span key={tag} className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-full mr-1 mb-1 inline-block">{tag}</span>
                        ))}
                      </div>
                      <Button variant="link" asChild className="text-primary font-semibold p-0 h-auto">
                        <Link to={`/blog/${post.id}`}>Read More &rarr;</Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              )) : (
                <p className="text-center text-lg text-foreground/70 col-span-full py-10">
                  No blog posts found matching your criteria. Try adjusting your search or filter.
                </p>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-12">
                  <Button variant="outline" size="icon" onClick={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                     <Button 
                        key={page} 
                        variant={currentPage === page ? "default" : "outline"}
                        onClick={() => goToPage(page)}
                        className="font-jost"
                      >
                        {page}
                      </Button>
                  ))}
                  <Button variant="outline" size="icon" onClick={() => goToPage(currentPage + 1)} disabled={currentPage === totalPages}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <aside className="lg:col-span-4 space-y-8">
              {/* Search Bar */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-jost text-lg flex items-center"><Search className="h-5 w-5 mr-2 text-primary" />Search Blog</CardTitle>
                </CardHeader>
                <CardContent>
                  <Input 
                    type="text" 
                    placeholder="Search articles..." 
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-full"
                  />
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-jost text-lg flex items-center"><Tag className="h-5 w-5 mr-2 text-primary" />Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {categories.map(category => (
                      <li key={category}>
                        <Button 
                          variant={selectedCategory === category ? 'secondary' : 'ghost'} 
                          onClick={() => handleCategoryChange(category)}
                          className="w-full justify-start font-jost"
                        >
                          {category}
                        </Button>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
              
              {/* Recent Posts (Simplified) */}
               <Card>
                <CardHeader>
                  <CardTitle className="font-jost text-lg">Recent Posts</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {mockBlogPosts.slice(0, 3).map(post => (
                      <li key={post.id} className="text-sm">
                        <Link to={`/blog/${post.id}`} className="text-primary hover:underline font-medium block">{post.title}</Link>
                        <span className="text-xs text-muted-foreground">{post.date}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </aside>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
