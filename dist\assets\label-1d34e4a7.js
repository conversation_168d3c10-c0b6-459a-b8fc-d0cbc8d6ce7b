import{R as o,j as r,p as i,r as d,P as u,z as f}from"./index-4bd11ef8.js";const c=o.forwardRef(({className:a,...t},e)=>r.jsx("textarea",{className:i("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...t}));c.displayName="Textarea";var b="Label",l=d.forwardRef((a,t)=>r.jsx(u.label,{...a,ref:t,onMouseDown:e=>{var s;e.target.closest("button, input, select, textarea")||((s=a.onMouseDown)==null||s.call(a,e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));l.displayName=b;var n=l;const m=f("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=o.forwardRef(({className:a,...t},e)=>r.jsx(n,{ref:e,className:i(m(),a),...t}));x.displayName=n.displayName;export{x as L,c as T};
