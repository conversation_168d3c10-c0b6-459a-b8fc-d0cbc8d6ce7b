import{c as x,u as h,r as p,t as u,j as e,m as t,I as n,B as a,M as o,v as j,s as g,F as f,T as y,q as b,w as N,x as v}from"./index-e7747fd2.js";import{L as r,T as w}from"./label-12fa0dfc.js";import{C as S,a as C,b as k,c as M}from"./card-68470f4b.js";const I=x("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),z=()=>{const{toast:c}=h();p.useEffect(()=>{u()},[]);const m=async s=>{s.preventDefault();const d=new FormData(s.target),i=Object.fromEntries(d.entries());console.log("Contact Form Data:",i);try{const l=JSON.parse(localStorage.getItem("contactMessages"))||[];if(localStorage.setItem("contactMessages",JSON.stringify([...l,{...i,date:new Date().toISOString()}])),(await v(i)).success)c({title:"Message Sent Successfully!",description:"Thank you for contacting us. We'll be in touch <NAME_EMAIL>.",variant:"default"}),s.target.reset();else throw new Error("Email sending failed")}catch(l){console.error("Failed to send contact message:",l),c({title:"Message Saved Locally",description:"Your message was saved but email notification failed. We'll still review your message.",variant:"default"}),s.target.reset()}};return e.jsxs("div",{className:"animate-fade-in font-sans",children:[e.jsxs("section",{className:"relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground",children:[e.jsx("div",{className:"absolute inset-0 bg-black/30"}),e.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[e.jsx(t.h1,{className:"text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:"Get in Touch"}),e.jsx(t.p,{className:"text-lg md:text-xl max-w-3xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"We're here to answer your questions and explore how our geospatial solutions can help you."})]})]}),e.jsx("section",{className:"py-16 lg:py-24",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"grid lg:grid-cols-2 gap-12",children:[e.jsx(t.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},children:e.jsxs(S,{className:"p-6 sm:p-8 shadow-xl bg-background",children:[e.jsx(C,{className:"p-0 mb-6",children:e.jsxs(k,{className:"font-jost text-2xl text-primary flex items-center",children:[e.jsx(I,{className:"h-6 w-6 mr-2"})," Send Us a Message"]})}),e.jsx(M,{className:"p-0",children:e.jsxs("form",{onSubmit:m,className:"space-y-6",children:[e.jsxs("div",{className:"grid sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"contact-name",children:"Full Name"}),e.jsx(n,{id:"contact-name",name:"name",type:"text",placeholder:"Your Name",required:!0,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"contact-email",children:"Email Address"}),e.jsx(n,{id:"contact-email",name:"email",type:"email",placeholder:"<EMAIL>",required:!0,className:"mt-1"})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"contact-subject",children:"Subject"}),e.jsx(n,{id:"contact-subject",name:"subject",type:"text",placeholder:"Inquiry about...",required:!0,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"contact-message",children:"Message"}),e.jsx(w,{id:"contact-message",name:"message",placeholder:"Your message here...",required:!0,rows:5,className:"mt-1"})]}),e.jsx(a,{type:"submit",size:"lg",className:"w-full font-jost",children:"Send Message"})]})})]})}),e.jsxs(t.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-jost font-semibold text-primary mb-4",children:"Contact Information"}),e.jsxs("div",{className:"space-y-4 text-foreground/80",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(o,{className:"h-6 w-6 mr-3 mt-1 text-primary flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-foreground",children:"GEOSTRATDRC Headquarters"}),e.jsx("p",{children:"Bukavu/Ibanda/Quartier Nyalukemba/avenue du lac"}),e.jsx("p",{children:"Bukavu, Democratic Republic of Congo"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(j,{className:"h-5 w-5 mr-3 text-primary"}),e.jsx("a",{href:"tel:+243973583690",className:"hover:text-primary",children:"+243 973 583 690"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(g,{className:"h-5 w-5 mr-3 text-primary"}),e.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-primary",children:"<EMAIL>"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-jost font-semibold text-primary mb-3",children:"Business Hours"}),e.jsx("p",{className:"text-foreground/80",children:"Monday - Friday: 9:00 AM - 5:00 PM (WAT)"}),e.jsx("p",{className:"text-foreground/80",children:"Saturday - Sunday: Closed"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-jost font-semibold text-primary mb-3",children:"Connect With Us"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(a,{variant:"outline",size:"icon",asChild:!0,className:"text-primary border-primary hover:bg-primary/10",children:e.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer","aria-label":"Facebook",children:e.jsx(f,{})})}),e.jsx(a,{variant:"outline",size:"icon",asChild:!0,className:"text-primary border-primary hover:bg-primary/10",children:e.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer","aria-label":"Twitter",children:e.jsx(y,{})})}),e.jsx(a,{variant:"outline",size:"icon",asChild:!0,className:"text-primary border-primary hover:bg-primary/10",children:e.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer","aria-label":"LinkedIn",children:e.jsx(b,{})})}),e.jsx(a,{variant:"outline",size:"icon",asChild:!0,className:"text-primary border-primary hover:bg-primary/10",children:e.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer","aria-label":"Instagram",children:e.jsx(N,{})})})]})]})]})]})})}),e.jsx("section",{className:"h-[400px] md:h-[500px] bg-muted",children:e.jsx("div",{className:"w-full h-full flex items-center justify-center text-foreground/50",children:e.jsxs("div",{className:"text-center p-4",children:[e.jsx(o,{size:48,className:"mx-auto mb-2"}),e.jsx("p",{className:"font-jost text-xl",children:"Our Office Location"}),e.jsx("p",{className:"text-sm",children:"(Interactive Map Will Be Displayed Here)"}),e.jsx("p",{className:"text-xs mt-2",children:"Using OpenStreetMap"})]})})})]})};export{z as default};
