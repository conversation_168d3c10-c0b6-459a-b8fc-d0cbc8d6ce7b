import{r as u,g as $,j as c,P as p,e as F,b as m,R as S,p as g}from"./index-0a8002e2.js";var y={exports:{}},h={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var d=u;function M(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var P=typeof Object.is=="function"?Object.is:M,T=d.useState,V=d.useEffect,D=d.useLayoutEffect,G=d.useDebugValue;function H(e,t){var a=t(),s=T({inst:{value:a,getSnapshot:t}}),n=s[0].inst,r=s[1];return D(function(){n.value=a,n.getSnapshot=t,v(n)&&r({inst:n})},[e,a,t]),V(function(){return v(n)&&r({inst:n}),e(function(){v(n)&&r({inst:n})})},[e]),G(a),a}function v(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!P(e,a)}catch{return!0}}function q(e,t){return t()}var B=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?q:H;h.useSyncExternalStore=d.useSyncExternalStore!==void 0?d.useSyncExternalStore:B;y.exports=h;var K=y.exports;function O(){return K.useSyncExternalStore(U,()=>!0,()=>!1)}function U(){return()=>{}}var A="Avatar",[W,ee]=$(A),[z,w]=W(A),L=u.forwardRef((e,t)=>{const{__scopeAvatar:a,...s}=e,[n,r]=u.useState("idle");return c.jsx(z,{scope:a,imageLoadingStatus:n,onImageLoadingStatusChange:r,children:c.jsx(p.span,{...s,ref:t})})});L.displayName=A;var R="AvatarImage",N=u.forwardRef((e,t)=>{const{__scopeAvatar:a,src:s,onLoadingStatusChange:n=()=>{},...r}=e,l=w(R,a),o=J(s,r),i=F(f=>{n(f),l.onImageLoadingStatusChange(f)});return m(()=>{o!=="idle"&&i(o)},[o,i]),o==="loaded"?c.jsx(p.img,{...r,ref:t,src:s}):null});N.displayName=R;var b="AvatarFallback",I=u.forwardRef((e,t)=>{const{__scopeAvatar:a,delayMs:s,...n}=e,r=w(b,a),[l,o]=u.useState(s===void 0);return u.useEffect(()=>{if(s!==void 0){const i=window.setTimeout(()=>o(!0),s);return()=>window.clearTimeout(i)}},[s]),l&&r.imageLoadingStatus!=="loaded"?c.jsx(p.span,{...n,ref:t}):null});I.displayName=b;function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function J(e,{referrerPolicy:t,crossOrigin:a}){const s=O(),n=u.useRef(null),r=(()=>s?(n.current||(n.current=new window.Image),n.current):null)(),[l,o]=u.useState(()=>x(r,e));return m(()=>{o(x(r,e))},[r,e]),m(()=>{const i=k=>()=>{o(k)};if(!r)return;const f=i("loaded"),E=i("error");return r.addEventListener("load",f),r.addEventListener("error",E),t&&(r.referrerPolicy=t),typeof a=="string"&&(r.crossOrigin=a),()=>{r.removeEventListener("load",f),r.removeEventListener("error",E)}},[r,a,t]),l}var j=L,C=N,_=I;const Q=S.forwardRef(({className:e,...t},a)=>c.jsx(j,{ref:a,className:g("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));Q.displayName=j.displayName;const X=S.forwardRef(({className:e,...t},a)=>c.jsx(C,{ref:a,className:g("aspect-square h-full w-full",e),...t}));X.displayName=C.displayName;const Y=S.forwardRef(({className:e,...t},a)=>c.jsx(_,{ref:a,className:g("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));Y.displayName=_.displayName;export{Q as A,X as a,Y as b};
