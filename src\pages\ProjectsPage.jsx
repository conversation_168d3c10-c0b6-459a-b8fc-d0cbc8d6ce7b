
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Filter, Briefcase, CheckSquare, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';

const allProjects = [
  { id: 1, title: 'Sustainable Agriculture Initiative', category: 'Completed', image: 'project_agri.jpg', alt: 'Drone view of a sustainable farm', summary: 'Implemented precision agriculture techniques for a 20% increase in crop yield and 15% reduction in water usage for a large cooperative in Bas-Congo.', details: 'This project involved...', outcome: 'Enhanced food security and sustainable farming practices.' },
  { id:2, title: 'Urban Expansion Monitoring - Kinshasa', category: 'Ongoing', image: 'project_urban.jpg', alt: 'Satellite imagery showing urban sprawl of Kinshasa', summary: 'Developing a dynamic monitoring system for urban growth in Kinshasa to support infrastructure planning and resource allocation.', details: 'Utilizing high-resolution satellite imagery and AI...', outcome: 'Improved urban planning and service delivery.' },
  { id: 3, title: 'Deforestation Hotspot Analysis', category: 'Completed', image: 'project_deforestation.jpg', alt: 'Map highlighting deforestation hotspots in Congo Basin', summary: 'Identified key deforestation drivers and hotspots in the Congo Basin, providing actionable data for conservation efforts.', details: 'Analyzed multi-temporal satellite data...', outcome: 'Strengthened conservation strategies and policy recommendations.' },
  { id: 4, title: 'Flood Risk Mapping - Ubangi River', category: 'Completed', image: 'project_flood.jpg', alt: 'Flood risk map for a region along Ubangi River', summary: 'Created detailed flood risk maps and early warning system components for communities along the Ubangi River.', details: 'Integrated hydrological models with terrain data...', outcome: 'Increased community resilience to flood events.' },
  { id: 5, title: 'Mining Impact Assessment', category: 'Ongoing', image: 'project_mining.jpg', alt: 'Aerial view of a mining site with environmental impact zones', summary: 'Assessing the environmental and social impacts of mining activities in Katanga province to promote responsible mining.', details: 'Combining field surveys with remote sensing data...', outcome: 'Guidelines for sustainable mining practices.' },
  { id: 6, title: 'Water Resource Management Plan', category: 'Upcoming', image: 'project_water.jpg', alt: 'Map showing water resources in a specific watershed', summary: 'Developing a comprehensive water resource management plan for a critical watershed to ensure equitable access and sustainability.', details: 'This project will involve...', outcome: 'Sustainable water resource allocation.' },
];

const categories = ['All', 'Completed', 'Ongoing', 'Upcoming'];

const ProjectsPage = () => {
  const [filter, setFilter] = useState('All');
  const [selectedProject, setSelectedProject] = useState(null);

  const filteredProjects = filter === 'All' ? allProjects : allProjects.filter(p => p.category === filter);

  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Our Impactful Projects
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Showcasing our commitment to delivering transformative geospatial solutions across various sectors.
          </motion.p>
        </div>
      </section>

      {/* Filter and Projects Grid Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-12">
            <Filter className="h-6 w-6 text-primary mr-2 hidden sm:block" />
            {categories.map(category => (
              <Button
                key={category}
                variant={filter === category ? 'default' : 'outline'}
                onClick={() => setFilter(category)}
                className="font-jost"
              >
                {category === 'Completed' && <CheckSquare className="mr-2 h-4 w-4" />}
                {category === 'Ongoing' && <Clock className="mr-2 h-4 w-4" />}
                {category === 'All' && <Briefcase className="mr-2 h-4 w-4" />}
                {category}
              </Button>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden h-full flex flex-col group hover:shadow-xl transition-shadow duration-300">
                  <img  
                    src={project.image} 
                    alt={project.alt} 
                    className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                   src="https://images.unsplash.com/photo-1599472696777-95cab5e0f891" />
                  <CardHeader>
                    <CardTitle className="font-jost text-xl text-primary group-hover:text-primary/90">{project.title}</CardTitle>
                    <span className={`text-xs font-semibold px-2 py-0.5 rounded-full ${
                      project.category === 'Completed' ? 'bg-green-100 text-green-700' :
                      project.category === 'Ongoing' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-blue-100 text-blue-700'
                    }`}>{project.category}</span>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <CardDescription className="mb-4 text-foreground/80">{project.summary}</CardDescription>
                  </CardContent>
                  <div className="p-6 pt-0">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="link" className="text-primary font-semibold p-0 h-auto" onClick={() => setSelectedProject(project)}>
                          View Case Study &rarr;
                        </Button>
                      </DialogTrigger>
                      {selectedProject && selectedProject.id === project.id && (
                        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader className="mb-4">
                            <DialogTitle className="font-jost text-2xl text-primary">{selectedProject.title}</DialogTitle>
                            <span className={`text-sm font-semibold px-2 py-1 rounded-full inline-block mt-1 ${
                              selectedProject.category === 'Completed' ? 'bg-green-100 text-green-700' :
                              selectedProject.category === 'Ongoing' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-blue-100 text-blue-700'
                            }`}>{selectedProject.category}</span>
                          </DialogHeader>
                          <img  
                            src={selectedProject.image} 
                            alt={selectedProject.alt} 
                            className="w-full h-64 object-cover rounded-md mb-6"
                           src="https://images.unsplash.com/photo-1572177812156-58036aae439c" />
                          <h3 className="font-jost text-lg font-semibold text-foreground mb-2">Project Overview</h3>
                          <p className="text-foreground/80 mb-4">{selectedProject.summary}</p>
                          <h3 className="font-jost text-lg font-semibold text-foreground mb-2">Details & Methodology</h3>
                          <p className="text-foreground/80 mb-4">{selectedProject.details || "Detailed methodology information will be available soon."}</p>
                          <h3 className="font-jost text-lg font-semibold text-foreground mb-2">Outcome & Impact</h3>
                          <p className="text-foreground/80 mb-6">{selectedProject.outcome}</p>
                          <Button asChild className="font-jost">
                            <Link to={`/quote?project=${encodeURIComponent(selectedProject.title)}`}>Inquire About Similar Projects</Link>
                          </Button>
                        </DialogContent>
                      )}
                    </Dialog>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
          {filteredProjects.length === 0 && (
            <p className="text-center text-lg text-foreground/70 col-span-full">
              No projects found for "{filter}". Try a different filter.
            </p>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">
            Partner with Us on Your Next Project
          </h2>
          <p className="text-lg text-foreground/80 max-w-xl mx-auto mb-8">
            Let our expertise in geospatial solutions drive your success. Contact us to discuss your project needs.
          </p>
          <Button size="lg" asChild className="font-jost">
            <Link to="/contact">Get in Touch</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default ProjectsPage;
