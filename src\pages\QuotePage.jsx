
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox'; // Assuming checkbox is created
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FileText, User, Mail, Phone, Briefcase, Layers, DollarSign, CalendarDays, Info } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useLocation } from 'react-router-dom';

const servicesOptions = [
  "Climate-Smart Agriculture", "Natural Risk Assessments", "Spatial Data Analytics",
  "Urban Planning", "Environmental Monitoring", "Natural Resources Management",
  "Climate Change Adaptation", "Water Resources Management", "Disaster Risk Management", "Other"
];

const QuotePage = () => {
  const { toast } = useToast();
  const location = useLocation();
  const [selectedServices, setSelectedServices] = useState([]);
  const [formData, setFormData] = useState({
    name: '', email: '', phone: '', company: '',
    projectTitle: '', projectDescription: '',
    estimatedBudget: '', desiredTimeline: '', additionalInfo: ''
  });

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const serviceFromQuery = queryParams.get('service');
    if (serviceFromQuery && servicesOptions.includes(serviceFromQuery)) {
      setSelectedServices([serviceFromQuery]);
    }
    const projectFromQuery = queryParams.get('project');
    if (projectFromQuery) {
      setFormData(prev => ({ ...prev, projectTitle: `Inquiry about: ${projectFromQuery}`}));
    }
  }, [location.search]);

  const handleServiceToggle = (service) => {
    setSelectedServices(prev => 
      prev.includes(service) ? prev.filter(s => s !== service) : [...prev, service]
    );
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleQuoteSubmit = (e) => {
    e.preventDefault();
    const fullQuoteData = { ...formData, services: selectedServices, submissionDate: new Date().toISOString() };
    console.log("Detailed Quote Request Data:", fullQuoteData);

    try {
      const existingQuotes = JSON.parse(localStorage.getItem('detailedQuoteRequests')) || [];
      localStorage.setItem('detailedQuoteRequests', JSON.stringify([...existingQuotes, fullQuoteData]));
      toast({
        title: "Quote Request Submitted!",
        description: "Thank you! Our team will review your request and contact you shortly.",
        variant: "default",
      });
      // Reset form
      setFormData({
        name: '', email: '', phone: '', company: '',
        projectTitle: '', projectDescription: '',
        estimatedBudget: '', desiredTimeline: '', additionalInfo: ''
      });
      setSelectedServices([]);
      // e.target.reset(); // This might not work well with controlled components for checkboxes
    } catch (error) {
      console.error("Failed to save detailed quote request:", error);
      toast({
        title: "Submission Error",
        description: "Could not submit your quote request. Please try again or contact us directly.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="animate-fade-in font-sans">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 bg-gradient-to-br from-primary to-teal-600 text-primary-foreground">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Request a Detailed Quote
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Provide us with details about your project, and we'll tailor a comprehensive proposal to meet your specific needs.
          </motion.p>
        </div>
      </section>

      {/* Quote Form Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <Card className="max-w-4xl mx-auto p-6 sm:p-8 md:p-10 shadow-2xl bg-background">
            <CardHeader className="p-0 mb-8 text-center">
              <FileText className="h-12 w-12 text-primary mx-auto mb-3" />
              <CardTitle className="font-jost text-3xl text-primary">Project Details Form</CardTitle>
              <CardDescription className="text-foreground/70 mt-1">
                The more information you provide, the more accurate our quote will be.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <form onSubmit={handleQuoteSubmit} className="space-y-8">
                {/* Contact Information */}
                <fieldset className="space-y-6 border p-4 rounded-md border-border">
                  <legend className="text-xl font-jost font-semibold text-primary px-2 flex items-center"><User className="h-5 w-5 mr-2" />Contact Information</legend>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="quote-name">Full Name <span className="text-destructive">*</span></Label>
                      <Input id="quote-name" name="name" value={formData.name} onChange={handleInputChange} type="text" placeholder="e.g., John Doe" required className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="quote-email">Email Address <span className="text-destructive">*</span></Label>
                      <Input id="quote-email" name="email" value={formData.email} onChange={handleInputChange} type="email" placeholder="e.g., <EMAIL>" required className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="quote-phone">Phone Number</Label>
                      <Input id="quote-phone" name="phone" value={formData.phone} onChange={handleInputChange} type="tel" placeholder="e.g., +243 XXX XXX XXX" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="quote-company">Company/Organization (Optional)</Label>
                      <Input id="quote-company" name="company" value={formData.company} onChange={handleInputChange} type="text" placeholder="e.g., Example Corp" className="mt-1" />
                    </div>
                  </div>
                </fieldset>

                {/* Service Selection */}
                <fieldset className="space-y-4 border p-4 rounded-md border-border">
                  <legend className="text-xl font-jost font-semibold text-primary px-2 flex items-center"><Layers className="h-5 w-5 mr-2" />Services of Interest <span className="text-destructive">*</span></legend>
                  <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {servicesOptions.map(service => (
                      <div key={service} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`service-${service.toLowerCase().replace(/\s+/g, '-')}`} 
                          checked={selectedServices.includes(service)}
                          onCheckedChange={() => handleServiceToggle(service)}
                        />
                        <Label htmlFor={`service-${service.toLowerCase().replace(/\s+/g, '-')}`} className="font-normal text-sm cursor-pointer">{service}</Label>
                      </div>
                    ))}
                  </div>
                  {selectedServices.length === 0 && <p className="text-sm text-destructive">Please select at least one service.</p>}
                </fieldset>

                {/* Project Details */}
                <fieldset className="space-y-6 border p-4 rounded-md border-border">
                  <legend className="text-xl font-jost font-semibold text-primary px-2 flex items-center"><Briefcase className="h-5 w-5 mr-2" />Project Details</legend>
                  <div>
                    <Label htmlFor="quote-project-title">Project Title/Name <span className="text-destructive">*</span></Label>
                    <Input id="quote-project-title" name="projectTitle" value={formData.projectTitle} onChange={handleInputChange} type="text" placeholder="e.g., Kinshasa Urban Resilience Plan" required className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="quote-project-description">Project Description & Objectives <span className="text-destructive">*</span></Label>
                    <Textarea id="quote-project-description" name="projectDescription" value={formData.projectDescription} onChange={handleInputChange} placeholder="Describe your project, its goals, scope, and any specific challenges..." required rows={5} className="mt-1" />
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="quote-budget" className="flex items-center"><DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />Estimated Budget (Optional)</Label>
                      <Input id="quote-budget" name="estimatedBudget" value={formData.estimatedBudget} onChange={handleInputChange} type="text" placeholder="e.g., $5,000 - $10,000 USD or N/A" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="quote-timeline" className="flex items-center"><CalendarDays className="h-4 w-4 mr-1 text-muted-foreground" />Desired Timeline (Optional)</Label>
                      <Input id="quote-timeline" name="desiredTimeline" value={formData.desiredTimeline} onChange={handleInputChange} type="text" placeholder="e.g., 3-6 months, or specific deadline" className="mt-1" />
                    </div>
                  </div>
                </fieldset>
                
                {/* Additional Information */}
                <fieldset className="space-y-6 border p-4 rounded-md border-border">
                  <legend className="text-xl font-jost font-semibold text-primary px-2 flex items-center"><Info className="h-5 w-5 mr-2" />Additional Information</legend>
                  <div>
                    <Label htmlFor="quote-additional-info">Any Other Relevant Information (Optional)</Label>
                    <Textarea id="quote-additional-info" name="additionalInfo" value={formData.additionalInfo} onChange={handleInputChange} placeholder="Include any existing data, specific requirements, or questions you have..." rows={3} className="mt-1" />
                  </div>
                </fieldset>

                <Button 
                  type="submit" 
                  size="lg" 
                  className="w-full font-jost text-lg py-3"
                  disabled={selectedServices.length === 0 || !formData.name || !formData.email || !formData.projectTitle || !formData.projectDescription}
                >
                  Submit Detailed Quote Request
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
};

export default QuotePage;
