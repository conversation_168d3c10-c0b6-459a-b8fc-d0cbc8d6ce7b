import{c as Je,r as i,b as et,d as tt,e as ue,f as G,j as l,P as k,g as nt,h as _,i as ae,D as rt,k as at,l as ot,n as ct,o as it,R as V,p as F,X as st}from"./index-3d91a596.js";const Dn=Je("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var ut=tt[" useId ".trim().toString()]||(()=>{}),lt=0;function z(e){const[t,n]=i.useState(ut());return et(()=>{e||n(r=>r??String(lt++))},[e]),e||(t?`radix-${t}`:"")}var Z="focusScope.autoFocusOnMount",q="focusScope.autoFocusOnUnmount",le={bubbles:!1,cancelable:!0},dt="FocusScope",Ce=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:o,...u}=e,[c,h]=i.useState(null),g=ue(a),p=ue(o),f=i.useRef(null),m=G(t,s=>h(s)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let s=function(b){if(y.paused||!c)return;const E=b.target;c.contains(E)?f.current=E:D(f.current,{select:!0})},d=function(b){if(y.paused||!c)return;const E=b.relatedTarget;E!==null&&(c.contains(E)||D(f.current,{select:!0}))},v=function(b){if(document.activeElement===document.body)for(const C of b)C.removedNodes.length>0&&D(c)};document.addEventListener("focusin",s),document.addEventListener("focusout",d);const S=new MutationObserver(v);return c&&S.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",s),document.removeEventListener("focusout",d),S.disconnect()}}},[r,c,y.paused]),i.useEffect(()=>{if(c){fe.add(y);const s=document.activeElement;if(!c.contains(s)){const v=new CustomEvent(Z,le);c.addEventListener(Z,g),c.dispatchEvent(v),v.defaultPrevented||(ft(pt(Se(c)),{select:!0}),document.activeElement===s&&D(c))}return()=>{c.removeEventListener(Z,g),setTimeout(()=>{const v=new CustomEvent(q,le);c.addEventListener(q,p),c.dispatchEvent(v),v.defaultPrevented||D(s??document.body,{select:!0}),c.removeEventListener(q,p),fe.remove(y)},0)}}},[c,g,p,y]);const w=i.useCallback(s=>{if(!n&&!r||y.paused)return;const d=s.key==="Tab"&&!s.altKey&&!s.ctrlKey&&!s.metaKey,v=document.activeElement;if(d&&v){const S=s.currentTarget,[b,E]=vt(S);b&&E?!s.shiftKey&&v===E?(s.preventDefault(),n&&D(b,{select:!0})):s.shiftKey&&v===b&&(s.preventDefault(),n&&D(E,{select:!0})):v===S&&s.preventDefault()}},[n,r,y.paused]);return l.jsx(k.div,{tabIndex:-1,...u,ref:m,onKeyDown:w})});Ce.displayName=dt;function ft(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(D(r,{select:t}),document.activeElement!==n)return}function vt(e){const t=Se(e),n=de(t,e),r=de(t.reverse(),e);return[n,r]}function Se(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const a=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||a?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function de(e,t){for(const n of e)if(!gt(n,{upTo:t}))return n}function gt(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function mt(e){return e instanceof HTMLInputElement&&"select"in e}function D(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&mt(e)&&t&&e.select()}}var fe=ht();function ht(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ve(e,t),e.unshift(t)},remove(t){var n;e=ve(e,t),(n=e[0])==null||n.resume()}}}function ve(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function pt(e){return e.filter(t=>t.tagName!=="A")}var Q=0;function yt(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ge()),document.body.insertAdjacentElement("beforeend",e[1]??ge()),Q++,()=>{Q===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Q--}},[])}function ge(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var R=function(){return R=Object.assign||function(t){for(var n,r=1,a=arguments.length;r<a;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},R.apply(this,arguments)};function we(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n}function bt(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,o;r<a;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var H="right-scroll-bar-position",U="width-before-scroll-bar",Et="with-scroll-bars-hidden",Ct="--removed-body-scroll-bar-size";function J(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function St(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var a=n.value;a!==r&&(n.value=r,n.callback(r,a))}}}})[0];return n.callback=t,n.facade}var wt=typeof window<"u"?i.useLayoutEffect:i.useEffect,me=new WeakMap;function xt(e,t){var n=St(t||null,function(r){return e.forEach(function(a){return J(a,r)})});return wt(function(){var r=me.get(n);if(r){var a=new Set(r),o=new Set(e),u=n.current;a.forEach(function(c){o.has(c)||J(c,null)}),o.forEach(function(c){a.has(c)||J(c,u)})}me.set(n,e)},[e]),n}function Rt(e){return e}function Dt(e,t){t===void 0&&(t=Rt);var n=[],r=!1,a={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(o){var u=t(o,r);return n.push(u),function(){n=n.filter(function(c){return c!==u})}},assignSyncMedium:function(o){for(r=!0;n.length;){var u=n;n=[],u.forEach(o)}n={push:function(c){return o(c)},filter:function(){return n}}},assignMedium:function(o){r=!0;var u=[];if(n.length){var c=n;n=[],c.forEach(o),u=n}var h=function(){var p=u;u=[],p.forEach(o)},g=function(){return Promise.resolve().then(h)};g(),n={push:function(p){u.push(p),g()},filter:function(p){return u=u.filter(p),n}}}};return a}function Nt(e){e===void 0&&(e={});var t=Dt(null);return t.options=R({async:!0,ssr:!1},e),t}var xe=function(e){var t=e.sideCar,n=we(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,R({},n))};xe.isSideCarExport=!0;function At(e,t){return e.useMedium(t),xe}var Re=Nt(),ee=function(){},X=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:ee,onWheelCapture:ee,onTouchMoveCapture:ee}),a=r[0],o=r[1],u=e.forwardProps,c=e.children,h=e.className,g=e.removeScrollBar,p=e.enabled,f=e.shards,m=e.sideCar,y=e.noIsolation,w=e.inert,s=e.allowPinchZoom,d=e.as,v=d===void 0?"div":d,S=e.gapMode,b=we(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=m,C=xt([n,t]),A=R(R({},b),a);return i.createElement(i.Fragment,null,p&&i.createElement(E,{sideCar:Re,removeScrollBar:g,shards:f,noIsolation:y,inert:w,setCallbacks:o,allowPinchZoom:!!s,lockRef:n,gapMode:S}),u?i.cloneElement(i.Children.only(c),R(R({},A),{ref:C})):i.createElement(v,R({},A,{className:h,ref:C}),c))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};X.classNames={fullWidth:U,zeroRight:H};var he,Pt=function(){if(he)return he;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Tt(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Pt();return t&&e.setAttribute("nonce",t),e}function Ot(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function It(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var kt=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Tt())&&(Ot(t,n),It(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Mt=function(){var e=kt();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},De=function(){var e=Mt(),t=function(n){var r=n.styles,a=n.dynamic;return e(r,a),null};return t},_t={left:0,top:0,right:0,gap:0},te=function(e){return parseInt(e||"",10)||0},Ft=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],a=t[e==="padding"?"paddingRight":"marginRight"];return[te(n),te(r),te(a)]},jt=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return _t;var t=Ft(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Lt=De(),I="data-scroll-locked",Wt=function(e,t,n,r){var a=e.left,o=e.top,u=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Et,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(I,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(u,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(H,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(U,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(H," .").concat(H,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(U," .").concat(U,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(I,`] {
    `).concat(Ct,": ").concat(c,`px;
  }
`)},pe=function(){var e=parseInt(document.body.getAttribute(I)||"0",10);return isFinite(e)?e:0},Bt=function(){i.useEffect(function(){return document.body.setAttribute(I,(pe()+1).toString()),function(){var e=pe()-1;e<=0?document.body.removeAttribute(I):document.body.setAttribute(I,e.toString())}},[])},$t=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=r===void 0?"margin":r;Bt();var o=i.useMemo(function(){return jt(a)},[a]);return i.createElement(Lt,{styles:Wt(o,!t,a,n?"":"!important")})},re=!1;if(typeof window<"u")try{var L=Object.defineProperty({},"passive",{get:function(){return re=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch{re=!1}var P=re?{passive:!1}:!1,Ht=function(e){return e.tagName==="TEXTAREA"},Ne=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ht(e)&&n[t]==="visible")},Ut=function(e){return Ne(e,"overflowY")},Kt=function(e){return Ne(e,"overflowX")},ye=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var a=Ae(e,r);if(a){var o=Pe(e,r),u=o[1],c=o[2];if(u>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Gt=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Vt=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ae=function(e,t){return e==="v"?Ut(t):Kt(t)},Pe=function(e,t){return e==="v"?Gt(t):Vt(t)},Xt=function(e,t){return e==="h"&&t==="rtl"?-1:1},Yt=function(e,t,n,r,a){var o=Xt(e,window.getComputedStyle(t).direction),u=o*r,c=n.target,h=t.contains(c),g=!1,p=u>0,f=0,m=0;do{var y=Pe(e,c),w=y[0],s=y[1],d=y[2],v=s-d-o*w;(w||v)&&Ae(e,c)&&(f+=v,m+=w),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!h&&c!==document.body||h&&(t.contains(c)||t===c));return(p&&(a&&Math.abs(f)<1||!a&&u>f)||!p&&(a&&Math.abs(m)<1||!a&&-u>m))&&(g=!0),g},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},be=function(e){return[e.deltaX,e.deltaY]},Ee=function(e){return e&&"current"in e?e.current:e},zt=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Zt=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},qt=0,T=[];function Qt(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),a=i.useState(qt++)[0],o=i.useState(De)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var s=bt([e.lockRef.current],(e.shards||[]).map(Ee),!0).filter(Boolean);return s.forEach(function(d){return d.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),s.forEach(function(d){return d.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(s,d){if("touches"in s&&s.touches.length===2||s.type==="wheel"&&s.ctrlKey)return!u.current.allowPinchZoom;var v=W(s),S=n.current,b="deltaX"in s?s.deltaX:S[0]-v[0],E="deltaY"in s?s.deltaY:S[1]-v[1],C,A=s.target,M=Math.abs(b)>Math.abs(E)?"h":"v";if("touches"in s&&M==="h"&&A.type==="range")return!1;var j=ye(M,A);if(!j)return!0;if(j?C=M:(C=M==="v"?"h":"v",j=ye(M,A)),!j)return!1;if(!r.current&&"changedTouches"in s&&(b||E)&&(r.current=C),!C)return!0;var se=r.current||C;return Yt(se,d,s,se==="h"?b:E,!0)},[]),h=i.useCallback(function(s){var d=s;if(!(!T.length||T[T.length-1]!==o)){var v="deltaY"in d?be(d):W(d),S=t.current.filter(function(C){return C.name===d.type&&(C.target===d.target||d.target===C.shadowParent)&&zt(C.delta,v)})[0];if(S&&S.should){d.cancelable&&d.preventDefault();return}if(!S){var b=(u.current.shards||[]).map(Ee).filter(Boolean).filter(function(C){return C.contains(d.target)}),E=b.length>0?c(d,b[0]):!u.current.noIsolation;E&&d.cancelable&&d.preventDefault()}}},[]),g=i.useCallback(function(s,d,v,S){var b={name:s,delta:d,target:v,should:S,shadowParent:Jt(v)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(E){return E!==b})},1)},[]),p=i.useCallback(function(s){n.current=W(s),r.current=void 0},[]),f=i.useCallback(function(s){g(s.type,be(s),s.target,c(s,e.lockRef.current))},[]),m=i.useCallback(function(s){g(s.type,W(s),s.target,c(s,e.lockRef.current))},[]);i.useEffect(function(){return T.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",h,P),document.addEventListener("touchmove",h,P),document.addEventListener("touchstart",p,P),function(){T=T.filter(function(s){return s!==o}),document.removeEventListener("wheel",h,P),document.removeEventListener("touchmove",h,P),document.removeEventListener("touchstart",p,P)}},[]);var y=e.removeScrollBar,w=e.inert;return i.createElement(i.Fragment,null,w?i.createElement(o,{styles:Zt(a)}):null,y?i.createElement($t,{gapMode:e.gapMode}):null)}function Jt(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const en=At(Re,Qt);var Te=i.forwardRef(function(e,t){return i.createElement(X,R({},e,{ref:t,sideCar:en}))});Te.classNames=X.classNames;const tn=Te;var nn=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},O=new WeakMap,B=new WeakMap,$={},ne=0,Oe=function(e){return e&&(e.host||Oe(e.parentNode))},rn=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Oe(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},an=function(e,t,n,r){var a=rn(t,Array.isArray(e)?e:[e]);$[n]||($[n]=new WeakMap);var o=$[n],u=[],c=new Set,h=new Set(a),g=function(f){!f||c.has(f)||(c.add(f),g(f.parentNode))};a.forEach(g);var p=function(f){!f||h.has(f)||Array.prototype.forEach.call(f.children,function(m){if(c.has(m))p(m);else try{var y=m.getAttribute(r),w=y!==null&&y!=="false",s=(O.get(m)||0)+1,d=(o.get(m)||0)+1;O.set(m,s),o.set(m,d),u.push(m),s===1&&w&&B.set(m,!0),d===1&&m.setAttribute(n,"true"),w||m.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",m,v)}})};return p(t),c.clear(),ne++,function(){u.forEach(function(f){var m=O.get(f)-1,y=o.get(f)-1;O.set(f,m),o.set(f,y),m||(B.has(f)||f.removeAttribute(r),B.delete(f)),y||f.removeAttribute(n)}),ne--,ne||(O=new WeakMap,O=new WeakMap,B=new WeakMap,$={})}},on=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=t||nn(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live]"))),an(r,a,n,"aria-hidden")):function(){return null}},Y="Dialog",[Ie,Nn]=nt(Y),[cn,x]=Ie(Y),ke=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:o,modal:u=!0}=e,c=i.useRef(null),h=i.useRef(null),[g,p]=ot({prop:r,defaultProp:a??!1,onChange:o,caller:Y});return l.jsx(cn,{scope:t,triggerRef:c,contentRef:h,contentId:z(),titleId:z(),descriptionId:z(),open:g,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(f=>!f),[p]),modal:u,children:n})};ke.displayName=Y;var Me="DialogTrigger",_e=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=x(Me,n),o=G(t,a.triggerRef);return l.jsx(k.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":ie(a.open),...r,ref:o,onClick:_(e.onClick,a.onOpenToggle)})});_e.displayName=Me;var oe="DialogPortal",[sn,Fe]=Ie(oe,{forceMount:void 0}),je=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:a}=e,o=x(oe,t);return l.jsx(sn,{scope:t,forceMount:n,children:i.Children.map(r,u=>l.jsx(ae,{present:n||o.open,children:l.jsx(ct,{asChild:!0,container:a,children:u})}))})};je.displayName=oe;var K="DialogOverlay",Le=i.forwardRef((e,t)=>{const n=Fe(K,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=x(K,e.__scopeDialog);return o.modal?l.jsx(ae,{present:r||o.open,children:l.jsx(ln,{...a,ref:t})}):null});Le.displayName=K;var un=it("DialogOverlay.RemoveScroll"),ln=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=x(K,n);return l.jsx(tn,{as:un,allowPinchZoom:!0,shards:[a.contentRef],children:l.jsx(k.div,{"data-state":ie(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",We=i.forwardRef((e,t)=>{const n=Fe(N,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=x(N,e.__scopeDialog);return l.jsx(ae,{present:r||o.open,children:o.modal?l.jsx(dn,{...a,ref:t}):l.jsx(fn,{...a,ref:t})})});We.displayName=N;var dn=i.forwardRef((e,t)=>{const n=x(N,e.__scopeDialog),r=i.useRef(null),a=G(t,n.contentRef,r);return i.useEffect(()=>{const o=r.current;if(o)return on(o)},[]),l.jsx(Be,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:_(e.onCloseAutoFocus,o=>{var u;o.preventDefault(),(u=n.triggerRef.current)==null||u.focus()}),onPointerDownOutside:_(e.onPointerDownOutside,o=>{const u=o.detail.originalEvent,c=u.button===0&&u.ctrlKey===!0;(u.button===2||c)&&o.preventDefault()}),onFocusOutside:_(e.onFocusOutside,o=>o.preventDefault())})}),fn=i.forwardRef((e,t)=>{const n=x(N,e.__scopeDialog),r=i.useRef(!1),a=i.useRef(!1);return l.jsx(Be,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var u,c;(u=e.onCloseAutoFocus)==null||u.call(e,o),o.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),o.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:o=>{var h,g;(h=e.onInteractOutside)==null||h.call(e,o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const u=o.target;((g=n.triggerRef.current)==null?void 0:g.contains(u))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&a.current&&o.preventDefault()}})}),Be=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o,...u}=e,c=x(N,n),h=i.useRef(null),g=G(t,h);return yt(),l.jsxs(l.Fragment,{children:[l.jsx(Ce,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:l.jsx(rt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":ie(c.open),...u,ref:g,onDismiss:()=>c.onOpenChange(!1)})}),l.jsxs(l.Fragment,{children:[l.jsx(vn,{titleId:c.titleId}),l.jsx(mn,{contentRef:h,descriptionId:c.descriptionId})]})]})}),ce="DialogTitle",$e=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=x(ce,n);return l.jsx(k.h2,{id:a.titleId,...r,ref:t})});$e.displayName=ce;var He="DialogDescription",Ue=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=x(He,n);return l.jsx(k.p,{id:a.descriptionId,...r,ref:t})});Ue.displayName=He;var Ke="DialogClose",Ge=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=x(Ke,n);return l.jsx(k.button,{type:"button",...r,ref:t,onClick:_(e.onClick,()=>a.onOpenChange(!1))})});Ge.displayName=Ke;function ie(e){return e?"open":"closed"}var Ve="DialogTitleWarning",[An,Xe]=at(Ve,{contentName:N,titleName:ce,docsSlug:"dialog"}),vn=({titleId:e})=>{const t=Xe(Ve),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},gn="DialogDescriptionWarning",mn=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Xe(gn).contentName}}.`;return i.useEffect(()=>{var o;const a=(o=e.current)==null?void 0:o.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},hn=ke,pn=_e,yn=je,Ye=Le,ze=We,Ze=$e,qe=Ue,bn=Ge;const Pn=hn,Tn=pn,En=yn,Qe=V.forwardRef(({className:e,...t},n)=>l.jsx(Ye,{ref:n,className:F("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Qe.displayName=Ye.displayName;const Cn=V.forwardRef(({className:e,children:t,...n},r)=>l.jsxs(En,{children:[l.jsx(Qe,{}),l.jsxs(ze,{ref:r,className:F("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,l.jsxs(bn,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[l.jsx(st,{className:"h-4 w-4"}),l.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Cn.displayName=ze.displayName;const Sn=({className:e,...t})=>l.jsx("div",{className:F("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Sn.displayName="DialogHeader";const wn=V.forwardRef(({className:e,...t},n)=>l.jsx(Ze,{ref:n,className:F("text-lg font-semibold leading-none tracking-tight",e),...t}));wn.displayName=Ze.displayName;const xn=V.forwardRef(({className:e,...t},n)=>l.jsx(qe,{ref:n,className:F("text-sm text-muted-foreground",e),...t}));xn.displayName=qe.displayName;export{Dn as C,Pn as D,Tn as a,Cn as b,Sn as c,wn as d};
