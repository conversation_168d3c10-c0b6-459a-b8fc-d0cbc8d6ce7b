
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'; // Assuming card is created
import { Input } from '@/components/ui/input'; // Assuming input is created
import { Textarea } from '@/components/ui/textarea'; // Assuming textarea is created
import { Label } from '@/components/ui/label'; // Assuming label is created
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'; // Assuming dialog is created
import { ChevronLeft, ChevronRight, PlayCircle, MapPin, Users, Clock, Award, BarChart3, Globe, Leaf, ShieldAlert, Building, CloudRain, Droplet, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';

const heroSlides = [
  {
    title: "Pioneering Geospatial Solutions in Central Africa",
    subtitle: "Empowering sustainable development through innovative data analysis and insights.",
    image: "Modern cityscape with digital overlays representing data",
    alt: "Modern cityscape with digital overlays",
    buttonText: "Discover Our Services",
    buttonLink: "/services"
  },
  {
    title: "Climate-Smart Strategies for a Resilient Future",
    subtitle: "Leveraging geospatial technology to adapt and thrive in a changing climate.",
    image: "Lush agricultural landscape with data points",
    alt: "Agricultural landscape with data visualization",
    buttonText: "Learn About Climate Adaptation",
    buttonLink: "/services#climate-change-adaptation"
  },
  {
    title: "Data-Driven Insights for Natural Resource Management",
    subtitle: "Optimize resource utilization and environmental protection with advanced spatial analytics.",
    image: "Satellite view of a forest with highlighted conservation areas",
    alt: "Satellite view of forest conservation",
    buttonText: "Explore Resource Management",
    buttonLink: "/services#natural-resources-management"
  }
];

const topFeatures = [
  { title: "No Hidden Costs", description: "Transparent pricing with clear project scopes.", icon: <Award className="h-10 w-10 text-primary" /> },
  { title: "Dedicated Team", description: "Expert professionals committed to your success.", icon: <Users className="h-10 w-10 text-primary" /> },
  { title: "Timely Delivery", description: "Reliable and efficient project completion.", icon: <Clock className="h-10 w-10 text-primary" /> },
];

const services = [
  { name: "Climate-Smart Agriculture", icon: <Leaf className="h-8 w-8" />, description: "Revolutionizing agricultural practices with innovative technologies and sustainable approaches for improved productivity." },
  { name: "Natural Risk Assessments", icon: <ShieldAlert className="h-8 w-8" />, description: "Comprehensive risk analysis and management strategies for natural hazards and environmental challenges." },
  { name: "Spatial Data Analytics", icon: <BarChart3 className="h-8 w-8" />, description: "Advanced geospatial analysis and data interpretation for informed decision-making processes." },
  { name: "Urban Planning", icon: <Building className="h-8 w-8" />, description: "Sustainable urban development solutions with focus on smart city planning and infrastructure optimization." },
  { name: "Environmental Monitoring", icon: <Globe className="h-8 w-8" />, description: "Comprehensive environmental assessment and monitoring systems for sustainable resource management." },
  { name: "Natural Resources Management", icon: <MapPin className="h-8 w-8" />, description: "Strategic planning and sustainable management of natural resources for long-term conservation." },
  { name: "Climate Change Adaptation", icon: <CloudRain className="h-8 w-8" />, description: "Innovative strategies and solutions for climate change mitigation and adaptation planning." },
  { name: "Water Resources Management", icon: <Droplet className="h-8 w-8" />, description: "Integrated water resource management solutions for sustainable water utilization and conservation." },
  { name: "Disaster Risk Management", icon: <AlertTriangle className="h-8 w-8" />, description: "Comprehensive disaster risk assessment and management strategies for community resilience." },
];

const HomePage = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % heroSlides.length);
    }, 7000); // Change slide every 7 seconds
    return () => clearTimeout(timer);
  }, [currentSlide]);

  const nextSlide = () => setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  const prevSlide = () => setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);

  const handleQuoteSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    console.log("Quote Form Data:", data);
    // Save to localStorage
    try {
      const existingQuotes = JSON.parse(localStorage.getItem('quoteRequests')) || [];
      localStorage.setItem('quoteRequests', JSON.stringify([...existingQuotes, data]));
      toast({
        title: "Quote Request Sent!",
        description: "Thank you! We'll be in touch soon.",
        variant: "default",
      });
      e.target.reset();
    } catch (error) {
      console.error("Failed to save quote request:", error);
      toast({
        title: "Error",
        description: "Could not save your quote request. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  const handleNewsletterSubmit = (e) => {
    e.preventDefault();
    const email = e.target.email.value;
    console.log("Newsletter Subscription:", email);
     try {
      const existingSubscribers = JSON.parse(localStorage.getItem('newsletterSubscribers')) || [];
      localStorage.setItem('newsletterSubscribers', JSON.stringify([...existingSubscribers, { email, date: new Date().toISOString() }]));
      toast({
        title: "Subscribed!",
        description: "Thanks for joining our newsletter.",
      });
      e.target.reset();
    } catch (error) {
      console.error("Failed to save newsletter subscription:", error);
      toast({
        title: "Error",
        description: "Could not subscribe. Please try again.",
        variant: "destructive",
      });
    }
  };


  return (
    <div className="animate-fade-in">
      {/* Hero Carousel Section */}
      <section className="relative h-[calc(100vh-5rem)] min-h-[500px] max-h-[700px] w-full overflow-hidden">
        {heroSlides.map((slide, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: index === currentSlide ? 0 : (index > currentSlide ? '100%' : '-100%') }}
            animate={{ opacity: index === currentSlide ? 1 : 0, x: 0 }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
            className="absolute inset-0 w-full h-full"
          >
            <img  src={slide.image} alt={slide.alt} className="w-full h-full object-cover" src="https://images.unsplash.com/photo-1691527385266-62295bbcabb1" />
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="text-center text-white p-4 md:p-8 max-w-3xl">
                <motion.h1 
                  className="text-4xl md:text-5xl lg:text-6xl font-jost font-bold mb-4"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                >
                  {slide.title}
                </motion.h1>
                <motion.p 
                  className="text-lg md:text-xl mb-8 font-sans"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                >
                  {slide.subtitle}
                </motion.p>
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                >
                  <Button size="lg" asChild className="bg-primary hover:bg-primary/90 text-primary-foreground font-jost text-lg px-8 py-3">
                    <Link to={slide.buttonLink}>{slide.buttonText}</Link>
                  </Button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        ))}
        <Button onClick={prevSlide} variant="outline" size="icon" className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/40 border-none text-white">
          <ChevronLeft className="h-6 w-6" />
        </Button>
        <Button onClick={nextSlide} variant="outline" size="icon" className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/40 border-none text-white">
          <ChevronRight className="h-6 w-6" />
        </Button>
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`h-3 w-3 rounded-full transition-colors ${currentSlide === index ? 'bg-primary' : 'bg-white/50 hover:bg-white/80'}`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </section>

      {/* Top Features Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            {topFeatures.map((feature, index) => (
              <motion.div 
                key={feature.title}
                className="p-6 bg-background rounded-lg shadow-lg"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex justify-center mb-4">{feature.icon}</div>
                <h3 className="text-xl font-jost font-semibold mb-2 text-primary">{feature.title}</h3>
                <p className="text-foreground/80 font-sans">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section with Video Modal */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-6">About GEOSTRATDRC</h2>
              <p className="text-lg text-foreground/90 mb-4 font-sans">
                GEOSTRATDRC is a premier geospatial innovation firm based in the Democratic Republic of Congo, specializing in transforming complex data into actionable insights. We are dedicated to fostering sustainable development across Central Africa.
              </p>
              <p className="text-foreground/80 mb-6 font-sans">
                Our expertise spans climate-smart agriculture, natural risk assessments, urban planning, and much more. We leverage cutting-edge technology and a passionate team to deliver solutions that address critical environmental and societal challenges.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" asChild className="font-jost">
                  <Link to="/about">Learn More About Us</Link>
                </Button>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button size="lg" variant="outline" className="font-jost border-primary text-primary hover:bg-primary/10 hover:text-primary">
                      <PlayCircle className="mr-2 h-5 w-5" /> Watch Our Story
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[625px] p-0">
                    <DialogHeader className="p-6 pb-0">
                      <DialogTitle className="font-jost text-2xl">Discover GEOSTRATDRC</DialogTitle>
                    </DialogHeader>
                    <div className="aspect-video">
                       <iframe 
                          className="w-full h-full" 
                          src="https://www.youtube.com/embed/dQw4w9WgXcQ" /* Replace with actual video */
                          title="YouTube video player" 
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                          allowFullScreen>
                       </iframe>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </motion.div>
            <motion.div
              className="relative group"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <img  
                src="team_working_on_geospatial_data.jpg" 
                alt="Team working collaboratively on geospatial data analysis" 
                className="rounded-lg shadow-xl w-full h-auto object-cover"
               src="https://images.unsplash.com/photo-1688413399498-e35ed74b554f" />
              <div className="absolute inset-0 bg-primary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Grid Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-primary/5 via-teal-500/5 to-secondary/5">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Core Services</h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto font-sans">
              We offer a comprehensive suite of geospatial services designed to meet the diverse needs of our clients and drive impactful change.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.name}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-xl transition-shadow duration-300 group bg-background">
                  <CardHeader className="flex flex-row items-center gap-4 pb-2">
                    <div className="p-3 rounded-full bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors duration-300">
                      {React.cloneElement(service.icon, { className: "h-8 w-8" })}
                    </div>
                    <CardTitle className="font-jost text-xl text-primary group-hover:text-primary/90">{service.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-foreground/80 font-sans mb-4">{service.description}</CardDescription>
                    <Button variant="link" asChild className="p-0 h-auto text-primary font-semibold group-hover:underline">
                      <Link to={`/services#${service.name.toLowerCase().replace(/\s+/g, '-')}`}>Learn More &rarr;</Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" asChild className="font-jost">
              <Link to="/services">Explore All Services</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Free Quote Form Section */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Ready to Start Your Project?</h2>
              <p className="text-lg text-foreground/80 mb-6 font-sans">
                Let's discuss how GEOSTRATDRC can help you achieve your goals. Fill out the form for a free, no-obligation quote.
              </p>
              <img  
                src="geospatial_analysis_infographic.jpg" 
                alt="Infographic showing geospatial data analysis process" 
                className="rounded-lg shadow-lg w-full h-auto object-cover max-h-96"
               src="https://images.unsplash.com/photo-1686061593213-98dad7c599b9" />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="p-6 sm:p-8 shadow-xl bg-background">
                <CardHeader className="p-0 mb-6">
                  <CardTitle className="font-jost text-2xl text-primary">Request a Free Quote</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <form onSubmit={handleQuoteSubmit} className="space-y-6">
                    <div className="grid sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name" className="font-sans">Full Name</Label>
                        <Input id="name" name="name" type="text" placeholder="John Doe" required className="mt-1 font-sans" />
                      </div>
                      <div>
                        <Label htmlFor="email" className="font-sans">Email Address</Label>
                        <Input id="email" name="email" type="email" placeholder="<EMAIL>" required className="mt-1 font-sans" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="phone" className="font-sans">Phone Number (Optional)</Label>
                      <Input id="phone" name="phone" type="tel" placeholder="+243 XXX XXX XXX" className="mt-1 font-sans" />
                    </div>
                    <div>
                      <Label htmlFor="service" className="font-sans">Service of Interest</Label>
                      <select 
                        id="service" 
                        name="service" 
                        required 
                        className="mt-1 block w-full rounded-md border-input bg-background px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-sans"
                      >
                        <option value="">Select a service</option>
                        {services.map(s => <option key={s.name} value={s.name}>{s.name}</option>)}
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="message" className="font-sans">Project Details</Label>
                      <Textarea id="message" name="message" placeholder="Briefly describe your project requirements..." required rows={4} className="mt-1 font-sans" />
                    </div>
                    <Button type="submit" size="lg" className="w-full font-jost">Submit Request</Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
      
      {/* Projects Portfolio (Placeholder) */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Our Impactful Projects</h2>
          <p className="text-lg text-foreground/80 max-w-2xl mx-auto mb-12 font-sans">
            Discover how we've helped organizations like yours achieve remarkable results through innovative geospatial solutions.
          </p>
          {/* Placeholder for project items and filtering */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1,2,3].map(p => (
              <Card key={p} className="text-left overflow-hidden group">
                <img  src={`project_thumbnail_${p}.jpg`} alt={`Project ${p} Thumbnail`} className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300" src="https://images.unsplash.com/photo-1572177812156-58036aae439c" />
                <CardContent className="p-6">
                  <CardTitle className="font-jost text-xl mb-2 text-primary">Project Title {p}</CardTitle>
                  <CardDescription className="font-sans mb-4">A brief description of this amazing project and its outcomes.</CardDescription>
                  <Button variant="link" asChild className="p-0 h-auto text-primary font-semibold">
                    <Link to={`/projects/project-${p}`}>View Case Study &rarr;</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-12">
            <Button size="lg" asChild className="font-jost">
              <Link to="/projects">Explore All Projects</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Team Section (Placeholder) */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">Meet Our Experts</h2>
          <p className="text-lg text-foreground/80 max-w-2xl mx-auto mb-12 font-sans">
            Our team of passionate professionals is the driving force behind our success and your innovative solutions.
          </p>
          {/* Featured team members */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {/* CEO */}
            <Card className="text-center p-6">
              <img src="/team/padona-matabaro-jason.jpg" alt="PADONA MATABARO Jason, CEO" className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-2 border-primary/20" />
              <CardTitle className="font-jost text-lg mb-1 text-primary">PADONA MATABARO Jason</CardTitle>
              <CardDescription className="font-sans text-sm text-secondary mb-2">CEO & Senior Geospatial Data Analyst</CardDescription>
              <Button variant="outline" size="sm" asChild className="font-jost text-xs">
                <Link to="/team#padona-matabaro-jason">View Profile</Link>
              </Button>
            </Card>

            {/* CTO */}
            <Card className="text-center p-6">
              <img src="/team/ushindi-gabriel.jpg" alt="Ushindi Gabriel, CTO" className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-2 border-primary/20" />
              <CardTitle className="font-jost text-lg mb-1 text-primary">Ushindi Gabriel</CardTitle>
              <CardDescription className="font-sans text-sm text-secondary mb-2">Chief Technology Officer</CardDescription>
              <Button variant="outline" size="sm" asChild className="font-jost text-xs">
                <Link to="/team#ushindi-gabriel">View Profile</Link>
              </Button>
            </Card>

            {/* HR Manager */}
            <Card className="text-center p-6">
              <img src="/team/gaelle-baraka.jpg" alt="Gaelle Baraka, HR Manager" className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-2 border-primary/20" />
              <CardTitle className="font-jost text-lg mb-1 text-primary">Gaelle Baraka</CardTitle>
              <CardDescription className="font-sans text-sm text-secondary mb-2">Human Resource Manager</CardDescription>
              <Button variant="outline" size="sm" asChild className="font-jost text-xs">
                <Link to="/team#gaelle-baraka">View Profile</Link>
              </Button>
            </Card>

            {/* Senior Risk Assessment Expert */}
            <Card className="text-center p-6">
              <img src="/team/moise-raphael.jpg" alt="Moise Raphael, Senior Risk Assessment Expert" className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-2 border-primary/20" />
              <CardTitle className="font-jost text-lg mb-1 text-primary">Moise Raphael</CardTitle>
              <CardDescription className="font-sans text-sm text-secondary mb-2">Senior Risk Assessment Expert</CardDescription>
              <Button variant="outline" size="sm" asChild className="font-jost text-xs">
                <Link to="/team#moise-raphael">View Profile</Link>
              </Button>
            </Card>
          </div>
           <div className="mt-12">
            <Button size="lg" asChild className="font-jost">
              <Link to="/team">Meet The Full Team</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Carousel (Placeholder) */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold text-primary mb-4">What Our Clients Say</h2>
          <p className="text-lg text-foreground/80 max-w-2xl mx-auto mb-12 font-sans">
            We pride ourselves on building strong partnerships and delivering exceptional value.
          </p>
          {/* Placeholder for testimonials carousel */}
          <Card className="p-8 max-w-3xl mx-auto bg-gradient-to-r from-primary/80 via-teal-600/80 to-secondary/80 text-primary-foreground shadow-xl">
            <CardContent className="text-center">
              <img  src="client_logo_placeholder.png" alt="Client Logo" className="h-12 mx-auto mb-6 filter brightness-0 invert opacity-80" src="https://images.unsplash.com/photo-1485531865381-286666aa80a9" />
              <p className="text-xl italic mb-6 font-sans">"GEOSTRATDRC's expertise was instrumental in our project's success. Their insights and dedication were truly remarkable."</p>
              <p className="font-jost font-semibold text-lg">Client Name</p>
              <p className="text-sm opacity-80">CEO, Example Company</p>
            </CardContent>
          </Card>
          <div className="mt-12">
            <Button size="lg" asChild className="font-jost">
              <Link to="/testimonials">Read More Testimonials</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section - Simplified as it's in footer too */}
      <section className="py-16 lg:py-24 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-jost font-bold mb-4">Stay Ahead with Geospatial Insights</h2>
          <p className="text-lg opacity-90 max-w-xl mx-auto mb-8 font-sans">
            Subscribe to our newsletter for the latest industry news, expert analysis, and company updates from GEOSTRATDRC.
          </p>
          <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto flex flex-col sm:flex-row gap-3">
            <Input 
              type="email" 
              name="email"
              placeholder="<EMAIL>" 
              required 
              className="bg-white/20 border-white/30 placeholder-white/70 text-white focus:bg-white/30 focus:ring-white/50 flex-grow font-sans"
            />
            <Button type="submit" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary font-jost">
              Subscribe Now
            </Button>
          </form>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
